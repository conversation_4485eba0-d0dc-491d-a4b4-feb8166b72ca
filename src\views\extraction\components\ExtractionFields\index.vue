<script setup lang="ts">
import { modelExtractField, modelExtractPerformance } from '@/services/extraction'
import type {
  IExtractField,
  IExtractFieldsList,
  IModelExtractFieldParams,
  IExtractFieldResponse,
  IExtractPerformanceResponse,
  IExtractPerformanceList,
  IExtractPerformance,
} from '@/services/extraction'
import BeforeExtraction from './components/BeforeExtraction.vue'
import AfterExtraction from './components/AfterExtraction.vue'
import AfterFulfillment from './components/AfterFulfillment.vue'

import { contractRefKey } from '../../types'
import type { ExtractionCantract } from '../../types'

import { $t } from '@/utils/i18n'
const contractInfo = inject<Ref<ExtractionCantract>>('contractInfo')

const contractRef = inject(contractRefKey)
if (!contractRef) throw new Error($t('contractRef 未提供'))

interface IExtractForm {
  extractFields: IExtractField[]
}

const extractForm = reactive<IExtractForm>({
  extractFields: [
    { isHovered: false, label: $t('合同编号'), value: '' },
    { isHovered: false, label: $t('合同名称'), value: '' },
    { isHovered: false, label: $t('签订日期'), value: '' },
    { isHovered: false, label: $t('签订地点'), value: '' },
    { isHovered: false, label: $t('甲方'), value: '' },
    { isHovered: false, label: $t('乙方'), value: '' },
    { isHovered: false, label: $t('付款方式'), value: '' },
    { isHovered: false, label: $t('付款时间'), value: '' },
    { isHovered: false, label: $t('交付时间'), value: '' },
    { isHovered: false, label: $t('交付地点'), value: '' },
    { isHovered: false, label: $t('交付物品'), value: '' },
    { isHovered: false, label: $t('违约责任'), value: '' },
    { isHovered: false, label: $t('争议解决方式'), value: '' },
    { isHovered: false, label: $t('管辖法院'), value: '' },
  ],
})
const fulfillmentExtractForm = reactive<IExtractForm>({
  extractFields: [
    { isHovered: false, label: $t('履约类型'), value: '' },
    { isHovered: false, label: $t('履约标的物'), value: '' },
    { isHovered: false, label: $t('履约事件标准名称'), value: '' },
    { isHovered: false, label: $t('责任自然人姓名'), value: '' },
    { isHovered: false, label: $t('具体履约截止日期'), value: '' },
    { isHovered: false, label: $t('履约金额'), value: '' },
    { isHovered: false, label: $t('特殊履约条件'), value: '' },
  ],
})
const fulfillmentExtractList = ref<IExtractPerformance[][]>([]) // 用来存放抽取到的履约信息
const fieldList = ref<string[]>([])
const fulfillmentList = ref<string[]>([])
const loading = ref(false)
const isExtracted = ref(false)
const isFulfillmented = ref(false)
const tabActive = ref('left')

const hanldeChangeTab = (val: string) => {
  tabActive.value = val
}

// 新增输入框
const addField = () => {
  if (tabActive.value === 'left') {
    extractForm.extractFields.push({ isHovered: false, label: '', value: '' })
  } else if (tabActive.value === 'right') {
    fulfillmentExtractForm.extractFields.push({ isHovered: false, label: '', value: '' })
  }
}

// 移除输入框
const removeFields = (index: number) => {
  if (index !== -1) {
    if (tabActive.value === 'left') {
      extractForm.extractFields.splice(index, 1)
    } else if (tabActive.value === 'right') {
      fulfillmentExtractForm.extractFields.splice(index, 1)
    }
  }
}

// 基本字段抽取
const updateExtractFields = (extractFields: IExtractField[], result: IExtractFieldResponse[]): IExtractField[] => {
  // 1. 创建一个 Map，快速通过 label 找到原始字段
  const fieldMap = new Map<string, IExtractField>()
  extractFields.forEach((field) => {
    fieldMap.set(field.label, field)
  })
  // 2. 对 result 按 order 升序排序
  const sortedResult = [...result].sort((a, b) => (a.order ?? 0) - (b.order ?? 0))
  // 3. 构造新的字段数组
  const updatedFields: IExtractField[] = sortedResult.map((item) => {
    const originalField = fieldMap.get(item.extractKeyWord)!
    return {
      ...originalField,
      value: item.extractValue ?? '',
      isHovered: false, // 重置 hover 状态
    }
  })
  return updatedFields
}
const startExtraction = async () => {
  if (extractForm.extractFields.length === 0) {
    ElMessage.error($t('抽取字段不能为空！'))
    return
  }
  loading.value = true
  fieldList.value = extractForm.extractFields.map((item) => item.label)
  const params: IModelExtractFieldParams = {
    fileCode: contractInfo!.value.contractUrl,
    contractName: contractInfo!.value.contractName,
    extractKeyWords: fieldList.value,
  }
  try {
    const {
      data: { result },
    } = await modelExtractField<IExtractFieldsList>(params)
    if (!result) {
      ElMessage.warning($t('未抽取到有效信息，请检查抽取字段或文件类型是否正确！'))
      return
    }
    console.log('基础字段抽取结果', result)
    const updatedExtractFields = updateExtractFields(extractForm.extractFields, result)
    extractForm.extractFields = updatedExtractFields
    isExtracted.value = true
  } catch (error) {
    console.warn(error)
  } finally {
    loading.value = false
  }
}

// 履约字段抽取
// : IExtractPerformanceResponse[]

// interface IExtractField11 {
//   extractKeyWord: string // 字段标识（如"合同编号"）
//   extractValue?: string | null // 字段值（可选）
//   extractValueList?: string[] | null // 字段值数组（可选）
//   locationText?: string | null // 文本位置信息（可选）
//   order: number // 排序序号
// }

const transformExtractFields = (data: IExtractPerformanceResponse[][]): IExtractPerformance[][] =>
  data.map((group) => {
    const sortedGroup = group.slice().sort((a, b) => a.order - b.order)
    return sortedGroup.map(({ extractKeyWord, extractValue, extractValueList, locationText }) => {
      const transformedItem: IExtractPerformance = {
        label: extractKeyWord,
        value: extractValue ? extractValue : $t('无'),
        locationText: locationText ? locationText : '',
      }
      if (extractValueList) {
        transformedItem.extractValueList = extractValueList
      }
      if (extractKeyWord === $t('责任自然人姓名') && Array.isArray(extractValueList)) {
        transformedItem.value = extractValueList.length > 1 ? extractValueList.join(',') : extractValueList[0]
      }
      return transformedItem
    })
  })

const startExtractionFulfillment = async () => {
  if (fulfillmentExtractForm.extractFields.length === 0) {
    ElMessage.error($t('抽取字段不能为空！'))
    return
  }
  loading.value = true
  fulfillmentList.value = fulfillmentExtractForm.extractFields.map((item) => item.label)

  const params = {
    fileCode: contractInfo!.value.contractUrl,
    contractName: contractInfo!.value.contractName,
    extractKeyWords: fulfillmentList.value,
  }
  try {
    const {
      data: { result },
    } = await modelExtractPerformance<IExtractPerformanceList>(params)
    if (!result) {
      ElMessage.warning($t('未抽取到有效信息，请检查抽取字段或文件类型是否正确！'))
      return
    }
    fulfillmentExtractList.value = transformExtractFields(result)
    isFulfillmented.value = true
  } catch (error) {
    console.warn(error)
  } finally {
    loading.value = false
  }
}

// 重新抽取
const redraw = (val: string) => {
  if (val === 'left') {
    isExtracted.value = false
  } else if (val === 'right') {
    isFulfillmented.value = false
  }
}

// 原文定位
const searchPosition = async (content: string) => {
  if (!content.trim() || content.trim() == $t('无')) {
    ElMessage.error($t('暂无原文内容！'))
    return
  }
  if (!contractInfo!.value.isDocFile) {
    // pdf 文件定位逻辑
    const res = await contractRef!.value.findTextNext(content)
    if (!res) {
      ElMessage.warning($t('未找到对应文本！'))
    }
  } else {
    // wps 文件定位逻辑
    let text = content
    let status: number
    let flag: boolean
    flag = await contractRef.value.locationByText(text)
    if (flag) return

    text = text.replace(/[，；。！]$/g, '')
    flag = await contractRef.value.locationByText(text)
    if (flag) return
    status = 1

    const regList = ['\n']
    let findList: string[] = []

    for (let i = 0; i < regList.length; i++) {
      const reg = new RegExp(regList[i], 'g')
      if (reg.test(text)) {
        findList = text.split(regList[i])
        break
      }
    }

    for (const item of findList) {
      flag = await contractRef.value.locationByText(item)
      if (flag) {
        status = 2
        break
      }
    }
    if (status === 1) {
      ElMessage.warning($t('未能定位到原文'))
    } else {
      ElMessage.warning($t('定位到部分原文'))
    }
  }
}
</script>

<template>
  <div v-loading="loading" element-loading-:text="$t('正在抽取中…')" class="extract-wrap">
    <div class="extract-inner">
      <div class="extract-title">
        <span class="extract-list-name" @click="hanldeChangeTab('left')">
          <span :class="{ 'tab-active': tabActive === 'left' }">{{ $t('基本字段抽取') }}</span>
        </span>
        <span class="extract-list-name" @click="hanldeChangeTab('right')">
          <span :class="{ 'tab-active': tabActive === 'right' }">{{ $t('履约信息抽取') }}</span>
        </span>
      </div>
      <!-- 基本字段抽取 -->
      <div v-show="tabActive === 'left'" class="base-extract">
        <BeforeExtraction
          v-if="!isExtracted"
          :extract-form="extractForm"
          :extract-list="extractForm.extractFields"
          :tab-active="tabActive"
          @addField="addField"
          @removeFields="removeFields"
          @startExtraction="startExtraction"
        ></BeforeExtraction>
        <AfterExtraction
          v-else
          :extract-list="extractForm.extractFields"
          @searchPosition="searchPosition"
          @redraw="redraw"
        ></AfterExtraction>
      </div>
      <!-- 履约信息抽取 -->
      <div v-show="tabActive === 'right'" class="fulfillment">
        <BeforeExtraction
          v-if="!isFulfillmented"
          :extract-form="fulfillmentExtractForm"
          :extract-list="fulfillmentExtractForm.extractFields"
          :tab-active="tabActive"
          @addField="addField"
          @removeFields="removeFields"
          @startExtractionFulfillment="startExtractionFulfillment"
        ></BeforeExtraction>
        <AfterFulfillment
          v-else
          :extract-list="fulfillmentExtractList"
          @searchPosition="searchPosition"
          @redraw="redraw"
        ></AfterFulfillment>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.extract-wrap {
  position: relative;
  display: flex;
  justify-content: center;
  width: 404px;
  min-width: 300px;
  height: 100%;
  padding-bottom: 43px;
  background-color: #fff;
  border-left: 1px solid #eaebf0;
  .extract-inner {
    display: flex;
    flex-direction: column;
    gap: 16px;
    width: 100%;
    height: 100%;
    .extract-title {
      display: flex;
      align-items: center;
      height: 56px;
      padding-left: 24px;
      line-height: 56px;
      border-bottom: 1px solid #e9e9e9;
      .extract-list-name {
        position: relative;
        width: 30%;
        font-size: 16px;
        font-weight: 600;
        text-align: center;
        &:hover {
          color: var(--main-font);
        }
        span {
          padding-bottom: 16px;
          cursor: pointer;
          user-select: none;
        }
        .tab-active {
          font-weight: 600;
          color: var(--main-font);
          pointer-events: none;
          border-bottom: 2px solid var(--main-font);
        }
      }
    }
    .base-extract,
    .fulfillment {
      height: calc(100% - 56px);
    }
  }
}
</style>
