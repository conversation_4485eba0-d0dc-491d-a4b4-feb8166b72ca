import { defineComponent, computed, type PropType, h } from 'vue'
import { EventTypeEnum } from '@/stores/modules/chat/helper'
import MarkdownIt from 'markdown-it'

import { $t } from '@/utils/i18n'
const markDown = new MarkdownIt({
  html: false,
  breaks: true,
  linkify: true,
  typographer: false,
})
markDown.renderer.rules.link_open = (tokens: any, idx: any, options: any, _evn: any, self: any) => {
  return self.renderToken(tokens, idx, options)
}

export const parseResponseText = (contentText: any) => {
  const content = typeof contentText == 'string' ? JSON.parse(contentText) : []
  const chatAgent = content.find((res: any) => res.event_type == EventTypeEnum.CHATAGENT) as any
  let resultContent = ''
  if (chatAgent) {
    resultContent = chatAgent.outputs.text
  }
  return resultContent
}

export const handleChatContent = (contentText: string) => {
  if (!contentText) {
    return $t('暂无内容')
  }
  const text = markDown.render(contentText)
  const outerRegex = /\^([^]*?)\^/g
  const result = text.replace(outerRegex, (_match: any, contentInsideCaret: any) => {
    const innerResult = contentInsideCaret.replace(/\[([^\]]+)\]/g, (_: any, inside: any) => {
      const strs = inside.split('/')
      if (strs.length == 3) {
        return `<a id="${inside}" class="quote-href">${strs[2]}</a>`
      }
      return `<span id="${inside}" class="quote-marks"></span>`
    })
    return `${innerResult}`
  })
  return result
}

export const OutputContent = defineComponent({
  name: 'OutputContent',
  props: {
    content: {
      type: String,
      required: true,
    },
    onHandlerClick: {
      type: Function as PropType<(id: string) => void>,
      required: false,
    },
    onCallback: {
      type: Function as PropType<(data: { editText: string; cleanText: string }) => void>,
      required: false,
    },
  },
  setup(props) {
    const bindEvt = (event: MouseEvent) => {
      if (!event.target) return
      const target = event.target as HTMLElement
      if (target.tagName === 'SPAN' && target.classList.contains('quote-marks')) {
        event.preventDefault()
        props.onHandlerClick && props.onHandlerClick(target.id)
      } else if (target.tagName == 'A') {
        const anchorTarget = target as HTMLAnchorElement
        if (!target.classList.contains('quote-href')) {
          event.preventDefault()
          window.open(anchorTarget.href)
        } else {
          event.preventDefault()
          props.onHandlerClick && props.onHandlerClick(target.id)
        }
      }
    }

    const processedContent = computed(() => {
      if (!props.content) {
        return ''
      }

      const text = markDown.render(props.content)
      const thinkRegex = /&lt;think(?:\s+.*?)?&gt;[\s\S]*?&lt;\/think&gt;/g
      const outerRegex = /\^([^]*?)\^/g
      const tempText = text.replace(thinkRegex, '')

      const result = tempText.replace(outerRegex, (_match: string, contentInsideCaret: string): string => {
        const innerResult = contentInsideCaret.replace(/\[([^\]]+)\]/g, (_: string, inside: string): string => {
          const strs: string[] = inside.split('/')
          if (strs.length == 3) {
            return `${strs[2]}<span id="${inside}" class="quote-marks"></span>`
          }
          return `<span id="${inside}" class="quote-marks"></span>`
        })
        return `${innerResult}`
      })

      const cleanText = result.replace(/<[^>]*>/g, '').replace(/\^(\[[^\]]+\])+\^/g, '')
      props.onCallback && props.onCallback({ editText: result, cleanText })

      return result
    })

    return () => {
      if (!props.content) {
        return h('div')
      }

      return h('div', {
        innerHTML: processedContent.value,
        onClick: bindEvt,
      })
    }
  },
})
