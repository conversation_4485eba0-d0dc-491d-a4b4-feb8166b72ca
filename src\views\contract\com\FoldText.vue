<script lang="ts" setup>
const props = defineProps({
  text: {
    type: String,
    default: '',
  },
})

const allPadding = 76
const line = 45
const foldIntro = ref(true)
const isShow = ref(false)
const textContainer = ref()
const hiddenText = ref()
const yanwenRef = ref()
const height = ref(0)
function handleFoldIntro() {
  foldIntro.value = !foldIntro.value
}

function getNodeCSS(el: Element, key: string) {
  const computedStyle = window.getComputedStyle(el) as any
  return computedStyle[key]
}

function setNodeCSS(el: HTMLDivElement, key: string, value: string) {
  ;(el.style as any)[key] = value
}

function getHeightWithTxt(html: string, width = 240) {
  const div = document.createElement('div')
  setNodeCSS(div, 'fontSize', '12px')
  setNodeCSS(div, 'wordBreak', 'break-all')
  setNodeCSS(div, 'position', 'absolute')
  setNodeCSS(div, 'whiteSpace', 'pre-wrap')
  setNodeCSS(div, 'letterSpacing', '1px')
  setNodeCSS(div, 'lineHeight', '22px')
  setNodeCSS(div, 'left', '-1000px')
  setNodeCSS(div, 'top', '0px')
  setNodeCSS(div, 'width', `${width}px`)
  document.body.append(div)
  div.innerHTML = html
  const _h = getNodeCSS(div, 'height').replace(/px/gi, '')
  document.body.removeChild(div)
  return Number(_h)
}

function updateFoldState() {
  if (textContainer.value && hiddenText.value) {
    const containerStyles = getComputedStyle(textContainer.value)
    const lineHeight = parseFloat(containerStyles.lineHeight)
    const maxHeight = lineHeight * 2
    const hiddenText1 = document.querySelector('.result-inner')
    if (hiddenText1) {
      const realHeight = getHeightWithTxt(hiddenText.value.innerHTML, hiddenText1!.clientWidth - allPadding)
      // console.log(realHeight);
      height.value = realHeight
      isShow.value = realHeight > line ? true : false

      foldIntro.value = isShow.value
    }
  }
}

onMounted(() => {
  updateFoldState()
})
watch(
  () => props.text,
  () => {
    nextTick(() => {
      updateFoldState()
    })
  },
  {
    immediate: true,
  },
)
</script>
<template>
  <div ref="yanwenRef" class="yuanwen">
    <div ref="textContainer" :class="foldIntro ? 'fold-text' : 'unfold-text'">
      <span v-if="isShow && foldIntro" class="intro-unfold-btn" @click.stop="handleFoldIntro">
        <span class="point">…</span>{{ $t('更多') }}
      </span>
      <span class="intro-txt">{{ text ? $t('原文') : $t('条款缺失') }}</span
      >{{ text }}
      <span v-if="isShow && !foldIntro" class="intro-fold-btn" @click.stop="handleFoldIntro">{{ $t('收起') }}</span>
    </div>
    <div ref="hiddenText" class="hidden-text">
      <span
        class="intro-txt"
        style="
          padding: 2px 8px;
          margin-right: 4px;
          font-size: 12px;
          background-color: var(--input-bg);
          border-radius: 4px;
        "
      >
        {{ $t('原文') }} </span
      >{{ text }}
    </div>
  </div>
</template>

<style lang="scss" scoped>
.yuanwen {
  position: relative;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.375rem;
  color: var(--is-color-7d7b89);
  border-left: 2px solid var(--is-color-d0d1dc);
}
.intro-txt {
  padding: 3px 6px;
  margin-right: 4px;
  font-size: 0.875rem;
  background-color: var(--input-bg);
  border-radius: 4px;
}

// 折叠样式
.fold-text {
  position: relative;
  display: -webkit-box;
  padding-left: 0.625rem;
  overflow: hidden;
  -webkit-line-clamp: 2;
  word-wrap: break-word;
  -webkit-box-orient: vertical;
  &::before {
    float: right;
    width: 0;
    height: 100%;
    margin-bottom: -1.25rem; /** 和主体行高一样 **/
    content: '';
  }
  .intro-unfold-btn {
    position: absolute;
    right: 2px;
    bottom: 0;
    color: var(--is-color-773bef);
    cursor: pointer;
    background-color: var(--bg-color);
    .point {
      color: var(--is-color-7d7b89);
    }
  }
  &::after {
    position: absolute;
    width: 100%;
    height: 100%;
    content: '';
  }
}

// 展开样式
.unfold-text {
  width: 100%;
  padding-left: 0.625rem;
  word-wrap: break-word;
  .intro-label {
    display: none;
  }
  .intro-fold-btn {
    color: var(--is-color-773bef);
    word-break: keep-all;
    cursor: pointer;
  }
}
.fold-text,
.unfold-text {
  transition: max-height 0.3s ease-in-out;
}

/* 隐藏的克隆元素（用于测量真实文本高度） */
.hidden-text {
  position: absolute;
  visibility: hidden;
  width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal; /* 确保换行 */
}
</style>
