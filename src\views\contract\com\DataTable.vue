<script lang="ts" setup>
defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  datas: {
    type: Array,
    default: () => [],
  },
  tableHeight: {
    type: String,
    default: 'calc(100vh - 15rem)',
  },
})
const emits = defineEmits(['view', 'del'])
const getReviewInfo = (node: any) => {
  emits('view', node)
}
const delReviewInfo = (id: string) => {
  emits('del', id)
}
</script>
<template>
  <el-table
    v-loading="loading"
    :data="datas"
    style="width: 100%"
    :height="tableHeight"
    :border="false"
    :header-cell-style="{
      'background-color': '#fff',
      color: 'var(--is-color-7d7b89)',
      'font-size': '14px',
      'font-weight': '400',
      padding: '0.75rem 0',
      'line-height': '1.375rem',
    }"
    :row-style="{ height: '46px' }"
    :cell-style="{ padding: '4px 0 0 0', 'font-size': '0.875rem', color: '#221D39' }"
  >
    <el-table-column prop="contractName" :label="$t('合同文档')" :show-overflow-tooltip="true" min-width="300">
      <template #default="scope">
        <span>{{ scope.row.contractName || '--' }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="contractStatus" :label="$t('状态')" width="180" />
    <el-table-column prop="createTime" :label="$t('创建时间')" width="200">
      <template #default="scope">
        <span>{{ scope.row.createTime ? scope.row.createTime : '-' }}</span>
      </template>
    </el-table-column>
    <el-table-column :label="$t('操作')" width="200" fixed="right">
      <template #default="scope">
        <el-link underline="never" style="color: var(--is-color-773bef)" @click="getReviewInfo(scope.row)">
          {{ $t('查看') }}
        </el-link>
        <el-divider direction="vertical" />
        <el-link underline="never" style="color: var(--is-color-773bef)" @click="delReviewInfo(scope.row.contractId)">
          {{ $t('删除') }}
        </el-link>
      </template>
    </el-table-column>
  </el-table>
</template>
