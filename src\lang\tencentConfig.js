// 腾讯翻译配置文件
// 请在这里填入您的腾讯云翻译API配置信息

export const TENCENT_TRANSLATE_CONFIG = {
  // 腾讯云 SecretId
  secretId: 'AKIDtc9SYTBi181hAQZZMvOksnVzIOH2avAF', // 请填入您的腾讯云 SecretId

  // 腾讯云 SecretKey
  secretKey: 'xgCUZQdiaHm9PTK6f1A6pKdjcLtHTSEm', // 请填入您的腾讯云 SecretKey

  // 地域（必需参数）
  region: 'ap-shenzhen-fsi', // 北京地域，也可以使用 'ap-shanghai', 'ap-guangzhou' 等

  // API配置
  host: 'tmt.tencentcloudapi.com',
  service: 'tmt',
  action: 'TextTranslate',
  version: '2018-03-21'
}

// 使用说明：
// 1. 请到腾讯云控制台申请API密钥: https://console.cloud.tencent.com/cam/capi
// 2. 开通机器翻译服务: https://console.cloud.tencent.com/tmt
// 3. 将SecretId和SecretKey填入上面的配置中
// 4. 保存文件后重新运行翻译脚本

// 腾讯云支持的地域列表：
// ap-beijing: 北京
// ap-shanghai: 上海
// ap-guangzhou: 广州
// ap-chengdu: 成都
// ap-chongqing: 重庆
// ap-hongkong: 香港
// ap-singapore: 新加坡
// ap-tokyo: 东京
// na-siliconvalley: 硅谷
// na-ashburn: 弗吉尼亚

// 腾讯翻译支持的语言代码：
// zh: 中文
// en: 英文
// ja: 日文
// ko: 韩文
// fr: 法文
// es: 西班牙文
// ru: 俄文
// de: 德文
// it: 意大利文
// tr: 土耳其文
// pt: 葡萄牙文
// vi: 越南文
// id: 印尼文
// th: 泰文
// ms: 马来文

// 注意：请不要将此文件提交到版本控制系统中，以保护您的API密钥安全
