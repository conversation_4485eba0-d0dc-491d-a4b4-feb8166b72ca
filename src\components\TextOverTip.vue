<script setup lang="ts">
// 使用demo     <TextOverTip content="呢可能扣分砍脑壳看能否你可快你噶嫩凯哥哥跟可归纳可" :row="3" />
type IPlacement =
  | 'top'
  | 'top-start'
  | 'top-end'
  | 'bottom'
  | 'bottom-start'
  | 'bottom-end'
  | 'left'
  | 'left-start'
  | 'left-end'
  | 'right'
  | 'right-start'
  | 'right-end'
interface IProps {
  content?: string // 显示的文字内容
  refName?: string // 子元素标识（如在同一页面中调用多次组件，此参数不可重复）
  effect?: 'dark' | 'light'
  placement?: IPlacement
  popperClass?: string
  row?: number
}

const props = withDefaults(defineProps<IProps>(), {
  content: '',
  refName: 'refSpan',
  effect: 'dark',
  placement: 'bottom',
  popperClass: '--ep-over-tooltip',
  row: 1,
})
const isDisabledTooltip = ref(false)

const refSpan: any = {}
const setRef = (e: any, item: string) => {
  if (e) {
    refSpan[item] = e
  }
}

function onMouseOver(str: string) {
  if (props.row === 1) {
    const parentWidth = refSpan[str]?.parentNode.offsetWidth
    const contentWidth = refSpan[str]?.offsetWidth
    // 判断是否禁用tooltip功能
    isDisabledTooltip.value = contentWidth <= parentWidth
  } else {
    // 多行隐藏
    const parentHeight = refSpan[str]?.parentNode.offsetHeight
    const contentHeight = refSpan[str]?.offsetHeight
    // 判断是否禁用tooltip功能
    isDisabledTooltip.value = contentHeight <= parentHeight + 5
  }
}
</script>

<template>
  <div class="text-over-tooltip-components">
    <el-tooltip
      :effect="effect"
      :disabled="isDisabledTooltip"
      :content="content"
      :placement="placement"
      :popper-class="popperClass"
      :show-after="200"
    >
      <div
        class="ellipsis"
        :class="{ custom: row > 1 }"
        :style="{ '--row-num': row }"
        @mouseover="onMouseOver(refName)"
      >
        <span :ref="(el) => setRef(el, refName)">
          <slot name="contentText">
            <span>{{ content }}</span>
          </slot>
        </span>
      </div>
    </el-tooltip>
  </div>
</template>

<style lang="scss" scoped>
.text-over-tooltip-components {
  /* 文字超出宽度显示省略号 单行 */
  .ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .custom {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: var(--row-num);
    word-break: break-all;
    -webkit-box-orient: vertical;
    white-space: wrap;
  }
}
</style>

<style>
.--ep-over-tooltip {
  max-width: 240px;
}
</style>
