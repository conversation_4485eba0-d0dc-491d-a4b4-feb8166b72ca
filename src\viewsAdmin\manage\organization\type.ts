import { $t } from '@/utils/i18n'

// 上级标识
export const superiorFlagList = [
  { name: $t('非上级'), value: 0, label: $t('否') },
  { name: $t('上级'), value: 1, label: $t('是') },
]

// 工作交接状态
export const workHandoverStatusList = [
  { label: $t('未交接'), value: 0 },
  { label: $t('已交接'), value: 1 },
]

// 用户状态
export const userStatusList = [
  { label: $t('禁用'), value: 0 },
  { label: $t('正常'), value: 1 },
  { label: $t('转岗'), value: 2 },
  { label: $t('离职'), value: 3 },
]

// 有效期
export const timeValidList = [
  { label: $t('永久'), value: 1 },
  { label: $t('自定义'), value: 0 },
]

// 拖拽参数映射 （ele组件--后台参数）
export const dragTypeObj = {
  before: 0,
  inner: 1,
  after: 2,
}

export interface UserNode {
  id: number
  userCode: string
  realName: string
  userName: string
  userPhone: string
  userStatus: number
  workHandoverStatus: number
  leaderFlag: number
  roleNameListString?: string
  orgNameListString?: string
  orgIdListString?: string
  roleCodeListString?: string
  mainDepartmentName?: string
  createBy?: string
}
