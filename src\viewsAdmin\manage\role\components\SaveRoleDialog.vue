<script setup lang="ts">
import { $t } from '@/utils/i18n'
import type { FormInstance, FormRules } from 'element-plus'
import { saveRole } from '@/services/manage/index.ts'
import { queryPermissionList } from '@/services/manage/index.ts'
import type { IPermission, ISaveRoleParams, IQueryPermissionList, IList } from '@/services/manage/index.ts'
import { RESPONSE_CODE_SUCCESS } from '@/constants'

const permissionList = ref<IPermission[]>([])
// 获取权限列表(用的时候再调)
const getPermissionList = async (roleCode?: string) => {
  try {
    const { data } = await queryPermissionList<IQueryPermissionList>(roleCode)
    permissionList.value = data.menuList
  } catch (err) {
    console.error('获取权限列表失败', err)
  }
}

const emits = defineEmits(['updateList'])

const roleId = ref('')
const title = computed(() => {
  return roleId.value ? $t('编辑角色') : $t('新增角色')
})

const visible = ref(false)
const ruleFormRef = ref<FormInstance>()

const formData = ref<ISaveRoleParams>({
  roleId: '',
  roleName: '',
  roleRemark: '',
  menuCodeList: [],
})

const rules: FormRules = {
  roleName: [
    {
      required: true,
      message: $t('请输入角色名称'),
      trigger: 'blur',
    },
  ],
  menuCodeList: [
    {
      required: true,
      message: $t('请至少选择一个功能权限'),
      trigger: 'change',
      validator: (_rule, value, callback) => {
        if (!value || value.length === 0) {
          callback(new Error($t('请至少选择一个功能权限')))
        } else {
          callback()
        }
      },
    },
  ],
}

const openDialog = async (row?: IList) => {
  visible.value = true
  if (row?.id) {
    roleId.value = row.id
    formData.value = {
      roleId: row.id,
      roleName: row.roleName,
      roleRemark: row.roleRemark,
      menuCodeList: row.menuCodeList,
    }
    await getPermissionList(row.roleCode)
  } else {
    await getPermissionList()
  }
}

const closeDialog = () => {
  visible.value = false
  ruleFormRef.value?.resetFields()
}

// 新增 / 编辑
const saveRoleData = async () => {
  await ruleFormRef.value?.validate()
  let params = {}
  if (roleId.value) {
    params = {
      id: roleId.value,
      ...formData.value,
    }
  } else {
    params = formData.value
  }
  try {
    const res = await saveRole(params as ISaveRoleParams)
    if (res.code == RESPONSE_CODE_SUCCESS) {
      emits('updateList')
      if (roleId.value) ElMessage.success($t('编辑角色成功'))
      else ElMessage.success($t('新增角色成功'))
      closeDialog()
    }
  } catch (error) {
    ElMessage.error($t('新增角色失败'))
    console.log('验证失败', error)
  }
}

defineExpose({
  openDialog,
})
</script>

<template>
  <el-dialog v-model="visible" :title="title" :close-on-click-modal="false" width="600px" @close="closeDialog">
    <el-form ref="ruleFormRef" :model="formData" :rules="rules" class="right-form" label-position="top">
      <el-form-item :label="$t('角色名称')" prop="roleName">
        <el-input v-model="formData.roleName" :placeholder="$t('请输入角色名称')" />
      </el-form-item>
      <el-form-item :label="$t('功能权限')" prop="menuCodeList">
        <el-checkbox-group v-model="formData.menuCodeList">
          <el-checkbox
            v-for="item of permissionList"
            :key="item.menuCode"
            :value="item.menuCode"
            :checked="item.relFlag"
            name="type"
          >
            {{ item.menuName }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">{{ $t('取 消') }}</el-button>
        <el-button type="primary" @click="saveRoleData" :disabled="!formData.roleName">{{ $t('确 认') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
