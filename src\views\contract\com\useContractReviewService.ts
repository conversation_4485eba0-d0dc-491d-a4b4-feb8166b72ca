import {
  getReivewRuleList,
  getRulesList,
  getCustomRulesList,
  getAiReviewRuleType,
  getAiReviewRulePurposeList,
  getMatchRule,
  getReviewRuleConfig,
  contractReview,
} from '@/services/contract'
import {
  AiReviewPosition,
  ContractInfo,
  ContractParticipant,
  ReviewRuleList,
  ReviewRulePosition,
  ReviewRuleType,
  ReviewRuleTypeItem,
} from './Model'
import { useContractStore } from '@/stores'
import router from '@/router'
import { $t } from '@/utils/i18n'
export function useContractReviewService() {
  const isAiReview = ref(false)
  const reviewRuleDatas = ref<ReviewRuleList[]>([]) //审查清单列表数据
  const reviewRuleLoading = ref(false)
  const reviewPositionDatas = ref<ReviewRulePosition[]>([])
  const reviewPositionLoading = ref(false)
  const reviewRuleCode = ref('')
  const reviewPositionCode = ref('')
  const contractStore = useContractStore()
  const commitAble = ref(false)
  /**
   * 获取审查清单
   * @returns
   */
  const queryReivewRuleList = async (_contract: ContractInfo) => {
    reviewRuleLoading.value = true
    reviewPositionLoading.value = true
    const { code, data, message } = await getReivewRuleList()
    reviewRuleLoading.value = false
    reviewPositionLoading.value = false
    if (!Array.isArray(data)) {
      return
    }
    data.forEach((item: ReviewRuleList) => {
      const reviewRule = new ReviewRuleList()
      reviewRule.typeName = item.typeName
      item.itermList.forEach((rule) => {
        reviewRule.itermList.push(new ReviewRulePosition(rule.ruleCode, rule.rulePosition))
      })
      reviewRuleDatas.value.push(reviewRule)
    })
    const {
      code: code1,
      data: matchRuleName,
      message: message1,
    } = await getMatchRule(contractStore.contractInfo.originalFileCode)
    const finded = reviewRuleDatas.value.find((item: ReviewRuleList) => {
      return item.typeName === matchRuleName
    })
    /**
     * 获取默认匹配规则
     */
    await queryAiReviewRuleType(contractStore.contractInfo.originalFileCode)
    if (finded) {
      reviewRuleCode.value = finded.typeName

      if (matchRuleName == '' || matchRuleName == '通用合同') {
        ElMessage.warning($t('未匹配到审查清单, 推荐使用AI生成审查清单'))

        isAiReview.value = true
      } else {
        isAiReview.value = false
        ElMessage.success($t('已匹配到审查清单'))
        handleReviewRuleChange(finded.typeName)
      }
    } else {
      reviewRuleCode.value = aiReviewPosition.value!.contractType
      ElMessage.warning('未匹配到审查清单, 推荐使用AI生成审查清单')
      isAiReview.value = true
    }
  }
  /**
   * 切换审查清单
   * @param code
   */
  const handleReviewRuleChange = (code: string) => {
    reviewPositionCode.value = ''
    const getRule = reviewRuleDatas.value.find((item) => {
      return item.typeName === code
    })

    if (getRule) {
      ruleTreeData.value.length = 0
      customRuleTreeData.value.length = 0
      reviewPositionDatas.value = getRule.itermList
    }
  }

  /**
   * 切换审查立场
   * @param code
   */
  const handleReviewRulePositionChange = (code: string) => {
    if (currentRulePositionCode.value === code) {
      return
    }
    ruleTreeData.value.length = 0
    customRuleTreeData.value.length = 0
    currentRulePositionCode.value = code
    queryRulesList(code)
    queryCustomRulesList(code)
  }

  const reloadCustomRule = () => {
    queryCustomRulesList(currentRulePositionCode.value)
  }

  const ruleTreeData = ref<ReviewRuleType[]>([])
  const customRuleTreeData = ref<ReviewRuleType[]>([])
  /**
   * 查询规则树
   * @param code
   */
  const queryRulesList = async (code: string) => {
    const { data } = await getRulesList<ReviewRuleType[]>(code)
    if (!Array.isArray(data)) {
      return
    }
    ruleTreeData.value = data
  }

  /**
   * 查询自定义审查项
   */
  const queryCustomRulesList = async (code: string) => {
    const { data } = await getCustomRulesList<ReviewRuleTypeItem[]>(code)
    if (!Array.isArray(data) || !data.length) {
      return
    }
    const reviewRuleType = new ReviewRuleType()
    reviewRuleType.ruleTypeName = $t('自定义审查项')
    reviewRuleType.id = '0'
    reviewRuleType.child = data.map((item: any) => {
      return new ReviewRuleTypeItem(item.ruleCustomId, item.ruleName)
    })
    customRuleTreeData.value = [reviewRuleType]
  }
  const aiReviewPosition = ref<AiReviewPosition>()

  /**
   * 获取智能审查立场
   */
  const queryAiReviewRuleType = async (fileCode: string) => {
    const { data, code, message } = await getAiReviewRuleType(fileCode)
    if (code !== '000000' && code !== '200') {
      ElMessage.error(message)
      return
    }
    if (!data) {
      return
    }
    aiReviewPosition.value = new AiReviewPosition(data as AiReviewPosition)
  }

  const currentRulePositionCode = ref('')
  const purposeLoading = ref(true)

  /**
   * 获取智能审查规则
   */
  const participantCache = ref<ContractParticipant>()
  const queryAiReviewRulePurposeList = async (participant: ContractParticipant) => {
    if (participantCache.value === participant) {
      return
    }
    if (participant.purposes.length) {
      return
    }
    participantCache.value = participant
    purposeLoading.value = true
    const { code, message, data } = await getAiReviewRulePurposeList(
      reviewRuleCode.value,
      contractStore.contractInfo.originalFileCode,
      '',
      participant.participantName,
    )
    purposeLoading.value = false
    if (code !== '000000' && code !== '200') {
      ElMessage.error(message)
      return
    }
    const { reviewPurposeList } = data as any
    participant.purposes = reviewPurposeList
  }

  const queryReviewRuleConfig = async () => {
    let ruleId = ''
    try {
      const { data } = await getReviewRuleConfig(
        contractStore.contractInfo.contractName,
        aiReviewPosition.value!.contractType,
        contractStore.contractInfo.originalFileCode,
        participantCache.value!.reviewPosition,
        participantCache.value!.purposes,
      )
      if (data) {
        const { ruleCode, id } = data as any
        contractStore.contractInfo.setRuleCode(ruleCode)
        ruleId = id
      }
    } catch (e) {
      ElMessage.error($t('生成审查清单失败'))
    }
    return ruleId
  }

  const save = async (params: any) => {
    const { code } = await contractReview(params)
    if (code === '000000') {
      router.push('/contract/result/2/' + contractStore.contractInfo.constractId + '?t=' + Date.now())
    }
  }
  const isActiveAble = ref(false)
  const watch1 = watch(
    () => reviewRuleCode.value,
    (_val) => {
      if (_val) {
        isActiveAble.value = true
      }
    },
    {
      immediate: true,
    },
  )
  onUnmounted(() => {
    watch1()
  })
  return {
    isActiveAble,
    aiReviewPosition,
    customRuleTreeData,
    isAiReview,
    commitAble,
    ruleTreeData,
    reviewRuleCode,
    participantCache,
    reviewPositionCode,
    reviewRuleDatas,
    reviewRuleLoading,
    purposeLoading,
    reviewPositionDatas,
    reviewPositionLoading,
    currentRulePositionCode,
    save,
    reloadCustomRule,
    queryReviewRuleConfig,
    queryRulesList,
    queryAiReviewRuleType,
    queryReivewRuleList,
    handleReviewRuleChange,
    queryCustomRulesList,
    queryAiReviewRulePurposeList,
    handleReviewRulePositionChange,
  }
}
