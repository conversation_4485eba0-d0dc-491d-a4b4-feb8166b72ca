import Layout from '@/layout/index.vue'
import type { RouteRecordRaw } from 'vue-router'

const router: RouteRecordRaw[] = [
  {
    path: '/extraction',
    component: Layout,
    children: [
      {
        path: '',
        name: 'Extraction',
        meta: {
          permission: 'ContractExtract',
        },
        component: () => import(/* webpackChunkName: "Extraction" */ '@/views/extraction/index.vue'),
      },
    ],
  },
]
export default router
