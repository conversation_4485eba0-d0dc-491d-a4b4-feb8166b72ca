<script setup lang="ts">
import SearchBox from './SearchBox.vue'
import TableBox from './TableBox.vue'
import { statusEnum } from '../types/enums'
import type { IStatusOption, ISearchArgs } from '../types'

import { $t } from '@/utils/i18n'
defineProps({
  isSemantics: {
    type: Boolean,
    default: false,
  },
})
const searchArgs = ref<ISearchArgs>({
  contractName: '', // 文件名称
  dateRange: [], // 比对完成时间 筛选范围
  status: '', // 比对状态
})

// 比对状态 - 比对中：1-99；比对成功：100；比对失败：-1；排队中：0；
const statusOption = ref<IStatusOption[]>([
  // { value: '', label: '全部' },
  { value: statusEnum.QUEUE, label: $t('排队中'), color: 'var(--main-bg)' },
  { value: statusEnum.IN_PROGRESS, label: $t('比对中'), color: 'var(--main-bg)' },
  { value: statusEnum.SUCCESS, label: $t('比对成功'), color: 'var(--is-color-1b9275)' },
  { value: statusEnum.FAILED, label: $t('比对失败'), color: 'var(--is-color-e6555e)' },
])

const tableBox = ref<InstanceType<typeof TableBox>>()
function search() {
  return tableBox.value?.reset()
}
function resetSearch() {
  searchArgs.value.contractName = ''
  searchArgs.value.dateRange = []
  searchArgs.value.status = ''
  search()
}
</script>

<template>
  <div class="diff-wrap">
    <SearchBox
      :search-args="searchArgs"
      :status-option="statusOption"
      @search="search"
      @resetSearch="resetSearch"
      @update:status-option="search"
    ></SearchBox>
    <TableBox
      ref="tableBox"
      :search-args="searchArgs"
      :status-option="statusOption"
      :is-semantics="isSemantics"
    ></TableBox>
  </div>
</template>

<style lang="scss" scoped>
.diff-wrap {
  display: flex;
  flex: 1;
  flex-direction: column;
  height: 0;
}
</style>
