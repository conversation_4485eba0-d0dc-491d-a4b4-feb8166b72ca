<script setup>
// import TextOverTooltip from '@/components/TextOverTooltip.vue'

defineProps({
  notationObj: {
    type: Object,
    default() {
      return {}
    },
  },
  isTopArrow: {
    type: Boolean,
    default: false,
  },
})
</script>

<template>
  <div ref="refNotation" class="notation-box">
    <div class="notation-item">
      <img src="@/assets/images/textComparison/head.png" alt="" class="notation-item-head" />
      <span class="notation-item-name">{{ notationObj.name }}</span>
      <span class="notation-item-time">{{ notationObj.time }}</span>
    </div>
    <div class="notation-content">
      <!-- <text-over-tooltip ref-name="testName1" class-name="fs5" :content="notationObj.content"></text-over-tooltip> -->
    </div>
    <div class="notation-arrow" :class="isTopArrow ? 'top-arrow' : 'bottom-arrow'"></div>
  </div>
</template>

<style lang="scss" scoped>
.notation-box {
  position: absolute;
  display: flex;
  flex-direction: column;
  width: 240px;
  min-height: 82px;
  padding: 10px;
  background: rgb(255 255 255 / 100%);
  border: 0.1px solid rgba($color: #000, $alpha: 12%);
  border-radius: 4px;
  box-shadow: 0 3px 12px 0 rgb(0 0 0 / 12%);
  .notation-item {
    display: flex;
    align-items: center;
    &-head {
      width: 12px;
      height: 12px;
      margin-bottom: 2px;
    }
    &-name {
      flex: 1;
      padding-left: 8px;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 12px;
      color: #929292;
      white-space: nowrap;
    }
    &-time {
      font-size: 12px;
      color: #929292;
    }
  }
  .notation-content {
    display: -webkit-box;

    // display: flex;
    flex: 1;
    align-items: center;
    padding-top: 16px;
    overflow: hidden;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 5;
  }
  .notation-arrow {
    position: absolute;
    left: 20px;
    width: 15px;
    height: 15px;
    background-color: var(--bg-color);
    transform: rotate(45deg);
    transform-origin: center;
  }
  .top-arrow {
    top: -8px;
    border-top: 0.1px solid rgba($color: #000, $alpha: 12%);
    border-right: none;
    border-bottom: none;
    border-left: 0.1px solid rgba($color: #000, $alpha: 12%);
  }
  .bottom-arrow {
    bottom: -8px;
    border-top: none;
    border-right: 0.1px solid rgba($color: #000, $alpha: 12%);
    border-bottom: 0.1px solid rgba($color: #000, $alpha: 12%);
    border-left: none;
  }
}
</style>
