import type { Ref } from 'vue'
import { nextTick, ref } from 'vue'

type ScrollElement = HTMLDivElement | null

interface ScrollReturn {
  scrollRef: Ref<ScrollElement>
  scrollToBottom: (target: HTMLElement) => Promise<void>
  scrollToTop: () => Promise<void>
  scrollToBottomIfAtBottom: () => Promise<void>
}

export function useScroll(): ScrollReturn {
  const scrollRef = ref<ScrollElement>(null)

  const scrollToBottom = async (target: HTMLElement) => {
    await nextTick()
    // 获取 simplebar 的滚动容器

    if (!target) return

    const start = target.scrollTop
    const end = target.scrollHeight
    const duration = 500 // 动画持续时间，单位：毫秒
    const startTime = performance.now()

    const animateScroll = (currentTime: number) => {
      const elapsed = currentTime - startTime
      if (elapsed < duration) {
        // 使用 ease-in-out 缓动函数
        const progress = elapsed / duration
        const ease = progress < 0.5 ? 2 * progress * progress : -1 + (4 - 2 * progress) * progress
        target.scrollTop = start + (end - start) * ease
        requestAnimationFrame(animateScroll)
      } else {
        target.scrollTop = end
      }
    }

    requestAnimationFrame(animateScroll)
  }

  const scrollToTop = async () => {
    await nextTick()
    if (scrollRef.value) scrollRef.value.scrollTop = 0
  }

  const scrollToBottomIfAtBottom = async () => {
    await nextTick()
    if (scrollRef.value) {
      const threshold = 10 // 阈值，表示滚动条到底部的距离阈值
      const distanceToBottom = scrollRef.value.scrollHeight - scrollRef.value.scrollTop - scrollRef.value.clientHeight

      if (distanceToBottom <= threshold) {
        scrollRef.value.scrollTop = scrollRef.value.scrollHeight
      }
    }
  }

  return {
    scrollRef,

    scrollToBottom,
    scrollToTop,
    scrollToBottomIfAtBottom,
  }
}
