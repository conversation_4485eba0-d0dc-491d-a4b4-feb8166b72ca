<script setup lang="ts">
import { pdfPureViewer, pdfViewer } from '@iterms/pdf-viewer'

const file = ref<Blob | null>(null)
const fileName = ref('')

const refPdf = ref<any>()

// function init() {
//   const myRequest = new Request('/test.pdf')
//   // let myRequest = new Request('/textPoints.pdf');
//   fetch(myRequest)
//     .then(function (response) {
//       if (!response.ok) {
//         throw new Error(`HTTP error! status: ${response.status}`)
//       }
//       return response.blob()
//     })
//     .then(function (response) {
//       console.log('response', response)
//       file.value = response
//     })
// }
// init()
</script>

<template>
  <div class="pdf">
    <pdfViewer v-if="file" ref="refPdf" :file="file" :fileName="fileName" class="pdf-viewer" />
  </div>
</template>

<style lang="scss" scoped>
.pdf {
  display: flex;
  height: 100vh;
  overflow: hidden;
  &-viewer {
    width: 100%;
    height: calc(100% - 40px);
  }
}
</style>
