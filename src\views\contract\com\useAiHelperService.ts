import { ChatStatusEnum, SmartChat, SmartChatTypeEnum, type QueryParamInter } from './Model'
import { fetch } from '@/services/sse'
import { throttle } from 'lodash-es'
import { formatMD } from '@/utils'
import { useScroll } from '@/utils/scroller'
import MarkdownIt from 'markdown-it'
import { setAiEnable } from '@/services/contract'

import { $t } from '@/utils/i18n'
export function useAiHelperService() {
  const refer = ref('')
  const refChat = ref()
  const configVisible = ref(false)
  const chatBtnStatus = ref(ChatStatusEnum.DISABLE)
  const questionStr = ref('')
  const chatUrl = `/llm/robot/chat-sse`
  const refAnswer = ref()
  const { scrollToBottom } = useScroll()
  const defaultSmartChat = new SmartChat(
    '## 您好 ~ 我是合同审查小助理\n您可以直接向我提问, 也可以框选合同中文本内容, 我能够帮助你进行条款解释, 文本翻译等操作',
    SmartChatTypeEnum.ANSWER,
  )
  defaultSmartChat.isDefault = true
  const smartChats = ref<SmartChat[]>([defaultSmartChat])
  const markdown = new MarkdownIt({ html: true, breaks: true, typographer: true })
  const addAnswer = (content: string) => {
    const answer = new SmartChat(content, SmartChatTypeEnum.ANSWER)
    smartChats.value.push(answer)
    return answer
  }
  const addQuestion = (content: string) => {
    const hasDefault = smartChats.value.find((item) => item.isDefault)
    if (hasDefault) {
      smartChats.value.splice(0, 1)
    }
    const question = new SmartChat(content, SmartChatTypeEnum.QUESTION)
    smartChats.value.push(question)
    return question
  }
  const getLastAnswerChat = () => {
    const answers = smartChats.value.filter((chat) => chat.type === SmartChatTypeEnum.ANSWER)
    if (answers.length) {
      return answers[answers.length - 1]
    }
    return null
  }

  const controller = ref<AbortController>()
  const isChating = ref(false)
  const chatingText: string[] = []
  let lastChatId: any
  const chating = (body: QueryParamInter) => {
    scrollThrottle()
    const lastChat = getLastAnswerChat()
    controller.value = new AbortController()
    if (lastChatId) {
      body.lastTimeChatRoundId = lastChatId
    }

    if (!lastChat) {
      return
    }
    isChating.value = true
    fetch(
      chatUrl,
      body,
      {
        async onopen(response) {
          isChating.value = true
          lastChat.isLoading = true
          chatBtnStatus.value = ChatStatusEnum.STOPABLE

          if (response.ok) {
            // requestAnimationFrame(animate)
          } else if (response.status >= 400 && response.status < 500 && response.status !== 429) {
            if (response.status == 401) {
              ElMessage.error($t('您的会话已过期，请重新登录'))
            } else {
              ElMessage.error($t('服务错误'))
            }
            endChating()
          } else {
            ElMessage.error($t('服务错误'))
            endChating()
          }
        },
        onmessage(msg) {
          if (!msg.data) {
            throw 'reponse is error'
          }

          const result = JSON.parse(msg.data)
          lastChatId = result.roundId
          lastChat.roundId = result.roundId
          lastChat.isLoading = true
          lastChat.content += result.answer
          // console.log(lastChat.content)
          scrollThrottle()
          // if(result.answer)
          // chatingText.push(...result.answer)
        },
        onerror(err) {
          throw err
        },
        onclose() {
          lastChat.isLoading = false
          chatBtnStatus.value = ChatStatusEnum.DISABLE
          endChating()
        },
      },
      controller.value,
    )
  }
  const endChating = () => {
    controller.value?.abort()

    isChating.value = false
  }

  const resetSmartChat = () => {
    smartChats.value = [defaultSmartChat]
    chatingText.length = 0
    refer.value = ''
    chatBtnStatus.value = ChatStatusEnum.DISABLE
    scrollThrottle.cancel()
  }

  const sendChatingByPop = (obj: { question: { name: string; quicklyType: string; refer: string } }) => {
    if (isChating.value) {
      ElMessage.warning($t('正在回答问题，请稍等'))
      return false
    }
    const question = addQuestion(obj.question.name)
    addAnswer('')
    const params: QueryParamInter = {
      question: question.content,
      chatSessionId: question.id,
      referenceContent: obj.question.refer,
    }
    if ('ask' !== obj.question.quicklyType) {
      params.typeCode = obj.question.quicklyType
    }
    chating(params)
  }

  const scrollto = async () => {
    if (refAnswer.value) {
      // 将滚动容器滚动到底部
      await nextTick()
      console.log(refAnswer.value.wrapRef)
      refAnswer.value.scrollTo(0, refAnswer.value.wrapRef.scrollHeight)
    }
  }
  const scrollThrottle = throttle(scrollto, 200)

  // onBeforeUnmount(()=>{
  //     resetSmartChat()
  //     console.log('out')
  // })

  const barScroller = () => {
    scrollThrottle.cancel()
  }
  const getQuestionOutput = (item: string) => {
    return markdown.render(item)
  }

  const format = (text: string) => {
    return text.replace(/([^\n])\s*###/g, '$1\n\n### ')
  }
  const getOutput = (item: SmartChat) => {
    const str = format(item.content)
    return markdown.render(str)
  }
  function exportCons(item: SmartChat) {
    const value = formatMD(item.content)
    return value + (item.isLoading ? "<span class='ai-cursor'></span>" : '')
  }

  const removeRefer = () => {
    refer.value = ''
  }
  const send = () => {
    if (questionStr.value.trim() == '') {
      return
    }
    const answer = addQuestion(questionStr.value)
    addAnswer('')
    const bodyParams: QueryParamInter = { question: '', chatSessionId: '' }
    bodyParams.question = answer.content
    bodyParams.chatSessionId = answer.id
    if (refer.value != '') {
      bodyParams.referenceContent = refer.value
    }
    chating(bodyParams)
    questionStr.value = ''
  }
  function handleEnter(event: { keyCode: number; ctrlKey: any; preventDefault: () => void }) {
    console.log(event)
    if (event.keyCode == 13) {
      if (!event.ctrlKey) {
        event.preventDefault()
        // 发送
        send()
      } else {
        questionStr.value += '\n'
      }
    }
  }
  const setting = ref('')
  const changeSetting = async () => {
    await setAiEnable(setting.value)

    if (!setting.value) {
      ElMessage.error($t('已禁用问答浮窗！'))
    } else {
      ElMessage.success($t('已启用问答浮窗！'))
    }
  }
  return {
    changeSetting,
    send,
    handleEnter,
    getOutput,
    barScroller,
    getQuestionOutput,
    resetSmartChat,
    removeRefer,
    refAnswer,
    setting,
    configVisible,
    refer,
    chatBtnStatus,
    isChating,
    questionStr,
    chating,
    addAnswer,
    addQuestion,
    refChat,
    smartChats,
  }
}
