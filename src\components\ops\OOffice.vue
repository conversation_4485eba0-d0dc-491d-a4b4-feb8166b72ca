<script lang="ts" setup>
import { onUnmounted, watch, onMounted } from 'vue'
import { propsArgs } from './props'
import loadScript from './loadScript'
import { callback } from './utils'

declare global {
  interface Window {
    DocsAPI: any
    DocEditor: any
    Asc: any
    $: any
  }
}
declare const Asc: any
declare const Api: any
declare const Common: any

interface ResultEvent {
  target?: any
  data: any
}

const props = defineProps(propsArgs)

watch(
  () => props.config,
  () => {
    onChangeProps()
  },
  { deep: true },
)

function onError(errorCode: number): void {
  let message: string = ''
  switch (errorCode) {
    case -2:
      message = 'Error load DocsAPI from ' + props.documentServerUrl
      break
    case -3:
      message = 'DocsAPI is not defined'
      break
    default:
      message = 'Unknown error loading component'
      errorCode = -1
  }
  if (typeof props.onLoadComponentError == 'undefined') {
    console.error(message)
  } else {
    callback(props.onLoadComponentError, { errorCode, message })
  }
}

function onLoad() {
  try {
    const id = props.id || ''

    if (!window.DocsAPI) onError(-3)
    if (window?.DocEditor?.instances[id]) {
      console.log('Skip loading. Instance already exists', id)
      return
    }
    if (!window?.DocEditor?.instances) {
      window.DocEditor = { instances: {} }
    }
    const initConfig = Object.assign(
      {
        // document: {
        //     fileType: props.document_fileType,
        //     title: props.document_title,
        // },
        // documentType: props.documentType,
        // editorConfig: {
        //     lang: props.editorConfig_lang,
        // },
        events: {
          onAppReady: onAppReady,
          onDocumentStateChange: onDocumentStateChange,
          onError: events_onError,
          onMetaChange: onMetaChange,
          onDocumentReady: onDocumentReady,
          onInfo: onInfo,
          onWarning: onWarning,
          onOutdatedVersion: onOutdatedVersion,
          onRequestSharingSettings: onRequestSharingSettings,
          onRequestRename: onRequestRename,
          onMakeActionLink: onMakeActionLink,
          onRequestInsertImage: onRequestInsertImage,
          onRequestSaveAs: onRequestSaveAs,
          onRequestMailMergeRecipients: onRequestMailMergeRecipients,
          onRequestCompareFile: onRequestCompareFile,
          onRequestEditRights: onRequestEditRights,
          onRequestHistory: onRequestHistory,
          onRequestHistoryClose: onRequestHistoryClose,
          onRequestHistoryData: onRequestHistoryData,
          onRequestRestore: onRequestRestore,
          onDownloadAs: onDownloadAs,
        },
        // height: props.height,
        // type: props.type,
        // width: props.width,

        editorConfig: {
          customization: {
            logo: {},
          },
        },
      },
      props.config || {},
    )

    initConfig.editorConfig.customization.logo = {
      image: 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7',
      imageDark: 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7',
      url: '',
    }
    const editor = window.DocsAPI.DocEditor(id, initConfig)

    window.DocEditor.instances[id] = editor

    setTimeout(() => {
      initUI()
      initSelected()
    }, 2000)
  } catch (err: any) {
    console.error(err)
    onError(-1)
  }
}

function onChangeProps() {
  const id = props.id || ''
  if (window?.DocEditor?.instances[id]) {
    window.DocEditor.instances[id].destroyEditor()
    window.DocEditor.instances[id] = undefined
    onLoad()
  }
}

onMounted(() => {
  const url = props.documentServerUrl
  if (!url) return
  const docApiUrl = `${url}`
  loadScript(docApiUrl, 'ai-api-script')
    .then(() => onLoad())
    .catch(() => {
      onError(-2)
    })
})

onUnmounted(() => {
  const id = props.id || ''
  if (window?.DocEditor?.instances[id]) {
    window.DocEditor.instances[id].destroyEditor()
    window.DocEditor.instances[id] = undefined
  }
})

function onAppReady(e: object) {
  callback(props.onAppReady, e)
}

function onDocumentStateChange(e: object) {
  callback(props.onDocumentStateChange, e)
}

function events_onError(e: object) {
  callback(props.onError, e)
}

function onMetaChange(e: object) {
  callback(props.onMetaChange, e)
}

function onDocumentReady(e: object) {
  callback(props.onDocumentReady, e)
}

function onInfo(e: ResultEvent) {
  if (e.data.type === 'selected_events') {
    console.log(e)
  }
}

function onWarning(e: object) {
  callback(props.onWarning, e)
}

function onOutdatedVersion(e: object) {
  callback(props.onOutdatedVersion, e)
}

function onRequestSharingSettings(e: object) {
  callback(props.onRequestSharingSettings, e)
}

function onRequestRename(e: object) {
  callback(props.onRequestRename, e)
}
function onMakeActionLink(e: object) {
  callback(props.onMakeActionLink, e)
}

function onRequestInsertImage(e: object) {
  callback(props.onRequestInsertImage, e)
}

function onRequestMailMergeRecipients(e: object) {
  callback(props.onRequestMailMergeRecipients, e)
}
function onRequestCompareFile(e: object) {
  callback(props.onRequestCompareFile, e)
}

function onRequestEditRights(e: object) {
  callback(props.onRequestEditRights, e)
}

function onRequestHistory(e: object) {
  callback(props.onRequestHistory, e)
}

function onRequestHistoryClose(e: object) {
  callback(props.onRequestHistoryClose, e)
}

const onRequestSaveAs = (e: object) => {
  callback(props.onRequestHistoryData, e)
}

const onRequestHistoryData = (e: object) => {
  callback(props.onRequestHistoryData, e)
}

const onRequestRestore = (e: object) => {
  callback(props.onRequestRestore, e)
}

// 连接器的方法

function createConnector() {
  const id = props.id || ''
  return window?.DocEditor?.instances[id].createConnector()
}

// 隐藏菜单栏
function initUI() {
  const connector = createConnector()
  connector.callCommand(
    function () {
      // const doc = Api.GetDocument()
      // var documentHolder = DE.getController('DocumentHolder').documentHolder
      // var Plugins = DE.getController('Common.Controllers.Plugins').plugins
      // console.log(Plugins)
      try {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-expect-error
        $('[data-layout-name=toolbar-plugins]').hide()
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-expect-error
        $('#left-btn-about').hide()
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-expect-error
        $('#slot-btn-share').hide()
      } catch (error) {}
    },
    function () {
      console.log('callback command')
    },
    {},
  )
}

function initSelected() {
  const connector = createConnector()
  connector.callCommand(
    function () {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      function areNumbersEqual(a, b, c, d) {
        return a === b && b === c && c === d
      }

      Api.asc_registerCallback('asc_onFocusObject', function () {
        const bounds = Api.asc_GetSelectionBounds()
        const left = 40
        const top = 20
        const x = bounds[3][0] + left
        const y = bounds[3][1] + top
        const text = Api.asc_GetSelectedText(true) || ''
        const isSelected = !areNumbersEqual(bounds[0][0], bounds[1][0], bounds[2][0], bounds[3][0])
        const data = {
          type: 'selected_events',
          bounds: bounds,
          left: left,
          top: top,
          x: x,
          y: y,
          text: text,
          isSelected: isSelected,
        }
        if (isSelected) {
          Common.Gateway.sendInfo(data)
        }
      })
    },
    function () {},
    {},
  )
}

// 搜索文本方法
function searchText(val: string) {
  let ofsResolve: (value: unknown) => void
  Asc.scope.val = val
  const connector = createConnector()
  connector.callCommand(
    function () {
      const doc = Api.GetDocument()
      const searchResult = doc.Search(Asc.scope.val)
      if (searchResult && searchResult.length) {
        searchResult[0].Select()
        return true
      }
      return false
    },
    function (officeRes: boolean) {
      if (!officeRes) return ofsResolve(false)
      ofsResolve(true)
    },
    {},
  )

  return new Promise((resolve) => {
    ofsResolve = resolve
  })
}

// 获取最新url
let resolveDownload: (value: any) => void
const onDownloadAs = (e: any) => {
  resolveDownload(e?.data)
}
function getFileUrl() {
  const id = props.id || ''
  window?.DocEditor?.instances[id].downloadAs()
  return new Promise((resolve: (value: any) => void) => {
    resolveDownload = resolve
  })
}

function setRevision(val: string) {
  let ofsResolve: (value: unknown) => void
  Asc.scope.val = val
  const connector = createConnector()
  connector.callCommand(
    function () {
      const doc = Api.GetDocument()
      const searchResult = doc.Search(Asc.scope.val)
      if (searchResult && searchResult.length) {
        console.dir(searchResult[0])
        const start = searchResult[0].Start
        const end = searchResult[0].End
        console.log('start', start)
        console.log('end', end)
        const range = doc.GetRange(start, end)
        range.Select()
        range.Delete()
        // const range2 = doc.GetRange(end, end + 1)
        // range2.Select()
        //  const range2 = doc.GetRange(end + 1, end + 2)
        doc.MoveCursorToPos(range.GetEndPos())
        const paragraph = Api.CreateParagraph()
        paragraph.AddText('oooo')
        doc.InsertContent([paragraph])
        // range2.Select()
        // range2.AddText('ONLY', 'after')

        return true
      }
      return false
      // const doc = Api.GetDocument()
      // const paragraph = doc.GetElement(0)
      // // doc.SetTrackRevisions(true)
      // paragraph.AddText('Track revisions mode was set.')
    },
    function (officeRes: boolean) {
      if (!officeRes) return ofsResolve(false)
      ofsResolve(true)
    },
    {},
  )

  return new Promise((resolve) => {
    ofsResolve = resolve
  })
}

defineExpose({
  searchText,
  getFileUrl,
  setRevision,
})
</script>

<template>
  <div :id="id" style="width: 100%; height: 100%"></div>
</template>
