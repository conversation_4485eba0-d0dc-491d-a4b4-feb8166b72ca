export const ITERMS_LOGIN_TK = 'iterms-u-tk'
export const LOGIN_SUCCESS = 'login-success'
export const SET_THINKING_VAL = 'thinking'
export const SET_NETSEARCH_VAL = 'netsearch'
export const REVIEW_DATA_LOAD = 'review-data-load'
export const PATH_URL = '/api/bff-iterms-saas'
export const CHAT_ATTACH_ACCEPT =
  'application/pdf,image/png,image/jpeg,image/jpg,image/svg,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document'
export const CHAT_ATTACH_LIMIT_SIZE = 10 //10M
export const CHAT_ATTACH_LIMIT_COUNT = 3
export const HISTORY_CHAT_COUNT = 5
// 记录用户的memberId 本地缓存 后期后端需要改
export const MEMBER_ID_KEY = 'MEMBER_ID'

export enum BtnStatusEnum {
  READY = 'ready',
  UN_READY = 'unready',
  PROCESS = 'process',
}
export enum InputBoxSizeEnum {
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large',
  LARGE_FLOW = 'large-flow',
  MEDIUM_ATTACH = 'medium-attach',
  SMALL_FLOW = 'small-flow',
  MEDIUM_HEADER = 'medium-header',
}
export enum InputerStyleEnum {
  HEADER_ATTACH_FLOW = 'header-attach-flow',
  HEADER_ATTACH = 'header-attach',
  INNER_ONLY = 'inner-only',
  INNER_ONLY_FLOW = 'inner-only-flow',
  ATTACH_FLOW = 'attach-input-flow',
  ATTACH = 'attach-input',
  HEADER_UPLOAD = 'header-upload',
  HEADER_UPLOAD_FLOW = 'header-upload-flow',
}

export const RESPONSE_CODE_SUCCESS = '000000'

export enum PageStep {
  DETAIL = 'detail',
  EDITOR = 'editor',
  TEMP = 'temp',
}

export enum PageView {
  MAIN = 'main',
  UNCATELOGUED = 'uncatelogued',
  FILELIST = 'file-list',
  RECYCLE = 'recycle',
  SETTING = 'setting',
  INTRODUCE = 'introduct',
}

export enum CreateKind {
  AI = 1,
  TEMPLATE = 2,
  EMPTY = 3,
  UPLOAD = 4,
}

export const disableMenuItems = [
  'quote',
  'markdown',
  'file',
  'signature',
  'seal',
  'diagrams',
  'mind-map',
  'mermaid',
  'qrcode',
  'barcode',
  'video',
  'audio',
  'file',
  'image',
  'code',
  'emoji',
  'math',
  'webpage',
  'preview',
  'embed',
  'exportword',
]

export const enum WorkflowMap {
  CREATE = '1',
  CASES = '2',
  LAWS = '3',
  ANALYSIS = '4',
  OUTLINE = '5',
  GENERATE = '6',
}

export const AutoSave = {
  enabled: false,
  interval: 10000,
}
export const enum FileOperateKind {
  DELETE = 0, //删除
  RECOVER = 1, //恢复
}
export const enum Btns {
  //头部按钮
  HOME = 0,
  CREATE_BLANK = 1,
  UPLOAD = 2,
  VERSION_LIST = 3,
  STEP = 4,
  PREV_BTN = 5,
  NEXT_BTN = 6,
  EDIT = 7,
  SAVE_AS = 8,
  DOWNLOAD = 9,
  SEARCH = 10,
}
export const enum Steps {
  VIEW = 'view',
  PECTINATION = 'pectination',
  CREATE = 'create',
}

export const CopyRight = {
  privite: 'https://legal.fadada.com/policy/1920377982875222016',
  // agreement: 'https://legal.fadada.com/policy/1838136497748082688',
  collect: 'https://legal.fadada.com/policy/1920379175785611264',
  service: 'https://legal.fadada.com/policy/1920378444479348736',
  deregister: 'https://legal.fadada.com/policy/1920378826072932352',
}

export const BASE_URL = '/api/bff-iterms-assistant'

export const enum SearchResultTabs {
  IS_LAW = 0, //法条检索
  IS_CASE = 1, //案例检索
}

export const enum LegalDocumentStatus {
  IS_TEMPLATE = 'T',
  IS_DOCUMENT = 'D',
}

export const enum DocumentType {
  DOC = 1,
  URL = 2,
  PDF = 3,
  DOCX = 4,
  HTML = 5,
}

export const FILE_TYPE_MAP = {
  [DocumentType.DOC]: 'doc',
  [DocumentType.URL]: 'url',
  [DocumentType.PDF]: 'pdf',
  [DocumentType.DOCX]: 'docx',
  [DocumentType.HTML]: 'html',
}

export const htmlTemp = (innerHTML: string) => {
  return `<!DOCTYPE html>
		<html lang="en">
		<head>
		<meta charset="UTF-8">  
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		</head>
		<body>  
			${innerHTML}
		</body>
		</html>`
}

export const downloadHtmlTemp = (innerHTML: string) => {
  return `<!DOCTYPE html>
		<html lang="en">
			<head>
				<meta charset="UTF-8">
				<style>					 
					@page {
						size: A4;
						margin: 2cm; /* 设置页边距 */
					}
					body {
						font-family: Arial, sans-serif;
						font-size: 12pt;
						line-height: 1.5;
					}
				</style>
			</head>
			<body>
				${innerHTML}
			</body>
		</html>`
}
