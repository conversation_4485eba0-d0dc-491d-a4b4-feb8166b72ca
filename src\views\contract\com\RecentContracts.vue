<script lang="ts" setup>
import DataTable from './DataTable.vue'
import { useContractListService } from './useContractListService.ts'
import mitt from '@/utils/eventsBus'

const tipsVisible = ref(false)
const { userId, loadRecents, recentDatas, loading, delReviewInfo, getReviewInfo, gotoReviewList } =
  useContractListService()
const closeTips = () => {
  tipsVisible.value = false
  localStorage.setItem(`review_tips_${userId.value}`, JSON.stringify(tipsVisible.value))
}
const tipsState = (val?: boolean) => {
  if (typeof val != 'undefined') {
    localStorage.setItem(`review_tips_${userId.value}`, JSON.stringify(val))
  } else {
    return localStorage.getItem(`review_tips_${userId.value}`)
  }
}

const showTips = () => {
  const hasReviewTips = tipsState()

  if (hasReviewTips != null) {
    // tips 状态值存在（可能是 true 或 false）
    tipsVisible.value = hasReviewTips == 'true' ? true : false
  } else {
    // tips 状态值不存在（undefined/null）
    tipsVisible.value = true // 默认显示提示信息
  }
}

// 切换企业的时候需要用到 以及退出登录的时候
mitt.on('refresh-contract-list', () => {
  if (userId.value) {
    nextTick(() => {
      // recentDatas.value = []
      loadRecents()
    })
  } else {
    recentDatas.value = []
  }
})
let timer: string | number | NodeJS.Timeout
onMounted(() => {
  loadRecents()
  showTips()
  timer = setInterval(() => {
    loadRecents()
  }, 30000)
})
onUnmounted(() => {
  clearInterval(timer)
  // 近期列表数据为空时，不修改 tips 状态
  if (recentDatas.value.length > 0) {
    tipsVisible.value = false
    tipsState(tipsVisible.value)
  }
})
</script>
<template>
  <div v-if="recentDatas.length" class="recent-wrap">
    <div class="recent-title">
      <span>{{ $t('合同列表') }}</span>
      <el-link underline="never" style="color: var(--is-color-773bef)" @click="gotoReviewList">{{
        $t('查看更多')
      }}</el-link>
    </div>
    <div class="recent-list">
      <DataTable
        :data="recentDatas"
        :loading="loading"
        @view="getReviewInfo"
        tableHeight="18.75rem"
        @del="
          (id) => {
            delReviewInfo(id, 'recent')
          }
        "
      />
      <div v-if="tipsVisible" class="tips-txt">
        <el-card>
          <p>{{ $t('无需等待文件完全处理，可点击查看详情') }}</p>
          <span @click="closeTips">{{ $t('我知道了') }}</span>
        </el-card>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.recent-wrap {
  width: 100%;
  margin-top: 48px;
  .recent-title {
    position: relative;
    display: flex;
    align-items: center;
    height: 22px;
    &::before {
      position: absolute;
      top: 3px;
      left: 0;
      width: 2px;
      height: 16px;
      content: '';
      background: linear-gradient(356.12deg, var(--main-font) 0.32%, var(--main-bg) 97.58%);
      border-radius: 2px;
    }
    span {
      padding: 0 12px 0 8px;
      font-size: 16px;
      font-weight: 500;
      line-height: 100%;
      color: #262626;
      text-align: center;
      letter-spacing: 0;
    }
  }
  .recent-list {
    position: relative;
    .tips {
      margin-right: 10px;
    }
    .tips-txt {
      position: absolute;
      top: -3.45rem;
      right: 9.375rem;
      z-index: 9;
      line-height: 22px;
      :deep(.el-card__body) {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        justify-content: space-between;
        width: 17.875rem;
        height: 6.375rem;
        padding: 1rem;
        color: var(--bg-color);
        background: linear-gradient(291.96deg, var(--main-font) 18.03%, var(--main-font) 88.73%);
      }
      p {
        width: 100%;
        font-size: 0.875rem;
        color: var(--bg-color);
        text-align: left;
      }
      span {
        display: inline-block;
        width: 5.5rem;
        height: 2rem;
        font-size: 0.875rem;
        line-height: 2rem;
        color: var(--is-color-773bef);
        text-align: center;
        cursor: pointer;
        background-color: var(--bg-color);
        border-radius: 0.25rem;
      }
      &::after {
        position: absolute;
        right: 15px;
        bottom: -8px;
        z-index: 9;
        width: 0;
        height: 0;
        margin-left: -10px; /* 半宽度偏移，确保三角形居中 */
        content: '';
        border-top: 10px solid var(--main-font); /* 颜色可以自定义  */
        border-right: 10px solid transparent;
        border-left: 10px solid transparent;
      }
    }
  }
}
:deep(.el-table__inner-wrapper::before) {
  background-color: var(--bg-color) !important;
}
</style>
