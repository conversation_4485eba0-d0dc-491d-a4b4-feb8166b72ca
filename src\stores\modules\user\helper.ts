import type { IUserInfo } from '@/types/user'

export class UserInfo implements IUserInfo {
  userId: string
  userName: string
  userStatus: string
  isPassword: boolean
  nickName: string
  avatarUrl: string
  corpList: Array<{
    corpId: string
    corpName: string
    corpLogo: string
    isDefault: boolean
  }>
  memberInfo?: {
    corp: {
      corpName: string
    }
  }
  constructor() {
    this.userId = ''
    this.userName = ''
    this.userStatus = ''
    this.isPassword = false
    this.nickName = ''
    this.avatarUrl = ''
    this.memberInfo = undefined
    this.corpList = []
  }
}
