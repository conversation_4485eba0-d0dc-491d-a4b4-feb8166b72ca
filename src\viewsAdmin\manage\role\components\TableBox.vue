<script setup lang="ts">
import { $t } from '@/utils/i18n'
import SaveRoleDialog from './SaveRoleDialog.vue'
import { queryPageRoleList, deleteRole } from '@/services/manage/index'
import type { IQueryPageRoleList, IRoleQueryData, IList } from '@/services/manage/index'
import { RESPONSE_CODE_SUCCESS } from '@/constants'

const emits = defineEmits(['editRole'])
const saveRoleDialogRef = ref<InstanceType<typeof SaveRoleDialog>>()

const loading = ref(false)
const page = ref(1)
const pageSize = ref(10)
const count = ref(0)

const tableData = ref<IList[]>()

// 分页获取角色列表
const getPageRoleList = async (searchParams?: IRoleQueryData) => {
  loading.value = true
  try {
    const params = {
      page: page.value,
      pageSize: pageSize.value,
      ...searchParams,
    }
    const { data } = await queryPageRoleList<IQueryPageRoleList>(params)
    tableData.value = data.list || []
    page.value = data.pageNum
    pageSize.value = data.pageSize
    count.value = +data.total
  } catch (err) {
    console.log('获取角色列表失败', err)
  } finally {
    loading.value = false
  }
}

// 权限设置
const editRole = (row: IList) => {
  saveRoleDialogRef.value?.openDialog(row)
}

// 删除角色
const removeRole = (id: string) => {
  ElMessageBox.confirm($t('确定要删除该角色吗？'), $t('提示'), {
    confirmButtonText: $t('确认'),
    cancelButtonText: $t('取消'),
  }).then(async () => {
    // 调用删除接口
    const trash = [id]
    try {
      const res = await deleteRole(trash)
      if (res.code === RESPONSE_CODE_SUCCESS) {
        await getPageRoleList()
        ElMessage.error($t('删除成功'))
      }
    } catch (err) {
      console.error('删除失败', err)
    }
  })
}

const updateTable = async () => {
  await getPageRoleList()
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  page.value = 1
  getPageRoleList()
}
const handleCurrentChange = (val: number) => {
  page.value = val
  getPageRoleList()
}

// 初始化
onMounted(() => {
  getPageRoleList()
})

defineExpose({
  getPageRoleList,
})
</script>

<template>
  <div class="table-body">
    <el-table :data="tableData" row-key="id" v-loading="loading" max-height="calc(100vh - 210px)">
      <el-table-column prop="roleName" :label="$t('角色名称')" :show-overflow-tooltip="true" />
      <el-table-column prop="menuNameList" :label="$t('功能权限')" :show-overflow-tooltip="true" min-width="180">
        <template #default="scope">
          {{ scope.row.menuNameList ? scope.row.menuNameList.join('；') : '' }}
        </template>
      </el-table-column>
      <!-- <el-table-column prop="roleRemark" :label="$t('备注')" :show-overflow-tooltip="true" min-width="150" /> -->
      <el-table-column prop="updateTime" :label="$t('更新时间')" />
      <el-table-column :label="$t('操作')" width="150">
        <template #default="scope">
          <el-button type="primary" link @click="editRole(scope.row)">{{ $t('编辑') }}</el-button>
          <el-button type="danger" link @click="removeRole(scope.row.id)">{{ $t('删除') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <div class="table-footer" v-show="tableData && tableData.length > 0">
    <el-pagination
      v-model:current-page="page"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 30, 40]"
      :total="count"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
  <SaveRoleDialog ref="saveRoleDialogRef" @updateList="updateTable"></SaveRoleDialog>
</template>

<style scoped lang="scss">
////////// 表格高度自适应待处理
.table-body {
  flex: 1;
  min-height: 0;
  overflow: auto;
}
.table-footer {
  flex-shrink: 0;
}
</style>
