<template>
  <div class="upload-progress-viwe">
    <div class="default-view" v-show="status == 1">
      <div class="animate">
        <div v-if="textType == 1" class="progress-info-line" :class="{ loop: status == 1 }">
          {{ $t('数据正在上传,请稍后…') }}
        </div>
        <div v-if="textType == 2" class="normal-info" :class="{ loop: status == 1 }">
          {{ $t('数据上传完毕，处理中...') }}
        </div>
      </div>
      <div class="progress-bar">
        <el-progress :percentage="value" :show-text="false"></el-progress>
      </div>
    </div>
    <div class="success-view" v-show="status == 2">
      <span class="el-icon-success tip-icon success"></span>
      <span class="normal">{{ $t('数据上传完成。') }}</span>
    </div>
    <div class="warn-view" v-show="status == 3">
      <span class="el-icon-warning tip-icon wraning"></span>
      <span class="normal">{{ $t('部分数据上传完成。') }}</span>
      <el-button type="text" icon="el-icon-download" @click.stop="emit('download')">{{ $t('下载错误数据') }}</el-button>
    </div>
    <div class="error-view" v-show="status == 4">
      <span class="el-icon-error tip-icon error"></span>
      <span class="normal">{{ $t('导入失败。') }}</span>
      <el-button type="text" icon="el-icon-refresh-right" @click.stop="emit('upload')">{{ $t('重新上传') }}</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  status?: string | number
  value?: number
  textType?: number
}

const emit = defineEmits<{
  download: []
  upload: []
}>()

withDefaults(defineProps<Props>(), {
  status: 1,
  value: 0,
  textType: 1,
})
</script>
<style lang="scss" scoped>
.upload-progress-viwe {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  .animate {
    width: 100%;
    padding-left: 80px;
  }
  .progress-info-line {
    display: inline-block;
    width: 130px;
    overflow-x: hidden;
    color: #2a2b2c;
    white-space: nowrap;
    &.loop {
      animation: loop1 1s infinite;
    }
  }
  .normal-info {
    display: inline-block;
    width: 140px;
    overflow-x: hidden;
    color: #2a2b2c;
    white-space: nowrap;
    &.loop {
      animation: loop2 1s infinite;
    }
  }
  .progress-bar {
    width: 316px;
    padding-top: 16px;
  }
  .tip-icon {
    position: relative;
    top: 1.5px;
    margin-right: 4px;
    font-size: 16px;
    &.wraning {
      color: #ffc600;
    }
    &.success {
      color: #1fac78;
    }
    &.error {
      color: #f85951;
    }
  }
  .normal {
    font-size: 12px;
    color: #2a2b2c;
  }
}

@keyframes loop1 {
  0% {
    width: 130px;
  }
  25% {
    width: 135px;
  }
  50% {
    width: 140px;
  }
  75% {
    width: 145px;
  }
  100% {
    width: 150px;
  }
}

@keyframes loop2 {
  0% {
    width: 140px;
  }
  25% {
    width: 145px;
  }
  50% {
    width: 150px;
  }
  75% {
    width: 155px;
  }
  100% {
    width: 160px;
  }
}
</style>
