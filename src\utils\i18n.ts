import { useI18n } from 'vue-i18n'

/**
 * 全局翻译函数
 * 可以在任何地方使用，包括非Vue组件中
 */
let globalT: any = null

/**
 * 初始化全局翻译函数
 * 在Vue组件中调用此函数来设置全局翻译
 */
export function initGlobalT() {
  if (!globalT) {
    const { t } = useI18n()
    globalT = t
    // 挂载到全局对象上
    if (typeof window !== 'undefined') {
      ;(window as any).$t = t
    }
  }
  return globalT
}

/**
 * 全局翻译函数
 * 在非Vue组件中使用
 */
export function $t(key: string, ...args: any[]): string {
  if (!globalT) {
    console.warn('Global translation function not initialized. Please call initGlobalT() first.')
    return key
  }
  return globalT(key, ...args)
}

/**
 * 获取翻译函数
 * 在Vue组件中使用
 */
export function useGlobalT() {
  const { t } = useI18n()
  
  // 初始化全局翻译函数
  if (!globalT) {
    globalT = t
    if (typeof window !== 'undefined') {
      ;(window as any).$t = t
    }
  }
  
  return { t, $t: t }
}

// 默认导出翻译函数
export default $t
