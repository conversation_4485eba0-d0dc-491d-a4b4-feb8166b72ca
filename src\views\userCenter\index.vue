<script setup lang="ts">
import { ref, computed } from 'vue'
import { useUserStore } from '@/stores'
import { CopyRight, RESPONSE_CODE_SUCCESS } from '@/constants'
import { useAccountService } from '@/composables/useAccountService'
import UserApi from '@/services/user'
import type { IUserInfo } from '@/types/user'
import { remToPx } from '@/utils'

import { $t } from '@/utils/i18n'
const userStore = useUserStore()
const { getChangeManagerCode, deregisterAccount, codeBtnName, verificationCode, identityAuthenticationVisible } =
  useAccountService()
const logoutVisible = ref(false)
const deregisterAccountVisible = ref(false)
const agreementVisible = ref(false)
const agreementChecked = ref(false)
const contactVisible = ref(false)
const editUserNameVisible = ref(false)
const nickName = ref('')

const userInfo = computed(() => {
  return userStore.userInfo
})

const deregisterAccountText = computed(() => {
  return `确认注销后，将删除或匿名化处理该账号下的数据，一旦注销将无法恢复，请在申请注销前做好备份。具体注销程序，包括但不限于：

            1. 解绑手机号；
            2. 删除或匿名化对话、合同等数据；`
})

const closeDeregisterAccountDialog = () => {
  deregisterAccountVisible.value = false
  agreementChecked.value = false
}

const closeEditUserName = () => {
  editUserNameVisible.value = false
  nickName.value = ''
}

const agreeAgreement = () => {
  agreementVisible.value = false
  agreementChecked.value = true
}

const confirmDeRegisterAccount = () => {
  if (agreementChecked.value) {
    agreementVisible.value = false
    deregisterAccountVisible.value = false
    identityAuthenticationVisible.value = true
  } else {
    agreementVisible.value = true
  }
}

const logOut = async () => {
  try {
    const { code } = await UserApi.loginOut()
    if (code === RESPONSE_CODE_SUCCESS) {
      userStore.initUserInfo()
      logoutVisible.value = false
      ElMessage.success($t('退出登录成功'))
    } else {
      ElMessage.error($t('退出登录失败，请稍后再试'))
    }
  } catch (error: any) {
    ElMessage.error($t('退出登录失败，请稍后再试'))
  }
}

//noopener 防止钓鱼攻击
const openNewTab = (url: string) => {
  window.open(url, '_blank', 'noopener,noreferrer')
}

const commitNickName = async () => {
  if (nickName.value.trim() === '') {
    ElMessage.warning($t('请输入用户名称！'))
    return
  }
  // 修改正则表达式，排除特殊字符 /\:*?<>|"
  const regex = /^[^\\/\:*?？<>|"]{1,20}$/
  if (!regex.test(nickName.value)) {
    ElMessage.warning('用户名称不可包含特殊字符/\:*?<>|')
    return
  }
  try {
    const { code, data } = await UserApi.editNickName<IUserInfo>(nickName.value)
    if (code === RESPONSE_CODE_SUCCESS) {
      ElMessage.success($t('修改成功'))
      userStore.editNickName(data.nickName)
      nickName.value = ''
      editUserNameVisible.value = false
    } else {
      ElMessage.warning($t('修改失败，请稍后再试'))
    }
  } catch (error: any) {
    ElMessage.error(error.message || $t('修改失败，请稍后再试'))
  }
}
</script>

<template>
  <div class="personal-center-container">
    <p class="personal-center-container-title">{{ $t('个人中心') }}</p>
    <div class="personal-center-content">
      <div class="personal-center-content-item account-setting-container">
        <div class="setting-title-bar">
          <div class="setting-title">{{ $t('账号设置') }}</div>
        </div>
        <div class="setting-content">
          <div
            class="setting-item avatar-container"
            @click="
              () => {
                editUserNameVisible = true
              }
            "
          >
            <div class="avatar">
              {{ userInfo.nickName ? userInfo.nickName[0] : 'i' }}
            </div>
            <div v-if="userInfo.nickName" class="nick-name">
              {{ userInfo.nickName ? userInfo.nickName : 'iterms' }}
            </div>
            <i class="iconfont arrow-right-icon">&#xe656;</i>
          </div>
          <div class="setting-item info-item">
            <div class="info-label">{{ $t('手机号') }}</div>
            <div class="phone-num">{{ userInfo.userName }}</div>
          </div>
        </div>
      </div>
      <div class="personal-center-content-item about-product-container">
        <div class="setting-title-bar">
          <div class="setting-title">{{ $t('关于产品') }}</div>
        </div>
        <div class="setting-content">
          <div class="setting-item about" @click="openNewTab(`${CopyRight.service}`)">
            <div>{{ $t('服务协议') }}</div>
            <i class="iconfont arrow-right-icon">&#xe656;</i>
          </div>
          <div class="setting-item about" @click="openNewTab(`${CopyRight.privite}`)">
            <div>{{ $t('隐私政策') }}</div>
            <i class="iconfont arrow-right-icon">&#xe656;</i>
          </div>
          <div class="setting-item about" @click="openNewTab(`${CopyRight.collect}`)">
            <div>{{ $t('第三方信息共享清单') }}</div>
            <i class="iconfont arrow-right-icon">&#xe656;</i>
          </div>
          <div class="setting-item about" @click="contactVisible = true">
            <div>{{ $t('联系我们') }}</div>
            <i class="iconfont arrow-right-icon">&#xe656;</i>
          </div>
        </div>
      </div>

      <div class="logout-button" @click="logoutVisible = true">{{ $t('退出登录') }}</div>
      <div class="deregister-account-button">
        <p @click="deregisterAccountVisible = true">{{ $t('注销账号') }}</p>
      </div>
    </div>
    <el-dialog
      v-model="logoutVisible"
      :title="$t('确认退出登录')"
      :width="remToPx('28.125rem')"
      @close="logoutVisible = false"
    >
      <p class="dialog-content-text">{{ $t('退出登录不会丢失任何数据，你仍可以登录此账号') }}</p>
      <template #footer>
        <el-button type="primary" size="small" class="cancel-button" @click="logoutVisible = false">{{
          $t('取消')
        }}</el-button>
        <el-button type="primary" size="small" class="confirm-button" @click="logOut">{{ $t('退出登录') }}</el-button>
      </template>
    </el-dialog>
    <el-dialog
      v-model="deregisterAccountVisible"
      :title="$t('注销账号须知')"
      :width="remToPx('28.125rem')"
      @close="closeDeregisterAccountDialog"
    >
      <p class="dialog-content-text deregister-account-text">
        {{ deregisterAccountText }}
      </p>
      <el-button type="primary" size="small" class="confirm-deregister-account" @click="confirmDeRegisterAccount">{{
        $t('确认注销')
      }}</el-button>
      <div class="deregister-account-agreement">
        <el-checkbox v-model="agreementChecked" size="default"></el-checkbox>
        <p class="deregister-account-agreement-text">
          {{ $t('我已阅读并同意') }}
          <el-link @click="openNewTab(`${CopyRight.deregister}`)" underline="never"
            >《{{ $t('账号注销协议') }}》</el-link
          >
        </p>
      </div>
    </el-dialog>
    <el-dialog
      v-model="agreementVisible"
      :title="$t('服务协议及隐私保护')"
      :width="remToPx('28.125rem')"
      @close="agreementVisible = false"
    >
      <div class="dialog-content">
        <p class="dialog-content-text">{{ $t('我已阅读并同意') }}《{{ $t('账号注销协议') }}》</p>
      </div>
      <template #footer>
        <el-button type="primary" size="small" class="cancel-button" @click="agreementVisible = false">{{
          $t('不同意')
        }}</el-button>
        <el-button type="primary" size="small" class="confirm-button" @click="agreeAgreement">{{
          $t('同意')
        }}</el-button>
      </template>
    </el-dialog>
    <el-dialog
      v-model="identityAuthenticationVisible"
      :title="$t('身份认证')"
      :width="remToPx('28.125rem')"
      @close="identityAuthenticationVisible = false"
    >
      <p class="dialog-content-text">{{ $t('为了您的账号安全，请验证身份。验证成功后进行下一步操作') }}</p>
      <el-input v-model="userInfo.userName" class="phone-num-text" :readonly="true" :clearable="false" />
      <el-input
        v-model="verificationCode"
        class="verify-code"
        :placeholder="$t('请输入验证码')"
        :clearable="false"
        maxlength="6"
      >
        <template #suffix>
          <span class="send_code_btn" @click="getChangeManagerCode">{{ codeBtnName }}</span>
        </template>
      </el-input>
      <template #footer>
        <el-button type="primary" size="small" class="cancel-button" @click="identityAuthenticationVisible = false">{{
          $t('取消')
        }}</el-button>
        <el-button type="primary" size="small" class="confirm-button" @click="deregisterAccount">{{
          $t('验证')
        }}</el-button>
      </template>
    </el-dialog>
    <el-dialog v-model="contactVisible" :title="$t('联系我们')" width="400" @close="contactVisible = false">
      <div class="dialog-content">
        <img
          class="contact-us-img"
          src="https://cdn.fadada.com/dist/static/c/39/20250528161527_d0226c0c-c9c5-4a3e-bcd5-8e218d8a1d1c.png"
        />
        <p class="contact-us-text">{{ $t('扫一扫上面二维码图案，联系我们') }}</p>
      </div>
    </el-dialog>
    <el-dialog v-model="editUserNameVisible" :title="$t('修改用户名')" width="500" @close="closeEditUserName">
      <div class="edit-user-name-content">
        <el-input v-model="nickName" :placeholder="$t('用户名称不可包含特殊字符/\:*?<>|')" clearable maxlength="20" />
      </div>
      <template #footer>
        <el-button @click="closeEditUserName">{{ $t('取消') }}</el-button>
        <el-button type="primary" @click="commitNickName">{{ $t('确定') }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .contact-us-img {
    width: 9.625rem;
    height: 9.625rem;
  }
  .contact-us-text {
    margin-top: 1.5rem;
    font-size: 0.875rem;
    line-height: 1.375rem;
    color: var(--is-color-7d7b89);
    text-align: center;
  }
}
.personal-center-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 50rem;
  height: fit-content;
  margin: 0 auto;
  color: var(--minor-font);
  .personal-center-container-title {
    width: 100%;
    margin: 0 auto;
    font-size: 1.125rem;
    font-weight: 500;
    line-height: 56px;
    color: #262626;
    text-align: center;
  }
  .personal-center-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 75rem;
    margin: 0 auto;
    .arrow-right-icon {
      margin-right: 0;
      margin-left: auto;
      font-size: 1.5rem;
      color: var(--sidebar-line);
      cursor: pointer;
    }
    .account-setting-container {
      padding-top: 1.5rem;
    }
    .about-product-container {
      margin-top: 1.5rem;
    }
    .setting-title-bar {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      text-align: left;
      .setting-title {
        font-size: 1rem;
        font-weight: 500;
        line-height: 1.5rem;
        color: var(--minor-font);
      }
    }
    .setting-content {
      display: flex;
      flex-direction: column;
      width: 100%;
      margin-top: 1rem;
      color: var(--minor-font);
      background-color: #f9f9fb;
      border-radius: 0.5rem;
      .setting-item {
        display: flex;
        align-items: center;
        padding: 1rem;
        border-bottom: 1px solid #e1e5ea;
      }
      .setting-item:last-of-type {
        border-bottom: none;
      }
      .avatar-container {
        .avatar {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 3.5rem;
          height: 3.5rem;
          font-size: 1.25rem;
          font-weight: 500;
          color: #31226e;
          cursor: pointer;
          background-color: #ded5fb;
          border-radius: 50%;
        }
        .nick-name {
          margin-left: 1.5rem;
          font-size: 0.875rem;
          font-weight: 500;
          line-height: 1.375rem;
        }
      }
      .info-item {
        height: 3rem;
      }
      .info-label {
        font-size: 0.875rem;
        font-weight: 400;
      }
      .phone-num {
        margin-left: 2.375rem;
        font-size: 0.875rem;
      }
      .pwd-status {
        margin-left: 1.5rem;
      }
      .pwd-setting {
        margin-right: 1rem;
        margin-left: auto;
        color: #3348ff;
        cursor: pointer;
      }
      .about {
        cursor: pointer;
      }
    }
    .logout-button {
      width: 100%;
      padding: 1rem 0;
      margin-top: 5rem;
      font-size: 1rem;
      color: #f32051;
      text-align: center;
      cursor: pointer;
      background-color: #f9f9fb;
      border-radius: 0.5rem;
    }
    .deregister-account-button {
      width: 100%;
      padding: 1rem 0;
      color: var(--is-color-7d7b89);
      p {
        width: fit-content;
        margin: 0 auto;
        font-size: 1rem;
        cursor: pointer;
      }
    }
  }
}
.cancel-button {
  color: #000000e3 !important;
  background: var(--bg-color) !important;
  border: 1px solid #00021a14;
  border-radius: 0.25rem;
}
.confirm-button {
  margin-left: 0.75rem;
  border-radius: 0.25rem;
}
.dialog-content-text {
  font-size: 0.875rem;
  color: var(--is-color-7d7b89);
}
.deregister-account-text {
  white-space: pre-line;
  scrollbar-width: none;
}
.confirm-deregister-account {
  width: 100%;
  height: 3rem;
  margin-top: 1rem;
  font-size: 1.125rem;
  font-weight: bold;
  line-height: 3rem;
  border-radius: 0.25rem;
}
.deregister-account-agreement {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 1rem;
  .deregister-account-agreement-text {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 1.375rem;
    margin-left: 0.5rem;
    font-size: 0.875rem;
    line-height: 1.375rem;
    color: var(--sidebar-line);
  }
}
.phone-num-text {
  width: 100%;
  padding-left: 1rem;
  margin-top: 1rem;
  font-size: 0.875rem;
  color: var(--is-color-7d7b89) !important;
}
.verify-code {
  width: 100%;
  padding-left: 1rem;
  margin-top: 1rem;
  font-size: 0.875rem;
  .send_code_btn {
    color: var(--iterms-band-main-color);
    cursor: pointer;
  }
}
</style>
