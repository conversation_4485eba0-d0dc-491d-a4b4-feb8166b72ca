<script lang="ts" setup>
import { createCustomRule, editCustomRule, queryCustomRuleById } from '@/services/contract'
import { type FormInstance } from 'element-plus'
import { $t } from '@/utils/i18n'
const visible = ref(false)
const refForm = ref<FormInstance>()
const emit = defineEmits(['create', 'edit'])
const rules = {
  ruleName: [{ required: true, message: $t('请输入审查项名称'), trigger: 'blur' }],
  riskJudgePrompts: [{ required: true, message: $t('请输入审查规则'), trigger: 'blur' }],
}
interface ICustomRule {
  riskJudgePrompts: string
  ruleCustomId?: string
  ruleName: string
  ruleCode?: string
}
const ruleCode = ref('')
const formData = ref<ICustomRule>({
  ruleName: '',
  riskJudgePrompts: '',
})

async function getCumstomId(obj: { id: string }) {
  const { data, message } = await queryCustomRuleById(obj.id)
  if (!data) {
    ElMessage.error(message)
    return
  }
  const { ruleName, riskJudgePrompts } = data as ICustomRule
  formData.value = { ruleName: ruleName, riskJudgePrompts: riskJudgePrompts, ruleCustomId: obj.id }
}

function submit() {
  refForm.value!.validate(async (valid) => {
    if (valid) {
      // 请求接口
      if (!formData.value.ruleCustomId) {
        const { code, data, message } = await createCustomRule(
          ruleCode.value,
          formData.value.ruleName,
          formData.value.riskJudgePrompts,
        )

        if (code !== '000000') {
          ElMessage.error(message)
          return
        }
        emit('create', { id: data, label: formData.value.ruleName })
      } else {
        const { code, message, data } = await editCustomRule(
          formData.value.ruleCustomId,
          formData.value.ruleName,
          formData.value.riskJudgePrompts,
        )
        if (code !== '000000') {
          ElMessage.error(message)
          return
        }
        console.log(formData.value)
        emit('edit', { id: formData.value.ruleCustomId, label: formData.value.ruleName })
      }

      close()
    }
  })
}

const show = (code: string, data: any) => {
  visible.value = true
  ruleCode.value = code
  if (data) {
    getCumstomId(data)
  }
}

defineExpose({
  show,
})
function close() {
  visible.value = false
  formData.value = {
    ruleName: '',
    riskJudgePrompts: '',
    ruleCustomId: '',
  }
  refForm.value!.resetFields()
  refForm.value!.clearValidate()
}
</script>
<template>
  <el-dialog
    :title="formData.ruleCustomId ? $t('编辑审查项') : $t('新增审查项')"
    v-model="visible"
    :append-to-body="true"
    :close-on-click-modal="false"
    width="600px"
    heigh="436px"
    @close="close"
  >
    <el-form ref="refForm" :model="formData" :rules="rules" class="form-wrap">
      <el-form-item :label="$t('审查项名称')" prop="ruleName">
        <el-input v-model="formData.ruleName" :placeholder="$t('请输入')"></el-input>
      </el-form-item>

      <el-form-item style="height: 20px">
        <div class="rulehandle-wrap">
          <el-tooltip class="item" effect="dark" placement="top">
            <template #content>
              {{ $t('审查合同是否约定了卖方的收款账户信息，包括银行账户名、开户行名称、银行账号三项信息。') }}<br />{{
                $t('若前述三项信息均未约定或未全部约定，请提示风险；若未约定，则无风险。')
              }}
            </template>
            <div style="height: 20px; color: var(--primary-color)">{{ $t('查看 “收款账户信息缺失” 示例') }}</div>
          </el-tooltip>
        </div>
      </el-form-item>
      <el-form-item :label="$t('审查规则')" prop="riskJudgePrompts">
        <el-input
          v-model="formData.riskJudgePrompts"
          :placeholder="$t('请输入')"
          type="textarea"
          show-word-limit
          :maxlength="200"
          :autosize="{ minRows: 4, maxRows: 6 }"
        ></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <div class="footer-wrap">
        <el-button @click="close">{{ $t('取消') }}</el-button>
        <el-button type="primary" @click="submit">{{ $t('确定') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<style lang="scss" scoped>
.rulehandle-wrap {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  height: 1.25rem;
  .rulehandle-text {
    margin-right: 24px;
    font-size: 12px;
  }
}
.footer-wrap {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}
:deep(.el-dialog__body) {
  padding-right: 32px;
}
</style>
