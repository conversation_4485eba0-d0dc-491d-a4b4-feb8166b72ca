import { i18nLang, i18nTranslate } from './translateUtil.js'

/**
 * @description: 翻译
 * @param {String} dir/需要翻译的目录或文件
 * @param {String} zh/需写入的中文json文件路径
 * @param {String} en/需写入的英文json文件路径
 * @return {type}
 */
// var filePath = path.resolve('src/views');
// var filePath = path.resolve('src/layouts');
// var filePath = path.resolve('src/components');
const translate = async (dir, zh, en, hk) => {
  await i18nLang(dir, zh)
  await i18nTranslate(zh, en)
  if (hk) {
    await i18nTranslate(zh, hk, 'hk')
  }
}

// 同時生成英文和香港繁體
translate('src', 'src/lang/zh.json', 'src/lang/en.json', 'src/lang/zh-HK.json')
