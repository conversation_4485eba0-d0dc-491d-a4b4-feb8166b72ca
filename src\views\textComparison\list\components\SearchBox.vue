<script setup lang="ts">
import type { IStatusOption, ISearchArgs } from '../types'
interface IProps {
  searchArgs: ISearchArgs
  statusOption: IStatusOption[]
}
const props = defineProps<IProps>()
const emits = defineEmits(['search', 'resetSearch', 'update:searchArgs'])

const searchData = computed({
  get: () => props.searchArgs,
  set: (val: ISearchArgs) => {
    emits('update:searchArgs', val)
  },
})

function handleReset() {
  emits('resetSearch')
}
function handleSearch() {
  console.log('props.searchArgs', searchData.value)
  emits('search')
}
</script>

<template>
  <div class="search-wrap">
    <div class="condition">
      <span class="condition-label">{{ $t('文件名称') }}</span>
      <el-input v-model="searchData.contractName" @keyup.enter="handleSearch" clearable :placeholder="$t('请输入')" />
    </div>
    <div class="condition">
      <span class="condition-label">{{ $t('完成时间') }}</span>
      <el-date-picker
        :default-time="[new Date(0, 0, 0, 0, 0, 0), new Date(0, 0, 0, 23, 59, 59)]"
        v-model="searchData.dateRange"
        type="daterange"
        :range-separator="$t('至')"
        :start-placeholder="$t('开始日期')"
        :end-placeholder="$t('结束日期')"
        value-format="YYYY-MM-DD HH:mm:ss"
        clearable
        @change="handleSearch"
        @clear="handleSearch"
      >
      </el-date-picker>
    </div>
    <div class="condition">
      <span class="condition-label">{{ $t('比对状态') }}</span>
      <!-- @clear="handleSearch" -->
      <el-select v-model="searchData.status" :placeholder="$t('请选择')" clearable @change="handleSearch">
        <el-option v-for="item in statusOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
    </div>
    <div class="btn-wrap">
      <el-button type="primary" @click.stop="handleSearch">{{ $t('查 询') }}</el-button>
      <el-button @click="handleReset" class="secondary">{{ $t('重 置') }}</el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.search-wrap {
  display: flex;
  flex-wrap: wrap;
  row-gap: 16px;
  padding: 16px 0;
  background-color: var(--bg-color);
  .condition {
    display: flex;
    align-items: center;
    width: 33.3%;
    &-label {
      width: 90px;
      padding-left: 12px;
      font-size: 14px;
      color: #606266;
    }
    .el-input {
      width: 100%;
    }
    .el-date-editor {
      width: 100% !important;
    }
    .el-select {
      width: 100%;
    }
    .el-cascader {
      width: 100%;
    }
  }
  .btn-wrap {
    display: flex;
    flex: 1;
    justify-content: flex-start;
    padding-left: 12px;
  }
}
</style>
