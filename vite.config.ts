import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import { viteMockServe } from 'vite-plugin-mock'
import { codeInspectorPlugin } from 'code-inspector-plugin'
import { viteStaticCopy } from 'vite-plugin-static-copy'
import postCssPxToRem from 'postcss-pxtorem'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import path from 'path'
// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')
  // 从环境变量中读取是否开启 code inspector
  const needCodeInspector = env.VITE_CODE_INSPECTOR_ENABLED === 'true'

  return {
    plugins: [
      vue(),
      AutoImport({
        imports: [{ 'element-plus': ['ElMessage'] }, 'vue', 'vue-router'],
        dts: './auto-imports.d.ts',
        resolvers: [ElementPlusResolver({ importStyle: 'sass' })],
      }),
      Components({
        resolvers: [ElementPlusResolver({ importStyle: 'sass' })],
      }),
      viteMockServe({
        mockPath: 'mock', // mock文件夹路径
        enable: mode === 'development', // 只有开发环境才开启mock
      }),
      viteStaticCopy({
        targets: [
          {
            src: './node_modules/@iterms/pdfjs/generic-legacy/*',
            dest: './pdf-viewer/',
          },
        ],
      }),
      needCodeInspector && codeInspectorPlugin({ bundler: 'vite' }),
      createSvgIconsPlugin({
        iconDirs: [path.resolve(process.cwd(), 'src/assets/icons/svg')],
        symbolId: 'icon-[dir]-[name]',
        customDomId: 'svgIconsId',
      }),
    ],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
      },
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "@/styles/element.scss" as *;`,
        },
      },
      postcss: {
        plugins: [
          postCssPxToRem({
            // 自适应，px>rem转换
            propList: ['*'], // 除 border 外所有px 转 rem // 需要转换的属性，这里选择全部都进行转换
            // selectorBlackList: ['.norem'],// 过滤掉norem-开头的class，不进行rem转换，这个内容可以不写
            unitPrecision: 5, // 转换后的精度，即小数点位数
            replace: true, // 是否直接更换属性值而不添加备份属性
            mediaQuery: false, // 是否在媒体查询中也转换px为rem
            minPixelValue: 0, // 设置要转换的最小像素值
          }),
        ],
      },
    },
    build: {
      chunkSizeWarningLimit: 1000,
      rollupOptions: {
        output: {
          manualChunks: {
            'element-plus': ['element-plus'],
          },
        },
      },
      sourcemap: mode === 'development',
    },
    server: {
      port: 5200,
      host: '0.0.0.0',
      proxy: {
        '/api/bff-iterms-saas': {
          target: 'https://dev-ai.iterms.com/',
          // target: 'http://************:8080/', // 权限管理 - 本地调试
          changeOrigin: true,
          pathRewrite: {
            '^/': '/',
          },
        },
      },
    },
  }
})
