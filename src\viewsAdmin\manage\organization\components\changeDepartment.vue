<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    :close-on-click-modal="false"
    width="480px"
    :show-close="true"
    @close="closeDialog"
  >
    <el-form
      :model="form"
      @submit.prevent
      ref="ruleForm"
      :label-position="'left'"
      label-width="60px"
      class="demo-ruleForm custom-dialog-form"
    >
      <el-form-item :label="$t('移入部门')" prop="orgIdList">
        <el-cascader
          style="width: 100%"
          v-model="form.orgIdList"
          :options="orgSelectList"
          :props="defaultProps"
          clearable
        ></el-cascader>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click.stop="closeDialog">{{ $t('取消') }}</el-button>
        <el-button type="primary" :disabled="form.orgIdList.length == 0" :loading="loading" @click.stop="submit">{{
          $t('确定')
        }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
// import { moveOrgUser } from '@/services/manage/organization'
import { useOrganizationStore } from '@/stores'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'

import { $t } from '@/utils/i18n'
// Store
const organizationStore = useOrganizationStore()

// 响应式数据
const dialogTitle = ref($t('移动人员'))
const visible = ref(false)
const loading = ref(false)
const currentOrgId = ref<number | null>(null)
const userCodeList = ref<string[]>([])

const defaultProps = reactive({
  children: 'subList',
  label: 'orgName',
  value: 'id',
  multiple: true,
  checkStrictly: true,
})

const form = reactive({
  orgIdList: [] as any[],
})

const rules = reactive({
  orgIdList: [{ required: true, message: $t('请选择要移入部门'), trigger: 'change' }],
})

// 模板引用
const ruleForm = ref<FormInstance>()

// 计算属性
const orgSelectList = computed(() => organizationStore.orgSelectList)

// 定义emits
const emit = defineEmits(['succ'])

// 方法
const closeDialog = () => {
  visible.value = false
  currentOrgId.value = null
  userCodeList.value = []
  form.orgIdList = []
  nextTick(() => {
    ruleForm.value?.resetFields()
  })
}

const openDialog = ({ orgId, userCodeList: userCodes }: { orgId: number; userCodeList: string[] }) => {
  // organizationStore.searchOrgSelect()
  currentOrgId.value = orgId
  userCodeList.value = userCodes
  visible.value = true
}

const submit = () => {
  if (loading.value) return false
  loading.value = true
  ruleForm.value?.validate((valid) => {
    if (valid) {
      confirmUpdate()
    } else {
      loading.value = false
    }
  })
}

const confirmUpdate = async () => {
  const transferOrgIdList = form.orgIdList.map((arr) => {
    return arr.slice().pop()
  })
  const data = {
    currentOrgId: currentOrgId.value!,
    transferOrgIdList: transferOrgIdList,
    userCodeList: userCodeList.value,
  }

  // const result = await moveOrgUser(data).finally(() => {
  //   loading.value = false
  // })

  // if (result.code == 200) {
  //   emit('succ')
  //   ElMessage.success($t('操作成功'))
  //   closeDialog()
  // }
}

// 暴露方法给父组件使用
defineExpose({
  openDialog,
})
</script>

<style lang="scss" scoped>
.custom-dialog-form {
  :deep(.el-form-item__label) {
    font-size: 12px;
    font-weight: normal;
    color: #2a2b2c;
  }
}
</style>
