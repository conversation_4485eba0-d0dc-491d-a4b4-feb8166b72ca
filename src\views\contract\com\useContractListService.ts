import { getRecentContractList, queryContractList, delContract } from '@/services/contract'
import { useUserStore } from '@/stores'
import { EContractStatus, type IContractTableData } from './Model'
import router from '@/router'
import { RESPONSE_CODE_SUCCESS } from '@/constants'
import { $t } from '@/utils/i18n'
export function useContractListService() {
  const userStore = useUserStore()
  const loading = ref(false)
  const recentDatas = ref<IContractTableData[]>([])
  const contractDatas = ref<IContractTableData[]>([])
  const page = ref(1)
  const pageSize = ref(10)
  const total = ref(0)
  const userId = computed(() => {
    return userStore.userInfo.userId
  })

  const statusOption = ref([
    { value: '0', label: $t('排队中') },
    { value: '1', label: $t('审查中') }, // 1-99
    { value: '100', label: $t('审查成功') },
    { value: '-1', label: $t('审查失败') },
  ])

  const loadContracts = async () => {
    if (!userId.value) {
      return false
    }
    loading.value = true
    const params = {
      pageNum: page.value,
      pageSize: pageSize.value,
    }
    const { code, message, data } = await queryContractList(params)
    loading.value = false
    if (code !== '000000') {
      ElMessage.error(message)
      return
    }
    const { list, total: count } = data as any

    contractDatas.value = list
    total.value = Number(count)
  }
  const loadRecents = async () => {
    if (!userId.value) {
      return false
    }
    loading.value = true
    const { code, message, data } = await getRecentContractList()
    loading.value = false
    if (code != '000000') {
      ElMessage.error(message)
      return
    }
    recentDatas.value = data as IContractTableData[]
  }

  const getReviewInfo = async ({ contractId, contractStatus }: IContractTableData) => {
    router.push(
      `/contract/view/${contractStatus == $t('编辑中') ? EContractStatus.REVIEW : EContractStatus.RESULT}/${contractId}`,
    )
    // const { code, message, data } = await getReviewInfoByContractId(contractId)
    // if (code != '000000') {
    //   ElMessage.error(message)
    //   return
    // }
    // const { recordId } = data as IContractRecord
    // if (recordId) {
    //   router.push(`/contract/view/${ContractStatus.REVIEW}/${id}`)
    // } else {
    //   router.push(`/contract/review/${id}`)
    // }
  }

  const delReviewInfo = (id: string, kind: string) => {
    ElMessageBox.confirm($t('确认删除审查记录吗?'), $t('提示'), {
      confirmButtonText: $t('确认'),
      cancelButtonText: $t('取消'),
      showClose: false,
    })
      .then(async () => {
        const { code } = await delContract(id)
        if (code === RESPONSE_CODE_SUCCESS) {
          if (kind == 'recent') {
            loadRecents()
          } else {
            page.value = 1
            loadContracts()
          }
          ElMessage.success($t('删除成功'))
        } else {
          ElMessage.error($t('删除失败'))
        }
      })
      .catch(() => {
        console.log('取消删除审查记录')
      })
  }

  const gotoReviewList = () => {
    router.push('/contract/more')
  }

  const handleSizeChange = (val: number) => {
    pageSize.value = val
    page.value = 1
    loadContracts()
  }

  const handleCurrentChange = (val: number) => {
    page.value = val
    loadContracts()
  }
  return {
    userId,
    total,
    page,
    pageSize,
    recentDatas,
    loading,
    statusOption,
    contractDatas,
    loadRecents,
    loadContracts,
    delReviewInfo,
    getReviewInfo,
    gotoReviewList,
    handleSizeChange,
    handleCurrentChange,
  }
}
function deleteReviewRecord(id: string) {
  throw new Error('Function not implemented.')
}
