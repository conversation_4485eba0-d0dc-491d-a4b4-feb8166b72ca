import { post, get } from '@/services'
import type { IResponse } from '@/services'

// 获取角色列表
// 请求参数
export interface IRoleQueryData {
  roleName?: string // 角色名称
  startLastUpdateDate?: string // 开始时间: 2023-12-11
  endLastUpdateDate?: string // 结束时间
}
interface IQueryPageRoleListParams extends IRoleQueryData {
  page: number
  pageSize: number
}
// 响应参数
export interface IList {
  id: string
  roleName: string
  roleCode: string
  roleRemark: string
  updateTime: string
  createBy: string
  menuCodeList: Array<string>
  menuNameList: Array<string>
}
export interface IQueryPageRoleList {
  pageNum: number
  pageSize: number
  total: number
  totalPages: number
  list: Array<IList>
}
// 接口
export function queryPageRoleList<T>(data: IQueryPageRoleListParams): Promise<IResponse<T>> {
  return post<T>(`/role/list-by-page`, data)
}

// 删除角色
export function deleteRole<T>(data: string[]): Promise<IResponse<T>> {
  return post<T>(`/role/del-by-ids`, data)
}

// 新增或修改角色授权
// 请求参数
export interface ISaveRoleParams {
  roleId: string
  roleName: string
  menuCodeList: string[]
  roleRemark?: string
}
// 接口
export function saveRole<T>(data: ISaveRoleParams): Promise<IResponse<T>> {
  return post<T>(`/role/save`, data)
}

// 获取功能权限列表
// 响应参数
export interface IPermission {
  menuName: string
  menuCode: string
  menuOrder: number
  relFlag: boolean // 关联标识 true: 存在关联 false:未关联
}
export interface IQueryPermissionList {
  roleCode: string // 角色Code
  menuList: IPermission[]
}
// 接口
export function queryPermissionList<T>(roleCode?: string): Promise<IResponse<T>> {
  return get<T>(`/menu/get-function-list-by-roleCode`, {
    roleCode,
  })
}
