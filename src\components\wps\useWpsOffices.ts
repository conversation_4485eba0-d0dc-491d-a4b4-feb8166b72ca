import { ref, shallowRef } from 'vue'
import WebOfficeSDK from './web-office-sdk-solution-v2.0.7.es.js'
export function useWpsOffices() {
  const officeType = ref(WebOfficeSDK.OfficeType.Writer)
  const appId = ref('')
  const fileId = ref('')
  const instance = shallowRef()
  const initOffice = (officeTypeVal: string, appIdVal: string, fileIdVal: string) => {
    instance.value = WebOfficeSDK.init({
      officeType: officeTypeVal,
      appId: appIdVal,
      fileId: fileIdVal,

      mount: document.getElementById('wps-office-container'),

      isListenResize: true,
    })
  }
  return { officeType }
}
