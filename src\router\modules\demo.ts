import type { RouteRecordRaw } from 'vue-router'

const router: RouteRecordRaw[] = [
  {
    path: '/demo',
    children: [
      {
        path: '',
        name: 'demo',
        component: () => import(/* webpackChunkName: "demo" */ '@/views/demo/index.vue'),
      },
    ],
  },
  {
    path: '/demoPdf',
    children: [
      {
        path: '',
        name: 'demoPdf',
        component: () => import(/* webpackChunkName: "demo2" */ '@/views/demo/demoPdf.vue'),
      },
    ],
  },
  {
    path: '/demoOffice',
    children: [
      {
        path: '',
        name: 'demoOffice',
        component: () => import(/* webpackChunkName: "demoOffice" */ '@/views/demo/demoOffice.vue'),
      },
    ],
  },
]
export default router
