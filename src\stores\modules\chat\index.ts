import { defineStore } from 'pinia'
import { ChatAcitve, ChatItem } from './helper'
import { HISTORY_CHAT_COUNT } from '@/constants'

export const useChatStore = defineStore('chat', () => {
  //历史对话列表数据
  const historyChatList = ref<ChatItem[]>([])
  // //当前对话数据
  const chatActive = ref(new ChatAcitve())

  const setHistoryChatList = (list: ChatItem[]) => {
    historyChatList.value = list
  }

  const insertChatInHeader = (data: any) => {
    if (historyChatList.value.length == 0) {
      historyChatList.value.push(ChatItem.create(data))
      return
    }
    // 查找目标元素的索引
    const targetIndex = historyChatList.value.findIndex((item: any) => item['id'] === data.id)
    if (targetIndex !== -1) {
      historyChatList.value.splice(targetIndex, 1)
      historyChatList.value.unshift(ChatItem.create(data))
    } else {
      if (historyChatList.value.length >= HISTORY_CHAT_COUNT) {
        const lastElement = historyChatList.value.pop()
        if (lastElement) {
          historyChatList.value.unshift(ChatItem.create(data))
        }
        return
      } else {
        historyChatList.value.unshift(ChatItem.create(data))
      }
    }
  }

  const deleteChatsFromStore = (id: string) => {
    historyChatList.value = historyChatList.value.filter((item) => item.id != id)
  }

  return {
    deleteChatsFromStore,
    chatActive,
    insertChatInHeader,
    setHistoryChatList,
    historyChatList,
  }
})
