<script setup lang="ts">
import { $t } from '@/utils/i18n'
import ContractUpload from './ContractUpload.vue'
import type { ExtractionCantract } from '../types'

const emits = defineEmits(['upload-succ'])

const visible = ref(false)
const dialogClosed = () => {
  visible.value = false
}

const dialogShow = () => {
  visible.value = true
}

const uploadSucc = (val: ExtractionCantract) => {
  dialogClosed()
  emits('upload-succ', val)
}

defineExpose({
  dialogShow,
})
</script>

<template>
  <el-dialog
    :title="$t('请上传合同')"
    v-model="visible"
    center
    :append-to-body="true"
    :modal-append-to-body="false"
    :close-on-click-modal="false"
    element-loading-:text="$t('文件上传中...')"
    :hide-required-asterisk="true"
    width="800px"
    heighr="436px"
    @close="dialogClosed"
  >
    <ContractUpload @uploadFile="uploadSucc" />
  </el-dialog>
</template>
