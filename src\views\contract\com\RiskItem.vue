<script lang="ts" setup>
import { ContractInfo, ContractRisk, ContractRiskItem, ContractRiskLocation } from './Model'
import TextOverTip from '@/components/TextOverTip.vue'
import RiskNodeTab from './RiskNodeTab.vue'
import RiskNode from './RiskNode.vue'
import mitt from '@/utils/eventsBus'
import { $t } from '@/utils/i18n'
defineProps({
  risks: {
    type: Array as PropType<ContractRisk[]>,
    required: true,
  },
  contractInfo: {
    type: ContractInfo,
    required: true,
  },
})

const emit = defineEmits(['toggleItem'])

const officeRef = inject('officeRef') as any

const miniWidth = ref(false)

const toggleItem = (risk: ContractRisk) => {
  risk.isActive = !risk.isActive
  emit('toggleItem', risk)
}

const toggleNode = (risk: ContractRisk, riskItem: ContractRiskItem) => {
  riskItem.isActive = !riskItem.isActive
}
const searchPosition = () => {}
const handleFeedback = (a: any, b: any) => {}
const handleAcceptSug = async (location: ContractRiskLocation) => {
  let suggestText = location.suggestionText
  suggestText = suggestText.replace(/<add>|<\/add>/g, '')
  suggestText = suggestText.replace(/<del>[\s\S]*?<\/del>/g, '')
  console.log('location', location)
  const id = await officeRef.value.setRevisions({ text: location.originalText, replaceText: suggestText })
  console.log('id', id)
  if (!id) {
    ElMessage.warning($t('未能定位到原文位置'))
    location.canSearch = false
  } else {
    location.revisonId = id
  }
}
const handleSelectAcceptSug = async (location: ContractRiskLocation) => {
  let suggestText = location.suggestionText
  suggestText = suggestText.replace(/<add>|<\/add>/g, '')
  suggestText = suggestText.replace(/<del>[\s\S]*?<\/del>/g, '')

  const id = await officeRef.value.setSelectRevisions(suggestText)
  if (!id) {
    ElMessage.warning($t('请先划选原文位置'))
  } else {
    location.revisonId = id
  }
}
const handleRecoverSug = async (item: any, node: any, location: any) => {
  console.log('location.revisonId', location.revisonId)
  await officeRef.value.rejectRevisions(location.revisonId)
  location.revisonId = ''
}

const hasSuggest = (locations: ContractRiskLocation[]) => {
  const l = locations.find((item) => item.isTabActive)
  return !!l?.suggestionText
}

mitt.on('isMiniWidth', (isMiniWidth: any) => {
  miniWidth.value = isMiniWidth
})
onUnmounted(() => {
  mitt.off('isMiniWidth')
})
</script>
<template>
  <div class="box-inner">
    <div
      v-for="item in risks"
      :key="item.ruleTypeName"
      class="risk-item"
      :class="{ 'risk-item__active': item.isActive }"
    >
      <!-- 一级头部 -->
      <div class="risk-item-header" :class="{ 'risk-item-header__active': item.isActive }" @click="toggleItem(item)">
        <!-- 一级标题内容 -->
        <div class="risk-item-header__left">
          <div class="arrow-icon gray">
            <i class="iconfont" :class="!item.isActive ? 'icon-is-caretright' : 'icon-is-caretbottom'" />
          </div>

          <TextOverTip
            :style="{
              fontSize: '16px',
              fontWeight: '500',
            }"
            :content="item.ruleTypeName"
          />
        </div>
        <span class="risk-item-header__right">
          <span class="risk-item-header__txt">{{ $t('风险(') }}{{ item.ruleItemList.length }}) </span>
        </span>
      </div>
      <el-collapse-transition>
        <div v-show="item.isActive" class="risk-item-content">
          <div
            v-for="(node, nodeId) in item.ruleItemList"
            :key="node.id"
            class="risk-node"
            :style="{ '--color': node.font.color }"
          >
            <!-- 二级头部 -->
            <div
              class="risk-node-header"
              :style="{
                '--color': node.font.color,
                '--bg-color': node.font.bgColor,
              }"
              :class="{ top: !node.isActive, left: node.isActive }"
              @click="toggleNode(item, node)"
            >
              <!-- 二级标题 -->
              <TextOverTip
                class="risk-item-header__left risk-node-header__left"
                :style="{ '--color': node.font.color }"
                :content="`${node.ruleWarn} ${node.locations.length > 1 ? `(${node.locations.length})` : ''}`"
                placement="top"
              />
              <span class="risk-node-header__right">
                <span
                  class="risk-node-header__txt"
                  :style="{ '--color': node.font.color, '--bg-color': node.font.bgColor }"
                  >{{ node.font.name }}
                </span>
                <i class="risk-node-header__line"></i>
                <div class="risk-node-header__icon">
                  <i class="iconfont gray" :class="node.isActive ? 'icon-is-caretright' : 'icon-is-caretbottom'" />
                </div>
              </span>
            </div>

            <!--tab 栏切换内容区域 -->
            <RiskNodeTab
              v-if="node.locations.length > 1"
              :item="item"
              :node="node"
              v-model:locations="node.locations"
              @search-position="searchPosition"
            ></RiskNodeTab>
            <!-- 二级内容 -->
            <RiskNode :node="node" :item="item" :node-id="nodeId" @search-position="searchPosition">
              <!-- <template #feedback="{ location }">
                <span class="handle-feedback" @click="handleFeedback(node, location.originalText)">
                  <i class="iconfont icon-is-feedback" />
                  {{ !miniWidth ? '反馈有误' : '反馈' }}</span
                >
              </template> -->

              <template v-if="node.ruleLevel !== 0 && hasSuggest(node.locations)" #default="{ location }">
                <div v-if="contractInfo.isDocFile">
                  <el-button v-if="location.revisonId" @click="handleRecoverSug(item, node, location)">{{
                    $t('撤销修改')
                  }}</el-button>
                  <template v-else>
                    <el-button v-if="location.canSearch" @click="handleAcceptSug(location)">
                      {{ $t('接受修订') }}
                    </el-button>
                    <el-button v-else @click="handleSelectAcceptSug(location)">
                      {{ $t('选定文中位置添加') }}
                    </el-button>
                  </template>
                </div>
              </template>
            </RiskNode>
          </div>
        </div>
      </el-collapse-transition>
    </div>
    <div class="box-footer">{{ $t('以上内容由AI生成') }}</div>
  </div>
</template>

<style lang="scss" scoped>
.gray {
  color: #dedede;
}
:deep(.el-button) {
  color: var(--minor-font);
  &:focus {
    color: var(--minor-font);
    background-color: var(--bg-color);
    border-color: #dcdfe6;
  }
  &:hover {
    color: var(--main-bg);
    background-color: #f1ebfd;
    border-color: #d6c4fa;
  }
  &:active {
    color: #6b35d7;
    outline: none;
    background-color: #f1ebfd;
    border-color: #6b35d7;
  }
}
.svg {
  color: #dedede;
}
.scrollbar-wrapper {
  flex: 1;
  width: 100%;
}
.box-inner {
  width: 100%;
  background-color: #f5f7fa;
  -webkit-font-smoothing: antialiased;
  .risk-item {
    border-top: 1px solid var(--input-bg);
    &:last-child {
      border-bottom: 1px solid var(--input-bg);
    }

    // 一级风险项头部
    .risk-item-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 46px;
      padding: 0 16px;
      cursor: pointer;
      user-select: none;
      background-color: var(--bg-color);
      &:hover {
        color: var(--is-color-773bef);
        .svg {
          color: var(--main-bg);
        }
        .iconfont {
          color: var(--is-color-773bef);
        }
      }
      .icon-is-lujing {
        transform: scale(0.4) rotate(-90deg);
      }
      &__left {
        display: flex;
        flex: 0.9;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 16px;
        line-height: 22px;
        white-space: nowrap;
        .arrow-icon {
          padding-right: 0.5rem;
        }
      }
      &__txt {
        margin-right: 4px;
        font-size: 14px;
        font-weight: 400;
      }
      &__right {
        display: flex;
        display: inline-block;
        align-items: center;
      }
    }
    .icon-is-lujing {
      display: inline-block;
      margin-left: 4px;
      color: #bdbdbd;

      // font-size: 6px;
      transform: scale(0.4);
    }
    .risk-item-header__active {
      font-weight: 500;
      border: none;
      .icon-is-lujing {
        transform: scale(0.4) rotateX(0deg);
      }
      .risk-item-header__txt {
        font-weight: 400;
      }
    }
    &-content {
      padding: 8px 16px 16px;
      background-color: #f5f7fa;
    }
  }
  .risk-node {
    margin-bottom: 8px;
    overflow: hidden;
    background-color: var(--bg-color);
    border: 1px solid var(--page-header-line);
    border-radius: 4px;

    // 二级风险项头部
    &-header {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 54px;
      padding: 0 16px;
      cursor: pointer;
      user-select: none;
      &:hover {
        .icon-is-lujing {
          color: var(--is-color-773bef);
        }
      }
      &::before {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        content: '';
        background-color: var(--color);
        border-radius: 4px 4px 0 0;
        transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
      }
      &::after {
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        content: '';
        background-color: var(--color);
        border-radius: 4px 0;
        transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
      }
      &__left {
        padding-left: 8px;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        font-weight: 500;
        line-height: 22px;
        white-space: nowrap;
        -webkit-box-orient: vertical;
      }
      &__right {
        display: flex;
        align-items: center;
      }
      &__line {
        display: inline-block;
        width: 1px;
        height: 12px;
        margin: 0 0 0 8px;
        background-color: var(--page-header-line);
      }
      &__txt {
        display: flex;
        align-items: center;
        height: 22px;
        padding: 0 8px;
        font-size: 14px;
        font-weight: 500;
        line-height: 1;
        color: var(--color);
        background-color: var(--bg-color);
        border-radius: 4px;
      }
      .icon-fold {
        transform: scale(0.4) rotateX(180deg);
      }
      &__icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        cursor: pointer;
      }
    }

    // 展开
    .top::before {
      visibility: hidden;
      opacity: 0;
    }

    // 收起
    .left::after {
      visibility: hidden;
      opacity: 0;
    }
  }
  .handle-feedback {
    display: flex;
    align-items: center;
    line-height: 20px;
    color: var(--is-color-7d7b89);
    cursor: pointer;
    .icon-is-feedback {
      margin-right: 4px;
    }
    &:hover {
      color: var(--iterms-band-main-color);
    }
  }
}
.icon-is-arrow-up {
  color: #bdbdbd;
}
.feedback-tag {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  margin-top: 4px;
  &-icon {
    color: #929292;
    cursor: pointer;
  }
}
.box-footer {
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: center;
  width: 33vw;
  padding: 6px 0 4px;
  color: #bdbdbd;
  text-align: center;
}
</style>
