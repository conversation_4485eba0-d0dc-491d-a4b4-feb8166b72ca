<template>
  <div class="step-view">
    <div class="step-node">
      <div class="step-icon">1</div>
      <div class="step-tail"></div>
      <div class="step-content">
        <span>{{ $t('下载Excel模板，批量填写信息。') }}</span>
        <el-button class="step-btn download" type="text" icon="el-icon-download" @click.stop="downloadTemplate">{{
          $t('下载模板')
        }}</el-button>
      </div>
    </div>

    <div class="step-node">
      <div class="step-icon">2</div>
      <div class="step-tail"></div>
      <div class="step-content">
        <span>{{ $t('上传填写好的信息。') }}</span>
        <el-button class="step-btn upload" type="primary" @click.stop="clickHandle">{{ $t('上传模板') }}</el-button>
      </div>
    </div>

    <div class="step-node">
      <div class="step-icon">3</div>
      <div class="step-tail"></div>
      <div class="step-content">
        <span>{{ $t('上传完成。') }}</span>
        <span class="gray">{{ $t('(有错误数据时，上传正确数据，下载错误数据）') }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { templateDown } from '@/services/manage/index'
import { RESPONSE_CODE_SUCCESS } from '@/constants'
import { useDownloadFile } from '@/composables/useDownloadFile'
const { downloadFile } = useDownloadFile()
const emit = defineEmits<{
  upload: []
}>()

const downloadTemplate = () => {
  downloadFile({
    fileName: '用户导入模板.xlsx',
    fileApi: [templateDown, {}],
  })
}

const clickHandle = () => {
  emit('upload')
}
</script>
<style lang="scss" scoped>
.step-node {
  position: relative;
  padding-bottom: 24px;
  .step-icon {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 22px;
    height: 22px;
    font-size: 12px;
    color: #262626;
    border: 1px solid #e9e9e9;
    border-radius: 50%;
  }
  .step-tail {
    position: absolute;
    top: 22px;
    left: 12px;
    width: 1px;
    height: calc(100% - 22px);
    background: #e9e9e9;
  }
  .step-content {
    position: relative;
    display: flex;
    align-items: center;
    min-height: 22px;
    padding-left: 27px;
    font-size: 12px;
    color: #2a2b2c;
    .text-btn {
      color: #276ef9;
      cursor: pointer;
      &:hover {
        opacity: 0.8;
      }
    }
    .gray {
      color: #929292;
    }
    .step-btn {
      position: absolute;
      top: -5px;
      &.upload {
        left: 140px;
      }
      &.download {
        left: 205px;
      }
    }
  }
  &:last-child {
    padding-bottom: 0;
    .step-tail {
      display: none;
    }
  }
}
</style>
