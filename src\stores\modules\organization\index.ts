import { defineStore } from 'pinia'
import { ref } from 'vue'
// import * as roleApi from '@/services/manage/role'
// import { queryOrgPageList } from '@/services/manage/organization'
// import { formatListFunc } from '@/utils/const'

interface OrgNode {
  id: string
  orgId: string | null
  orgName: string
  parentId: string
  rootFlag: boolean
  orgOrder: number
  orgIdPath: string
  orgNamePath: string
  hasChild: boolean | null
  leaderUserCode: string | null
  leaderName: string | null
  children: OrgNode[]
}

interface UserNode {
  id: number
  userCode: string
  realName: string
  userName: string
  userPhone?: string
  userStatus: number
  workHandoverStatus: number
  leaderFlag: number
  roleNameListString?: string
  orgNameListString?: string
  mainDepartmentName?: string
  createBy?: string
}

interface roleListItem {
  id: string
  roleName: string
  roleCode: string
  roleRemark: string
  updateTime: string
  createBy: string
  menuCodeList: string[]
  menuNameList: string[]
}

const loopResolveTree = function (list: OrgNode[]): OrgNode[] {
  list.forEach((node) => {
    if (node.children) {
      if (node.children.length) loopResolveTree(node.children)
      else node.children = []
    }
  })
  return list
}

export const useOrganizationStore = defineStore('organization', () => {
  // 状态
  const orgSelectList = ref<OrgNode[]>([])
  const roleSelectList = ref<UserNode[]>([])
  const roleSelectMap = ref<Record<string, string>>({})

  const orgList = ref<OrgNode[]>([])
  const roleList = ref<roleListItem[]>([])

  const setOrgList = (list: OrgNode[]) => {
    orgList.value = list || []
  }

  const setRoleList = (list: roleListItem[]) => {
    roleList.value = list || []
  }

  // 操作方法
  const setOrgSelectList = (list: OrgNode[]) => {
    orgSelectList.value = list || []
  }

  const setRoleSelectList = (list: UserNode[]) => {
    roleSelectList.value = list || []
  }

  const setRoleSelectMap = (obj: Record<string, string>) => {
    roleSelectMap.value = obj
  }

  // const searchOrgSelect = async (): Promise<void> => {
  //   try {
  //     // TODO: 需要根据实际的服务路径进行调整
  //     // const res = await queryOrgPageList({})
  //     // if (res.code == 200) {
  //     //   const tree = loopResolveTree(res.data || [])
  //     //   setOrgSelectList(tree)
  //     // }
  //     console.log('searchOrgSelect called')
  //   } catch (error) {
  //     console.error('Failed to fetch org select list:', error)
  //   }
  // }

  // const searchRoleSelect = async (): Promise<void> => {
  //   const params = {
  //     data: { roleName: '', startLastUpdateDate: '', endLastUpdateDate: '' },
  //     page: 1,
  //     pageSize: 1000,
  //   }

  //   try {
  //     // TODO: 需要根据实际的服务路径进行调整
  //     // const res = await roleApi.httpApi.list(params)
  //     // if (res.code == 200) {
  //     //   const { result } = res.data || {}
  //     //   setRoleSelectList(result || [])
  //     //   const roleCodeMap = formatListFunc({
  //     //     obj: result,
  //     //     key: 'roleCode',
  //     //     value: 'roleName',
  //     //   })
  //     //   setRoleSelectMap(roleCodeMap)
  //     // }
  //     console.log('searchRoleSelect called', params)
  //   } catch (error) {
  //     console.error('Failed to fetch role select list:', error)
  //   }
  // }

  return {
    // 状态
    orgSelectList,
    roleSelectList,
    roleSelectMap,
    orgList,
    roleList,

    // 操作方法
    setOrgSelectList,
    setRoleSelectList,
    setRoleSelectMap,
    setOrgList,
    setRoleList,
    // searchOrgSelect,
    // searchRoleSelect,
  }
})
