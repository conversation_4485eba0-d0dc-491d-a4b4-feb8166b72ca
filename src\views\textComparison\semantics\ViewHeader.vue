<script setup lang="ts">
defineProps<{ headerObj: { isOrigin: boolean; filename: string } }>()
</script>

<template>
  <div class="view-top" :class="{ 'top-left': headerObj.isOrigin }">
    <span class="view-top-tit" :class="{ 'right-title': !headerObj.isOrigin }">
      <span>{{ headerObj.isOrigin ? '标准' : '对比' }}</span>
      <span>{{ $t('文档') }}</span>
    </span>
    <span class="view-top-doc">
      <div class="view-top-doc-name">{{ headerObj.filename }}</div>
    </span>
  </div>
</template>

<style lang="scss" scoped>
.view-top {
  z-index: 1;
  display: flex;
  align-items: center;

  // width: calc(100% - 36px);
  height: 65px;

  // margin: 0 16px;
  padding: 0 24px;
  background: var(--bg-color);
  border-bottom: 2px solid #e9e9e9;
  &-shadow {
    box-shadow: 0 2px 8px 0 rgb(0 0 0 / 10%);
  }
  .view-top-tit {
    display: flex;
    flex-shrink: 0;
    flex-flow: column wrap;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    padding-top: 2px;
    margin-right: 16px;
    font-size: 12px;
    color: var(--bg-color);
    background-color: #1fac78;
    border-radius: 4px;
  }
  .right-title {
    background-color: #276ef9;
  }
  .view-top-doc {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #979797;
    white-space: nowrap;
    &-name {
      color: #262626 !important;

      // line-height: 18px;
    }
    &-intro {
      font-size: 12px;
      color: #979797;
    }
    &-oper {
      margin: 0 16px;
    }
    &-version {
      display: inline-block;
      padding: 2px 4px;
      margin-top: 4px;
      color: #1fac78;
      background-color: #e8f6f1;
      border-radius: 4px;
    }
    .right-version {
      color: #276ef9;
      background-color: #f1f5ff;
    }
  }
}
.top-left {
  margin-right: 4px;
}
.hide-view-top {
  position: absolute;
  top: 0;
  opacity: 0;
}
.show-top {
  opacity: 1 !important;
}
</style>
