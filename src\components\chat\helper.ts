import { InputerStyleEnum } from '@/constants'
import { formatFileSize, uuid } from '@/utils'
import type { UploadFile } from '@/types/chat'
const IMG_SUFFIX = ['jpg', 'jpeg', 'png']
export interface UploadRawFile extends File {
  uid: number
  isDirectory?: boolean
}

export type UploadStatus = 'ready' | 'uploading' | 'success' | 'fail'

export class IUploadFile implements UploadFile {
  name = ''
  percentage = 0
  status: UploadStatus = 'ready'
  size = 0
  completed = false
  uid: number = 0
  id: string = ''
  url?: string | undefined
  raw?: UploadRawFile | undefined
  fileKey: string = ''
  fileId: string
  fileName = this.name
  contentType = ''
  fileSuffix = ''
  suffixName = ''
  fileSize = 0
  filePage = 0
  fileContent = ''
  attachType = 1
  uuid = ''
  type: string = ''
  constructor(data: Partial<IUploadFile>) {
    Object.assign(this, data)
    this.fileId = uuid()
  }
  getFileSize() {
    return formatFileSize(this.fileSize, 2)
  }
  isImg(): boolean {
    return IMG_SUFFIX.includes(this.fileSuffix.toLocaleLowerCase())
  }
  getFileSuffix() {
    if (this.fileSuffix == 'doc') {
      return 'docx'
    }
    return this.fileSuffix
  }
}

const defaultConfig = {
  inputerSize: { width: 682, height: 32 },
  attachShow: false,
  headerShow: false,
  uploadPopShow: true,
  uploadShow: false,
  lineShow: false,
  leftFooterBarShow: false,
}

const assignAttr = (conf: {
  grid: string
  attachShow?: boolean
  inputerSize: { width: number; height: number }
  headerShow?: boolean
  leftFooterBarShow?: boolean
  lineShow?: boolean
  uploadShow?: boolean
}) => {
  return { ...defaultConfig, ...conf } //Object.assign(defaultConfig,conf)
}

export const getDisplayGrid = () => {
  const inputDisplayMap = new Map()

  inputDisplayMap.set(
    InputerStyleEnum.HEADER_ATTACH_FLOW,
    assignAttr({
      grid: `'input-header input-header input-header' 'input-attach input-attach input-attach'
		'input-line input-line input-line' 'input-inner input-inner input-inner'  'input-footer-left . input-footer-right'`,
      attachShow: true,
      inputerSize: { width: 760, height: 72 },
      headerShow: true,
      leftFooterBarShow: true,
      lineShow: true,
    }),
  )
  inputDisplayMap.set(
    InputerStyleEnum.HEADER_ATTACH,
    assignAttr({
      grid: `'input-header input-header ' 'input-attach input-attach'   'input-line input-line' ' input-inner input-footer-right'`,
      attachShow: true,
      inputerSize: { width: 682, height: 58 },
      headerShow: true,

      lineShow: true,
    }),
  )
  inputDisplayMap.set(
    InputerStyleEnum.INNER_ONLY,
    assignAttr({
      grid: `'input-inner input-footer-right'`,
      inputerSize: { width: 682, height: 58 },
    }),
  )
  inputDisplayMap.set(
    InputerStyleEnum.INNER_ONLY_FLOW,
    assignAttr({
      grid: `'input-inner input-inner' '. input-footer-right'`,
      inputerSize: { width: 760, height: 72 },
    }),
  )
  inputDisplayMap.set(
    InputerStyleEnum.ATTACH_FLOW,
    assignAttr({
      grid: `'input-attach input-attach input-attach' 'input-inner input-inner input-inner' 'input-footer-left . input-footer-right'`,
      attachShow: true,
      leftFooterBarShow: true,
      inputerSize: { width: 760, height: 72 },
    }),
  )
  inputDisplayMap.set(
    InputerStyleEnum.ATTACH,
    assignAttr({
      grid: `'input-attach input-attach' 'input-inner input-footer-right'`,
      attachShow: true,
      inputerSize: { width: 682, height: 58 },
    }),
  )
  inputDisplayMap.set(
    InputerStyleEnum.HEADER_UPLOAD,
    assignAttr({
      grid: `'input-header input-header ' 'input-attach input-attach' 'input-upload input-upload' 'input-line input-line' 'input-inner input-footer-right'`,
      uploadShow: true,
      lineShow: true,
      inputerSize: { width: 682, height: 58 },
      headerShow: true,
    }),
  )
  inputDisplayMap.set(
    InputerStyleEnum.HEADER_UPLOAD_FLOW,
    assignAttr({
      grid: `'input-header input-header' 'input-upload input-upload' 'input-line input-line ' 'input-inner input-inner' '. input-footer-right'`,
      uploadShow: true,
      lineShow: true,
      headerShow: true,
      inputerSize: { width: 760, height: 72 },
    }),
  )
  return inputDisplayMap
}
