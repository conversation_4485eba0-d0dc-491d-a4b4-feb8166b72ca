<script lang="ts" setup>
import RiskItem from './RiskItem.vue'
import { useContractStore } from '@/stores'
const contractStore = useContractStore()
const props = defineProps({
  service: {
    type: Object,
    required: true,
  },
})
const contract = ref(props.service)
</script>
<template>
  <el-scrollbar wrap-class="scrollbar-wrapper">
    <Transition name="fade">
      <div v-show="contract.progress != 100" class="progress-wrap">
        <span>{{ $t('审查进度') }}</span>
        <el-progress :percentage="contract.progress" />
      </div>
    </Transition>

    <div class="top-node-options">
      <div
        v-for="(item, idx) in contract.levelOptions"
        :key="idx"
        class="top-node-options__btn"
        :class="{ 'top-node-options__active': contract.activeLevel === item.level }"
        :style="{ '--color': item.color, '--font-color': item.color }"
        @click="contract.handleSelectOption(item)"
      >
        <span>{{ item.name }}</span>
        <span v-show="!contract.isMiniWidth" class="top-node-options__count"
          >({{ item.count == 0 ? 0 : item.count }})</span
        >
      </div>
      <el-dropdown class="more" size="mini" @command="contract.handleDropdown">
        <span style="outline: none">
          <i class="iconfont icon-is-more"></i>{{ !contract.isMiniWidth ? $t('更多') : '' }}
        </span>
        <template v-slot:dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="allFold" style="font-size: 14px">
              {{ contract.isExpandAll ? $t('收起展开') : $t('展开全部') }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <!--risk items-->
    <div>
      <RiskItem
        v-if="contract.filterData.length"
        :risks="contract.filterData"
        :contract-info="contractStore.contractInfo"
        @toggle-item="contract.toggleItem"
      ></RiskItem>
      <div v-else>
        <el-empty :description="$t('暂无数据')" />
      </div>
    </div>
  </el-scrollbar>
</template>
<style lang="scss" scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 1s;
  transition-delay: 1s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
.progress-wrap {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  height: 0.875rem;
  padding: 1rem;
  margin-top: 0.5rem;
  font-size: 0.875rem;
  background-color: var(--bg-color);
  :deep(.el-progress) {
    flex: 1;
    margin-left: 6px;
  }
}
.top-node-options {
  display: flex;
  gap: 8px;
  width: 100%;
  padding: 12px 16px;
  background-color: var(--bg-color);

  --font-color: '';
  &__btn {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: center;
    height: 28px;
    line-height: 28px;
    color: var(--minor-font);
    white-space: nowrap;
    cursor: pointer;
    border: 1px solid var(--page-header-line);
    border-radius: 4px;
    &:hover {
      color: var(--font-color);
    }
  }
  &__active {
    font-weight: 500;
    color: var(--font-color);

    // background-color: var(--bgColor);
    border: 1px solid var(--color);
  }
  &__count {
    font-size: 12px;
  }
  .more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 8px;
    margin-left: 8px;
    color: var(--minor-font);
    cursor: pointer;
    border: 1px solid var(--page-header-line);
    border-radius: 4px;
    &:hover {
      color: var(--is-color-773bef);
    }
    &::before {
      position: absolute;
      left: -9px;
      width: 1px;
      height: 1em;
      margin-right: 8px;
      content: '';
      background-color: var(--page-header-line);
    }
    span {
      display: flex;
      align-items: center;
    }
    i {
      margin-right: 2px;
    }
  }
}
</style>
