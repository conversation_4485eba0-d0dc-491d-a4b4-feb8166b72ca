<script lang="ts" setup>
import { $t } from '@/utils/i18n'

const props = defineProps({
  hideHeader: {
    type: Boolean,
    default: false,
  },
})
const visible = ref(false)
const sordIdx = ref(1)
let itemTimer: any
let nodeTimer: any

interface ITask {
  title: string
  sort: number
  task: ISubTask[]
}
interface ISubTask {
  text: string
  status: number
  sort: number
}
const list: ITask[] = [
  {
    title: $t('第一阶段：深度理解审查目标'),
    sort: 0,
    task: [
      { text: $t('正在接收审查任务指令'), status: 0, sort: 1 },
      { text: $t('正在识别核心审查对象与范围'), status: 0, sort: 2 },
      { text: $t('正在解析任务背后的关键意图'), status: 0, sort: 3 },
      { text: $t('正在设定审查清单的成功标准'), status: 0, sort: 4 },
    ],
  },
  {
    title: $t('第二阶段：构建审查框架'),
    sort: 5,
    task: [
      { text: $t('正在调用相关领域的知识库'), status: 0, sort: 5 },
      { text: $t('正在提取关键节点与逻辑关系'), status: 0, sort: 6 },
      { text: $t('正在构建多层级的审查结构'), status: 0, sort: 7 },
      { text: $t('正在规划审查清单的核心模块'), status: 0, sort: 8 },
    ],
  },
  {
    title: $t('第三阶段：生成并优化审查项'),
    sort: 9,
    task: [
      { text: $t('正在生成初步的审查项草案'), status: 0, sort: 9 },
      { text: $t('正在评估并优化每个步骤的清晰度'), status: 0, sort: 10 },
      { text: $t('正在确保每个审查项的可执行性'), status: 0, sort: 11 },
      { text: $t('正在补充可能存在的逻辑漏洞'), status: 0, sort: 12 },
      { text: $t('正在进行最终的格式化与润色'), status: 0, sort: 13 },
      { text: $t('即将完成，审查清单即将呈现！'), status: 0, sort: 14 },
    ],
  },
]
const tasks = ref<ITask[]>([])

function setTask() {
  tasks.value.forEach((item: ITask) => {
    item.task.forEach((t) => {
      if (sordIdx.value === t.sort) {
        t.status = 1
      }
    })
  })
  sordIdx.value++
}

function stopTask() {
  tasks.value.forEach((item) => {
    item.task.forEach((t) => {
      if (t.sort === 14) {
        t.status = 1
      }
    })
  })
  nextTick(() => {
    visible.value = false
  })
}

function stop() {
  itemTimer && clearInterval(itemTimer)
  if (sordIdx.value > 13) {
    return stopTask()
  }
  nodeTimer = setInterval(() => {
    setTask()
    if (sordIdx.value > 14) {
      stopTask()
      clearInterval(nodeTimer)
    }
  }, 500)
}

function end() {
  sordIdx.value = 1
  tasks.value = JSON.parse(JSON.stringify(list))
  visible.value = true
}

function start() {
  sordIdx.value = 1
  tasks.value = JSON.parse(JSON.stringify(list))
  visible.value = true
  itemTimer = setInterval(() => {
    setTask()
    if (sordIdx.value > 13) clearInterval(itemTimer)
  }, 5000)
}

defineExpose({
  start,
  stop,
  end,
})

onBeforeUnmount(() => {
  itemTimer && clearInterval(itemTimer)
  nodeTimer && clearInterval(nodeTimer)
})
</script>
<template>
  <div v-if="visible" class="task-wrap">
    <template v-if="!hideHeader">
      <div class="title">{{ $t('审查清单生成中') }}</div>
      <div class="desc">
        {{ $t('预计1-2分钟，请耐心等待')
        }}<span><i class="loading-dot">.</i><i class="loading-dot">.</i><i class="loading-dot">.</i></span>
      </div>
    </template>
    <div v-for="(item, index) in tasks" v-show="sordIdx >= item.sort" :key="index" class="task-item">
      <div class="task-title">{{ item.title }}</div>
      <div class="task-nodes">
        <div v-for="(node, nodeIdx) of item.task" :key="nodeIdx" class="task-node">
          <div v-show="sordIdx >= node.sort" style="display: flex; align-items: center">
            <div v-if="node.status !== 1" class="radio-circle"></div>
            <!-- <icons v-else name="check-circle-outlined" width="1.25rem" height="1.25rem" style="color: #409eff"></icons> -->
            <i v-else style="font-size: 18px; color: #409eff" class="icon-is-check-circle-outlined"></i>

            <span style="margin-left: 8px"> {{ node.text }}</span>
            <span v-show="node.status === 0"
              ><i class="loading-dot">.</i><i class="loading-dot">.</i><i class="loading-dot">.</i></span
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.task-wrap {
  z-index: 9999;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  background-color: #fff;
  .title {
    margin-bottom: 12px;
    font-size: 16px;
    font-weight: bold;
  }
  .desc {
    font-size: 14px;
    line-height: 100%;
    color: #7d7b89;
    letter-spacing: 1px;
  }
  .task-item {
    .task-title {
      height: 24px;
      margin: 12px 0;
      font-size: 14px;
      font-weight: 500;
      line-height: 24px;
      color: #262626;
      letter-spacing: 1px;
    }
    .task-nodes {
      .task-node {
        display: flex;
        align-items: center;
        height: 24px;
        margin-bottom: 8px;
        font-size: 14px;
        line-height: 24px;
        color: #262626;
        letter-spacing: 1px;
        .iconfont {
          margin-right: 4px;
          font-size: 16px;
        }
        .icon-is-uncheck-circle-outlined {
          color: #cacaca;
        }
        .icon-is-a-11 {
          color: #492ed1;
        }
      }
    }
  }
}
.loading-dot {
  animation: loading 0.9s infinite ease-in-out;
}
.loading-dot:nth-child(1) {
  animation-delay: 0.3s;
}
.loading-dot:nth-child(2) {
  animation-delay: 0.6s;
}
.loading-dot:nth-child(3) {
  animation-delay: 0.9s;
}

@keyframes loading {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
.radio-circle {
  width: 1rem;
  height: 1rem;
  margin-left: 2px;
  cursor: pointer;
  border: 1px solid #c0c4cc;
  border-radius: 50%;
}
</style>
