<script lang="ts" setup>
import OPS from '@/components/ops/OOffice.vue'

import { $t } from '@/utils/i18n'
const config = {
  document: {
    fileType: 'docx',
    key: '1641121211122112',
    title: 'Example Document Title.docx',
    url: 'https://cdn.fadada.com/dist/static/c/12/20230509135214_1b7cde0e-ac3e-409c-8cac-7b11f65b7b0f.doc',
    permissions: {
      comment: true,
      edit: true,
    },
  },
  editorConfig: {
    // callbackUrl: 'http://************:9002/save',
    lang: 'zh',
    user: {
      group: '',
      id: '11',
      name: $t('阿策'),
    },
    customization: {
      hideRulers: true,
      plugins: true,
      spellcheck: false,
      help: false,
      comments: true,
      // restrictMode: 'cctrl'
      needCommentsList: true,
      trackChanges: true,
      shortcut: true, // 开启快捷回调
      // reviewDisplay: 'final',
    },
    mode: 'edit',
  },
}

function onDocumentReady() {
  console.log('DocumentReady')
}

const refOffice = ref<InstanceType<typeof OPS>>()
const text = ref('')
async function handleSeachText() {
  const flag = await refOffice.value?.searchText(text.value)
  if (!flag) {
    ElMessage.warning($t('未找到文本'))
  }
}

async function handleFileUrl() {
  const res = await refOffice.value?.getFileUrl()
  ElMessage.success('url: ' + res.url)
}

async function handleRevision() {
  const flag = await refOffice.value?.setRevision(text.value)
}
</script>
<template>
  <div class="page-wrap">
    <div class="office">
      <OPS
        document-server-url="
      https://ai-review-test.fadada.com/weboffice/web-apps/apps/api/documents/api.js"
        id="1234567890"
        width="100%"
        height="100%"
        :config="config"
        ref="refOffice"
        @DocumentReady="onDocumentReady"
      />
    </div>
    <div class="sidebar">
      <div class="sidebar-item">
        <el-input v-model="text"></el-input>
        <el-button type="primary" @click="handleSeachText">{{ $t('搜索') }}</el-button>
      </div>
      <div class="sidebar-item">
        <el-button type="primary" @click="handleFileUrl">{{ $t('获取文件地址') }}</el-button>
      </div>
      <div class="sidebar-item">
        <el-button type="primary" @click="handleRevision">{{ $t('修订') }}</el-button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.page-wrap {
  display: flex;
  width: 100vw;
  height: 100vh;
  .office {
    flex: 1;
    width: 0;
  }
  .sidebar {
    width: 350px;
    padding: 8px;
    &-item {
      display: flex;
      gap: 0 8px;
      margin-bottom: 12px;
    }
  }
}
</style>
