import path from 'path'
import fs from 'fs'
import axios from 'axios'
import crypto from 'crypto'
import https from 'https'
import { CAIYUN_TRANSLATE_CONFIG } from './caiyunConfig.js'
import { TENCENT_TRANSLATE_CONFIG } from './tencentConfig.js'
import { shouldExcludeFile } from './scripts/i18n-exclude-config.js'

// node执行路径
const dirPath = process.cwd()

// 语言json文件key
const langKey = []

const language = {
  en: 'en', //英语
  ja: 'jp', //日语
  kr: 'kor', //韩语
  fr: 'fra', //法语
  ru: 'ru', //俄语
  sp: 'spa', //西语
  hk: 'zh', //香港繁体（这里用 zh，在腾讯翻译函数中会转换为 zh-TW）
}

// 注意：现在使用统一的排除配置文件

/**
 * @description: 将字符串的第一个字母大写
 * @param {String} str 需要处理的字符串
 * @return {String} 首字母大写的字符串
 */
const capitalizeFirstLetter = (str) => {
  if (!str || typeof str !== 'string') return str
  return str.charAt(0).toUpperCase() + str.slice(1)
}

/**
 * @description: 文件夹遍历
 * @param {content} content/文件夹路径
 * @return {type}
 */
const fileTra = (content) => {
  //根据文件路径读取文件，返回文件列表
  return new Promise((resolve, reject) => {
    fs.readdir(content, async function (err, files) {
      if (err) {
        console.warn(err)
        reject(err)
      } else {
        //遍历读取到的文件列表
        for (let i = 0; i < files.length; i++) {
          //获取当前文件的绝对路径
          const filedir = path.join(content, files[i])

          // 检查是否应该排除此文件/目录
          if (shouldExcludeFile(filedir)) {
            console.log(`跳过排除的文件: ${filedir}`)
            continue
          }

          //根据文件路径获取文件信息
          const isFile = await fileRead(filedir)
          // 如果是文件夹，递归遍历该文件夹下面的文件
          if (!isFile) {
            await fileTra(filedir)
          } else {
            // 读取文件
            const fileContent = fs.readFileSync(filedir, 'utf-8')
            // 提取i18n语言文字
            const lang = getTranslateKey(fileContent)
            lang.forEach((item) => {
              if (langKey.indexOf(item) === -1 || item === '') {
                langKey.push(item)
              }
            })
          }
        }
        resolve(files)
      }
    })
  })
}

/**
 * @description: 匹配t('')或t("")里的内容
 * @param {type}
 * @return {type}
 */
const getTranslateKey = (source) => {
  let result = []
  const reg = /(\$|\.)t\((\'|\")([^\)\'\"]+)(\'|\")(,([^\)\'\"]+))?\)/gm
  let matchKey
  while ((matchKey = reg.exec(source))) {
    result.push(matchKey[3])
  }
  return result
}

/**
 * @description: 判断是文件还是文件夹
 * @param {String} filedir/文件路径
 * @return {type}
 */
const fileRead = (filedir) => {
  return new Promise((resolve, reject) => {
    fs.stat(filedir, function (err, stats) {
      if (err) {
        console.warn($t('获取文件stats失败'))
        reject(err)
      } else {
        //文件
        const isFile = stats.isFile()
        // 文件夹
        const isDir = stats.isDirectory()
        if (isFile) {
          resolve(true)
        }
        if (isDir) {
          resolve(false)
        }
      }
    })
  })
}

/**
 * @description: 翻译接口 - 使用免费的翻译API
 * @param {String} zh/翻译文本
 * @param {String} lang/翻译语种
 * @param {String} i/请求次数，超过三次不再请求
 * @return {type}
 */
const translate = (zh, lang, i = 0) => {
  return new Promise(async (resolve, reject) => {
    const targetLang = language[lang] || language['en']

    console.log(`开始翻译: "${zh}" -> ${lang} (${targetLang})`)

    // 方案1: 使用腾讯翻译接口（优先级最高）
    try {
      const tencentResult = await translateWithTencent(zh, targetLang)
      if (tencentResult && tencentResult !== zh) {
        // 如果翻译的是英文，确保第一个字母大写
        let finalResult = tencentResult
        if (lang === 'en' && finalResult) {
          finalResult = capitalizeFirstLetter(finalResult)
        }
        console.log(`腾讯翻译成功 [${lang}]:`, zh, '-->', finalResult)
        resolve(finalResult)
        return
      }
    } catch (error) {
      console.warn(`腾讯翻译失败 [${lang}]:`, error.message)
    }

    // 如果所有翻译接口都失败，返回原文
    console.warn(`所有翻译接口都失败，返回原文 [${lang}]:`, zh)
    resolve(zh)
  })
}

// 彩云翻译函数
const translateWithCaiyun = (text, targetLang) => {
  return new Promise((resolve, reject) => {
    // 检查配置
    if (!CAIYUN_TRANSLATE_CONFIG.token || CAIYUN_TRANSLATE_CONFIG.token === '') {
      reject(new Error($t('彩云翻译配置不完整，请在 caiyunConfig.js 中设置token')))
      return
    }

    const url = CAIYUN_TRANSLATE_CONFIG.apiUrl
    const token = CAIYUN_TRANSLATE_CONFIG.token

    // 根据目标语言确定翻译方向
    let transType = 'zh2en' // 默认中文到英文
    switch (targetLang) {
      case 'en':
        transType = 'zh2en'
        break
      case 'jp':
      case 'ja':
        transType = 'zh2ja'
        break
      case 'kor':
      case 'ko':
        transType = 'zh2ko'
        break
      case 'fra':
      case 'fr':
        transType = 'zh2fr'
        break
      case 'spa':
      case 'es':
        transType = 'zh2es'
        break
      case 'ru':
        transType = 'zh2ru'
        break
      default:
        transType = 'zh2en'
    }

    const payload = {
      source: [text], // 彩云翻译支持批量翻译，这里传入数组
      trans_type: transType,
      request_id: 'translate_' + Date.now(),
      detect: true,
    }

    axios
      .post(url, payload, {
        headers: {
          'Content-Type': 'application/json',
          'X-Authorization': 'token ' + token,
        },
        timeout: 30000,
      })
      .then((res) => {
        if (res.data && res.data.target && res.data.target.length > 0) {
          let translatedText = res.data.target[0]
          // 如果翻译的是英文，确保第一个字母大写
          if (targetLang === 'en' && translatedText) {
            translatedText = capitalizeFirstLetter(translatedText)
          }
          resolve(translatedText)
        } else {
          reject(new Error($t('彩云翻译响应格式错误')))
        }
      })
      .catch((error) => {
        console.error($t('彩云翻译请求失败:'), error.message)
        reject(error)
      })
  })
}

// 腾讯翻译请求频率控制
let tencentRequestTimes = [] // 记录请求时间戳
const TENCENT_MAX_REQUESTS_PER_SECOND = 4 // 每秒最大请求数
const TENCENT_TIME_WINDOW = 1200 // 时间窗口：

// 腾讯翻译辅助函数
const sha256 = (message, secret = '', encoding) => {
  const hmac = crypto.createHmac('sha256', secret)
  return hmac.update(message).digest(encoding)
}

const getHash = (message, encoding = 'hex') => {
  const hash = crypto.createHash('sha256')
  return hash.update(message).digest(encoding)
}

const getDate = (timestamp) => {
  const date = new Date(timestamp * 1000)
  const year = date.getUTCFullYear()
  const month = ('0' + (date.getUTCMonth() + 1)).slice(-2)
  const day = ('0' + date.getUTCDate()).slice(-2)
  return `${year}-${month}-${day}`
}

// 腾讯翻译函数
const translateWithTencent = (text, targetLang) => {
  return new Promise(async (resolve, reject) => {
    // 检查配置
    if (!TENCENT_TRANSLATE_CONFIG.secretId || !TENCENT_TRANSLATE_CONFIG.secretKey) {
      reject(new Error($t('腾讯翻译配置不完整，请在 tencentConfig.js 中设置SecretId和SecretKey')))
      return
    }

    if (!TENCENT_TRANSLATE_CONFIG.region) {
      reject(new Error($t('腾讯翻译配置不完整，请在 tencentConfig.js 中设置region参数')))
      return
    }

    // 频率控制：确保1秒内最多5次请求
    const now = Date.now()

    // 清理超过时间窗口的请求记录
    tencentRequestTimes = tencentRequestTimes.filter((time) => now - time < TENCENT_TIME_WINDOW)

    // 检查当前时间窗口内的请求数量
    if (tencentRequestTimes.length >= TENCENT_MAX_REQUESTS_PER_SECOND) {
      const oldestRequest = Math.min(...tencentRequestTimes)
      const waitTime = TENCENT_TIME_WINDOW - (now - oldestRequest)
      console.log(`腾讯翻译频率限制，等待 ${waitTime}ms`)
      await new Promise((resolve) => setTimeout(resolve, waitTime))
    }

    // 记录当前请求时间
    tencentRequestTimes.push(Date.now())

    const { secretId, secretKey, region, host, service, action, version } = TENCENT_TRANSLATE_CONFIG

    // 构建请求payload
    let targetLanguage = targetLang

    // 处理香港繁体的特殊情况
    if (targetLang === 'zh') {
      targetLanguage = 'zh-TW' // 腾讯翻译使用 zh-TW 表示繁体中文
    }

    const payload = JSON.stringify({
      SourceText: text,
      Source: 'zh',
      Target: targetLanguage,
      ProjectId: 0,
    })

    const timestamp = parseInt(String(new Date().getTime() / 1000))
    const date = getDate(timestamp)

    // ************* 步骤 1：拼接规范请求串 *************
    const signedHeaders = 'content-type;host'
    const hashedRequestPayload = getHash(payload)
    const httpRequestMethod = 'POST'
    const canonicalUri = '/'
    const canonicalQueryString = ''
    const canonicalHeaders = 'content-type:application/json; charset=utf-8\n' + 'host:' + host + '\n'

    const canonicalRequest =
      httpRequestMethod +
      '\n' +
      canonicalUri +
      '\n' +
      canonicalQueryString +
      '\n' +
      canonicalHeaders +
      '\n' +
      signedHeaders +
      '\n' +
      hashedRequestPayload

    // ************* 步骤 2：拼接待签名字符串 *************
    const algorithm = 'TC3-HMAC-SHA256'
    const hashedCanonicalRequest = getHash(canonicalRequest)
    const credentialScope = date + '/' + service + '/' + 'tc3_request'
    const stringToSign = algorithm + '\n' + timestamp + '\n' + credentialScope + '\n' + hashedCanonicalRequest

    // ************* 步骤 3：计算签名 *************
    const kDate = sha256(date, 'TC3' + secretKey)
    const kService = sha256(service, kDate)
    const kSigning = sha256('tc3_request', kService)
    const signature = sha256(stringToSign, kSigning, 'hex')

    // ************* 步骤 4：拼接 Authorization *************
    const authorization =
      algorithm +
      ' ' +
      'Credential=' +
      secretId +
      '/' +
      credentialScope +
      ', ' +
      'SignedHeaders=' +
      signedHeaders +
      ', ' +
      'Signature=' +
      signature

    // ************* 步骤 5：构造并发起请求 *************
    const headers = {
      Authorization: authorization,
      'Content-Type': 'application/json; charset=utf-8',
      Host: host,
      'X-TC-Action': action,
      'X-TC-Timestamp': timestamp,
      'X-TC-Version': version,
      'X-TC-Region': region, // 确保 Region 参数始终被设置
    }

    const options = {
      hostname: host,
      method: httpRequestMethod,
      headers,
      timeout: 10000,
    }

    const req = https.request(options, (res) => {
      let data = ''
      res.on('data', (chunk) => {
        data += chunk
      })

      res.on('end', () => {
        try {
          const response = JSON.parse(data)
          if (response.Response && response.Response.TargetText) {
            let translatedText = response.Response.TargetText
            // 如果翻译的是英文，确保第一个字母大写
            if (targetLanguage === 'en' && translatedText) {
              translatedText = capitalizeFirstLetter(translatedText)
            }
            resolve(translatedText)
          } else if (response.Response && response.Response.Error) {
            reject(new Error(`腾讯翻译错误: ${response.Response.Error.Message}`))
          } else {
            reject(new Error($t('腾讯翻译响应格式错误')))
          }
        } catch (error) {
          reject(new Error($t('腾讯翻译响应解析失败:') + error.message))
        }
      })
    })

    req.on('error', (error) => {
      console.error($t('腾讯翻译请求失败:'), error.message)
      reject(error)
    })

    req.on('timeout', () => {
      req.destroy()
      reject(new Error($t('腾讯翻译请求超时')))
    })

    req.write(payload)
    req.end()
  })
}

/**
 * @description: 语言抽离并写入中文json文件
 * @param {String} p/需翻译的目录路径
 * @param {String} l/需写入的中文json文件路径
 * @return {type}
 */
const i18nLang = async (p, l) => {
  // 抽离语言
  const tPath = path.join(dirPath, p)
  await fileTra(tPath)
  // 写入语言文件
  const lPath = path.join(dirPath, l)
  let zh = fs.readFileSync(lPath, 'utf-8')
  return new Promise((resolve, reject) => {
    zh = zh ? JSON.parse(zh) : {}
    langKey.forEach((key) => {
      if (Object.keys(zh).indexOf(key) === -1) {
        zh[key] = key
      }
    })
    const err = fs.writeFileSync(lPath, JSON.stringify(zh), 'utf8')
    if (err) {
      reject(err)
    }
    resolve()
  })
}

/**
 * @description: 将中文json文件翻译写入英文json文件
 * @param {String} zh/中文语言文件路径
 * @param {String} en/英文语言文件路径
 * @param {String} lang/语言种类，默认英文
 * @return {type}
 */
const i18nTranslate = async (zh, en, lang = 'en') => {
  const zhPath = path.join(dirPath, zh)
  let zhJson = fs.readFileSync(zhPath, 'utf-8')
  zhJson = zhJson ? JSON.parse(zhJson) : {}
  const enPath = path.join(dirPath, en)
  let enJson = fs.readFileSync(enPath, 'utf-8')
  enJson = enJson ? JSON.parse(enJson) : {}
  const key = []
  Object.keys(zhJson).forEach((k) => {
    if (Object.keys(enJson).indexOf(k) === -1) {
      key.push(k)
    }
  })
  for (let i = 0; i < key.length; i++) {
    const e = await translate(key[i], lang)
    enJson[key[i]] = e
  }
  return new Promise((resolve, reject) => {
    const err = fs.writeFileSync(enPath, JSON.stringify(enJson), 'utf8')
    if (err) {
      reject(err)
    }
    resolve()
  })
}

export { i18nLang, i18nTranslate }
