<template>
  <div class="option-wrap">
    <div class="option-header">
      <div class="option-header-name">
        <span class="option-header-name-txt">{{ $t('审查项') }}</span>
        <!-- <el-tooltip effect="light" :content="$t('请先输入审查项名称，再使用该功能自动生成规则')" placement="top" visible-arrow>
          <i class="icon-pro-question"></i>
        </el-tooltip> -->
      </div>
      <div class="option-header-btn" @click="handleAdd">
        <i class="iconfont icon-is-plus" style="margin-right: 0.25rem; font-size: 0.75rem"></i>
        <span>{{ $t('添加审查项') }}</span>
      </div>
    </div>
    <div ref="refCards" class="option-cards">
      <div v-for="(card, idx) of datas" :key="idx" class="option-card">
        <div class="option-card-header" @click="handleFold(card)">
          <template v-if="!card.isEdit">
            <span class="option-card-header-title"
              ><span style="margin-right: 8px; white-space: nowrap">{{ idx + 1 }}. </span
              ><OverTooltip style="width: 100%" :content="card.ruleTypeName"
            /></span>
            <div class="option-card-header-btn">
              <span class="option-card-header-line"></span>
              <div class="option-card-header-fold" :class="{ 'icon-arrow-fold': card.isFold }">
                <i class="iconfont icon-is-lujing"></i>
              </div>
            </div>
          </template>
          <template v-else>
            <el-input
              v-model="card.ruleTypeName"
              :class="{ 'input-error': card.isError }"
              :placeholder="$t('请输入审查项名称')"
              @click.stop.capture="1 + 1"
              @change="hanldeChange(card)"
            ></el-input>
            <div class="option-card-header-btn">
              <span class="option-card-header-line"></span>
              <div class="option-card-header-fold" :class="{ 'icon-arrow-fold': card.isFold }">
                <i class="iconfont icon-is-lujing"></i>
              </div>
            </div>
          </template>
        </div>
        <template v-if="!card.isFold">
          <span class="option-card-tag">{{ $t('提取规则') }}</span>
          <div class="option-card-rule">
            <span v-if="!card.isEdit">{{ card.ruleItem?.infoExtractPrompts }}</span>
            <el-input
              v-else
              v-model="card.ruleItem!.infoExtractPrompts"
              type="textarea"
              :autosize="{ minRows: 5, maxRows: 8 }"
              :maxlength="1000"
              :placeholder="$t('请输入')"
            ></el-input>
            <div v-if="card.isLoading" class="option-card-loading">
              <i class="el-icon-loading"></i>
              <span>{{ $t('生成中...') }}</span>
            </div>
          </div>
          <span class="option-card-tag" style="margin-top: 8px">{{ $t('审查规则') }}</span>
          <div class="option-card-rule">
            <span v-if="!card.isEdit">{{ card.ruleItem?.riskJudgePrompts }}</span>
            <el-input
              v-else
              v-model="card.ruleItem!.riskJudgePrompts"
              type="textarea"
              :autosize="{ minRows: 5, maxRows: 8 }"
              :maxlength="1000"
              :placeholder="$t('请输入')"
            ></el-input>
            <div v-if="card.isLoading" class="option-card-loading">
              <i class="iconfont icon-is-again"></i>
              <span>{{ $t('生成中...') }}</span>
            </div>
          </div>

          <div v-if="!card.isEdit" class="option-card-footer">
            <el-button @click="handleEdit(card)">{{ $t('编辑') }}</el-button>
            <div class="option-card-footer-handel">
              <span class="option-card-footer-del" @click="handleDel(idx)">
                <i class="iconfont icon-is-shanchu"></i>
                <span>{{ $t('删除') }}</span>
              </span>
            </div>
          </div>
          <div v-else class="option-card-footer">
            <el-tooltip
              effect="light"
              :content="$t('请先输入审查项名称，再使用该功能自动生成规则')"
              placement="top"
              visible-arrow
              :disabled="!!card.ruleTypeName"
            >
              <el-button :disabled="!card.ruleTypeName || card.isLoading" @click="handleAi(card)">
                <span class="ai-btn"
                  ><i class="iconfont icon-is-aiStart" style="margin-right: 4px"></i>{{ $t('AI生成规则') }}</span
                >
              </el-button>
            </el-tooltip>

            <div class="option-card-footer-handel">
              <el-button @click="handleCancel(card, idx)">{{ $t('取消') }}</el-button>
              <el-button type="primary" :disabled="card.isLoading" @click="handleSave(card)">{{
                $t('保存')
              }}</el-button>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import OverTooltip from '@/components/TextOverTip.vue'
import { queryReviewRuleIterm, queryRiskLabelName } from '@/services/contract'
import { RuleTypeItem, RuleTypeList, type IContractRule } from './Model'
import { cloneDeep } from 'lodash-es'
import type { PropType } from 'vue'
import { $t } from '@/utils/i18n'
const refCards = ref()
const emit = defineEmits(['update'])

const props = defineProps({
  optionData: {
    type: Array as PropType<RuleTypeList[]>,
    required: true,
  },
  contractRule: {
    type: Object as PropType<IContractRule>,
    required: true,
  },
})
function handleFold(item: RuleTypeList) {
  item.isFold = !item.isFold
}
const cache = new Map()
function handleEdit(item: RuleTypeList) {
  item.isEdit = true
  cache.set(item.ruleTypeName, item)
  //   item.copyObj = { ...item }
}
const datas = ref<RuleTypeList[]>([])
watch(
  () => props.optionData,
  (_data) => {
    datas.value = cloneDeep(_data)
  },
  {
    immediate: true,
  },
)

function hanldeChange(item: RuleTypeList) {
  const list = props.optionData.filter((node) => node.ruleTypeName && node.ruleTypeName === item.ruleTypeName)
  if (list.length < 2) {
    item.isError = false
  }
}

function handleDel(idx: number) {
  ElMessageBox.confirm('确定删除吗?', $t('提示'), {
    confirmButtonText: $t('确认'),
    cancelButtonText: $t('取消'),
    confirmButtonClass: 'el-button--danger',
    customClass: 'cus-message-box warn',
    showClose: false,
  })
    .then(async () => {
      datas.value.splice(idx, 1)

      emit('update', datas.value)
    })
    .catch(() => {})
}

function handleCancel(item: RuleTypeList, idx: number) {
  item.isEdit = false

  if (item.ruleTypeName.trim() == '') {
    datas.value.splice(idx, 1)
    emit('update', datas.value)
  } else {
    if (cache.get(item.ruleTypeName)) {
      const cacheData = cache.get(item.ruleTypeName)
      item.ruleTypeName = cacheData.ruleTypeName
      item.ruleItem = cacheData.ruleItem
    }
  }
}

async function handleSave(item: RuleTypeList) {
  if (!item.ruleTypeName || !item.ruleItem?.infoExtractPrompts || !item.ruleItem?.riskJudgePrompts) {
    item.isError = true
    ElMessage.error($t('审查项内容不能为空'))
    return
  }
  const list = props.optionData.filter((node) => node.ruleTypeName && node.ruleTypeName === item.ruleTypeName)
  if (list.length > 1) {
    item.isError = true
    ElMessage.error($t('审查项标题不能重复'))
    return
  }
  try {
    item.isLoading = true
    const { code, data, message } = await queryRiskLabelName({
      extractionPrompt: item.ruleItem.infoExtractPrompts,
      reviewItemName: item.ruleTypeName,
    })
    if (code != '200') {
      ElMessage.error(message)
      return
    }
    const { riskLabelName } = data as { riskLabelName: string }
    item.isEdit = false
    item.isError = false
    item.ruleTypeName = riskLabelName
  } catch (error) {
    console.warn('error', error)
  } finally {
    item.isLoading = false
  }
}

function handleAdd() {
  const newRule = new RuleTypeList()
  newRule.isEdit = true
  datas.value.push(newRule)
  emit('update', datas.value)
  setTimeout(() => {
    refCards.value.scrollTop = refCards.value.scrollHeight
  }, 100)
}

async function handleAi(item: RuleTypeList) {
  item.isLoading = true
  const { contractType, reviewPosition, fileUrl, fileCode } = props.contractRule
  const params = {
    contractType,
    reviewPosition,
    fileUrl,
    fileCode,
    reviewItemName: item.ruleTypeName,
  }
  try {
    const { code, message, data } = await queryReviewRuleIterm(params)
    if (code !== '200' && code !== '000000') {
      ElMessage.error(message)
      return
    }
    const { extractionPrompt, riskJudgmentPrompt } = data as {
      extractionPrompt: string
      riskJudgmentPrompt: string
    }
    item.ruleItem.infoExtractPrompts = extractionPrompt
    item.ruleItem.riskJudgePrompts = riskJudgmentPrompt
  } catch (error) {
    console.warn('error', error)
  } finally {
    item.isLoading = false
  }
}
</script>

<style lang="scss" scoped>
.option-wrap {
  position: relative;
  display: flex;
  flex: 1;
  flex-direction: column;
  background-color: #f2f3f6;
  .option-header {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 56px;
    padding: 0 16px;
    background-color: #fff;
    .option-header-name {
      display: flex;
      align-items: center;
      &-txt {
        margin-right: 4px;
        color: #221d39;
      }
    }
    .option-header-btn {
      display: flex;
      align-items: center;
      color: #492ed1;
      cursor: pointer;
      .el-icon-plus {
        margin-right: 4px;
      }
    }
  }
  .option-cards {
    display: flex;
    flex-direction: column;
    padding: 16px;
    overflow-y: auto;
    .option-card {
      padding: 0 16px;
      margin-bottom: 8px;
      background-color: #fff;
      &-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 0;
        font-size: 14px;
        font-weight: 500;
        color: #221d39;
        &-title {
          display: flex;
          flex: 1;
          width: 0;
          margin-right: 20px;
        }
        &-btn {
          display: flex;
          align-items: center;
          cursor: pointer;
        }
        &-fold {
          transform: scale(0.4);
        }
        .icon-arrow-fold {
          transform: scale(0.4) rotate(180deg);
        }
        &-line {
          display: inline-block;
          width: 1px;
          height: 12px;
          margin-right: 4px;
          background-color: #eaebf0;
        }
        .input-error {
          :deep(.el-input__inner) {
            border-color: #f56c6c;
          }
        }
      }
      &-tag {
        display: inline-block;
        padding: 2px 8px;
        margin-top: 0;
        margin-bottom: 8px;
        background-color: #f2f3f6;
        border-radius: 4px;
      }
      &-rule {
        position: relative;
        padding: 0;
        line-height: 22px;
      }
      &-loading {
        position: absolute;
        top: 8px;
        left: 0;
        display: flex;
        align-items: center;
        width: 100%;
        height: calc(100% - 8px);
        padding-left: 8px;
        background-color: #f2f3f6;
        border-radius: 4px;
        .el-icon-loading {
          margin-right: 4px;
          font-size: 18px;
          color: rgb(73 46 209 / 100%);
        }
      }
      &-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 0;
        margin-top: 16px;
        border-top: 1px solid #eaebf0;
        .ai-btn {
          display: flex;
          align-items: center;
          img {
            margin-right: 4px;
          }
        }
        &-handel {
          display: flex;
          align-items: center;
          cursor: pointer;
          .option-card-footer-del {
            display: flex;
            align-items: center;
            &:hover {
              color: #492ed1;
            }
          }
          .icon-is-del {
            margin-right: 4px;
          }
        }
      }
    }
  }
}
</style>
