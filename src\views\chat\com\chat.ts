import { ChatMessage, ChatStatusEnum } from '@/stores/modules/chat/helper'
import type { DifyConversationParams, ConversationCallback, DifyMessageResponse } from '@/types/chat'
import { fetch } from '@/services/sse'
import { useChatStore } from '@/stores'
import JSON5 from 'json5'
import { RESPONSE_CODE_SUCCESS } from '@/constants'
import ChatApi from '@/services/chat'

import { $t } from '@/utils/i18n'
export function useChatService() {
  const chatStore = useChatStore()
  const chatMsgs = ref<ChatMessage[]>([])
  const ctrl = ref()
  const difyConversation = async (
    chatId: string,
    { content, attachInfo, searchTypes, docTypeCode, thinking = 0, webSearch = 0 }: DifyConversationParams,
    updateId: string,
    cb: ConversationCallback,
  ) => {
    ctrl.value = new AbortController()
    const updateMsg = chatMsgs.value.find((msg: any) => msg.id === updateId) as ChatMessage
    if (!updateMsg) {
      ElMessage.warning($t('消息不存在'))
      return
    }
    await fetch(
      '/chat/sseChat',
      {
        chatId,
        content,
        chatAttachInfo: attachInfo,
        searchTypes,
        docTypeCode,
        thinking,
        webSearch,
      },
      {
        async onopen(response: Response) {
          if (response.ok) {
            updateMsg.setStatus(ChatStatusEnum.PREPARE)
          } else if (response.status >= 400 && response.status < 500 && response.status !== 429) {
            throw new Error()
          } else {
            throw new Error()
          }
        },
        async onmessage({ data }: { data: string }) {
          if (!data) {
            throw new Error(data)
          }
          if (updateMsg.status == ChatStatusEnum.PREPARE) {
            updateMsg.setStatus(ChatStatusEnum.PROGRESS)
          }
          if (data) {
            const { event, answer, data: tempData, messageId, taskId }: DifyMessageResponse = JSON5.parse(data)
            if (tempData?.status === 'stopped') chatStore.chatActive.setWorkflowStatus(tempData.status)
            chatStore.chatActive.setTaskId(taskId)
            updateMsg.difyRecevier(event, answer, tempData)
            cb()
            if (event == 'message_end') {
              if (chatStore.chatActive.getWorkflowStatus() != 'stopped') {
                chatMsgs.value.map((it: any, index: number) => {
                  if (index === chatMsgs.value.length - 1) {
                    it.showTools = true
                    it.isLast = true
                  } else {
                    it.showTools = false
                    it.isLast = false
                  }
                  return it
                })
                updateMsg.setStatus(ChatStatusEnum.DONE)
                updateMsg.messageId = messageId
                cb(true)
                ctrl.value.abort()
              }
            }
            if (event == 'error') {
              updateMsg.setStatus(ChatStatusEnum.ERROR)
              cb(true)
            }
          }
        },
        onerror(err: Error) {
          console.log('fetch error', err)
          throw err
        },
        onclose() {
          console.log('close')
          ctrl.value.abort()
          const chatId = chatStore.chatActive.getChatId()
          const targetIndex = chatStore.historyChatList.findIndex((item: any) => item['id'] === chatId)
          if (targetIndex !== -1) {
            chatStore.insertChatInHeader(chatStore.historyChatList[targetIndex])
          }
        },
      },
      ctrl.value,
    ).catch((err: Error) => {
      console.log(err)
      ctrl.value.abort()
    })
  }

  const abortConversation = async (index: number) => {
    const taskId = chatStore.chatActive.getTaskId()
    if (taskId) {
      const res = await ChatApi.stopChat(taskId)
      if (res.code === RESPONSE_CODE_SUCCESS) {
        ctrl.value.abort()

        if (chatMsgs.value && chatMsgs.value.length) {
          const updateMessage = chatMsgs.value[index] as ChatMessage
          updateMessage.setStatus(ChatStatusEnum.STOP)
        }
      }
    } else {
      ElMessage.warning($t('当前对话没有进行中的任务'))
    }
  }
  return {
    chatMsgs,
    difyConversation,
    abortConversation,
  }
}
