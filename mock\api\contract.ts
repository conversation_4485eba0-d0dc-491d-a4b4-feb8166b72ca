export const contracts = [
  {
    url: '/api/bff-iterms-saas/mock/llm/llm-review-rule-list/list-all-rule-by-rule-id',
    method: 'get',
    response: () => ({
      code: '000000',
      message: '操作成功！',
      success: true,
      data: {
        ruleListName: '协 议 书.docx_AI_2025-07-22_14:32:41',
        rulePosition: '服务提供方（乙方）',
        ruleTypeList: [
          {
            allRuleTypeName: null,
            child: [
              {
                allRuleTypeName: '合同主体与授权审查-甲方主体资格与授权范围审查',
                child: null,
                id: '1947545414686380033',
                orderNum: 99,
                parentId: '1947545414686380032',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts:
                    '指合同中甲方的名称、身份及授权范围的描述，包括是否明确甲方为施工方，是否具备签署本协议的合法授权。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts:
                    '若甲方未明确其为施工方或未提供授权文件，存在主体资格瑕疵风险；若授权范围不明确，可能影响协议效力。',
                  ruleLevel: 2,
                  ruleName: '甲方主体资格',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380033',
                  ruleWarn: '甲方主体资格与授权范围审查',
                },
                ruleTypeName: '甲方主体资格与授权范围审查',
              },
              {
                allRuleTypeName: '合同主体与授权审查-乙方主体资格与资质审查',
                child: null,
                id: '1947545414686380034',
                orderNum: 99,
                parentId: '1947545414686380032',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同中乙方的名称、身份及是否具备从事工程资料服务的资质或能力的描述。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若乙方未明确其具备相关资质或能力，可能影响服务履行的合法性与专业性。',
                  ruleLevel: 2,
                  ruleName: '乙方主体资格',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380034',
                  ruleWarn: '乙方主体资格与资质审查',
                },
                ruleTypeName: '乙方主体资格与资质审查',
              },
            ],
            id: '1947545414686380032',
            orderNum: 99,
            parentId: '0',
            parentOrderNum: null,
            parentRuleTypeName: null,
            reviewBasis: null,
            ruleItem: null,
            ruleTypeName: '合同主体与授权审查',
          },
          {
            allRuleTypeName: null,
            child: [
              {
                allRuleTypeName: '服务内容与工作范围审查-乙方服务内容明确性审查',
                child: null,
                id: '1947545414686380036',
                orderNum: 99,
                parentId: '1947545414686380035',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts:
                    '指合同中乙方负责的内业资料种类及工作内容的描述，包括是否明确包括现场签证、报告单、监理回复单、工程联系单、施工日记、工程进度款、竣工图、工程检测等。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若服务内容描述不明确或存在遗漏，可能导致乙方工作范围不清，影响履约。',
                  ruleLevel: 2,
                  ruleName: '服务内容',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380036',
                  ruleWarn: '乙方服务内容明确性审查',
                },
                ruleTypeName: '乙方服务内容明确性审查',
              },
              {
                allRuleTypeName: '服务内容与工作范围审查-乙方工作标准与规范要求审查',
                child: null,
                id: '1947545414686380037',
                orderNum: 99,
                parentId: '1947545414686380035',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同中乙方完成内业资料时应遵循的规范标准，如水利建设工程相关规范的描述。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若未明确规范标准，可能导致乙方工作质量不符合要求，影响工程验收。',
                  ruleLevel: 2,
                  ruleName: '工作标准',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380037',
                  ruleWarn: '乙方工作标准与规范要求审查',
                },
                ruleTypeName: '乙方工作标准与规范要求审查',
              },
              {
                allRuleTypeName: '服务内容与工作范围审查-乙方资料提交与归档责任审查',
                child: null,
                id: '1947545414686380038',
                orderNum: 99,
                parentId: '1947545414686380035',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同中乙方在工程竣工验收后提交完整内业资料并归档的义务描述。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若未明确提交时间、形式或归档责任，可能导致资料缺失或归档不及时，影响工程验收。',
                  ruleLevel: 2,
                  ruleName: '资料提交与归档',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380038',
                  ruleWarn: '乙方资料提交与归档责任审查',
                },
                ruleTypeName: '乙方资料提交与归档责任审查',
              },
            ],
            id: '1947545414686380035',
            orderNum: 99,
            parentId: '0',
            parentOrderNum: null,
            parentRuleTypeName: null,
            reviewBasis: null,
            ruleItem: null,
            ruleTypeName: '服务内容与工作范围审查',
          },
          {
            allRuleTypeName: null,
            child: [
              {
                allRuleTypeName: '甲方资料提供义务审查-甲方资料提供及时性审查',
                child: null,
                id: '1947545414686380040',
                orderNum: 99,
                parentId: '1947545414686380039',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts:
                    '指合同中甲方在施工过程中向乙方提供完成内业资料所需数据的义务描述，如标高、施工时间、施工记录、工程变更资料等。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若未明确资料提供的时间节点或责任，可能导致乙方工作受阻，影响工程进度。',
                  ruleLevel: 2,
                  ruleName: '资料提供及时性',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380040',
                  ruleWarn: '甲方资料提供及时性审查',
                },
                ruleTypeName: '甲方资料提供及时性审查',
              },
              {
                allRuleTypeName: '甲方资料提供义务审查-甲方资料完整性审查',
                child: null,
                id: '1947545414686380041',
                orderNum: 99,
                parentId: '1947545414686380039',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同中甲方提供资料的完整性要求，如是否涵盖所有必要数据和文件。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若未明确资料完整性要求，可能导致乙方无法完成资料整理，影响工程验收。',
                  ruleLevel: 2,
                  ruleName: '资料完整性',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380041',
                  ruleWarn: '甲方资料完整性审查',
                },
                ruleTypeName: '甲方资料完整性审查',
              },
            ],
            id: '1947545414686380039',
            orderNum: 99,
            parentId: '0',
            parentOrderNum: null,
            parentRuleTypeName: null,
            reviewBasis: null,
            ruleItem: null,
            ruleTypeName: '甲方资料提供义务审查',
          },
          {
            allRuleTypeName: null,
            child: [
              {
                allRuleTypeName: '费用与支付条款审查-协议费用计算方式合理性审查',
                child: null,
                id: '1947545414686380043',
                orderNum: 99,
                parentId: '1947545414686380042',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同中协议费用的计算方式，如按工程造价的一定比例计算的描述。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若费用计算方式不明确或比例不合理，可能导致费用争议或乙方利益受损。',
                  ruleLevel: 2,
                  ruleName: '费用计算方式',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380043',
                  ruleWarn: '协议费用计算方式合理性审查',
                },
                ruleTypeName: '协议费用计算方式合理性审查',
              },
              {
                allRuleTypeName: '费用与支付条款审查-支付节点与比例合理性审查',
                child: null,
                id: '1947545414686380044',
                orderNum: 99,
                parentId: '1947545414686380042',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts:
                    '指合同中协议费用分阶段支付的节点及比例，如合同签订时、工程进度款支付时、结算完成时的支付比例。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若支付节点不明确或比例不合理，可能导致乙方资金压力或甲方拖延付款。',
                  ruleLevel: 2,
                  ruleName: '支付节点与比例',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380044',
                  ruleWarn: '支付节点与比例合理性审查',
                },
                ruleTypeName: '支付节点与比例合理性审查',
              },
              {
                allRuleTypeName: '费用与支付条款审查-整改与补充资料的费用承担审查',
                child: null,
                id: '1947545414686380045',
                orderNum: 99,
                parentId: '1947545414686380042',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同中关于整改或补充资料是否产生额外费用的描述。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若未明确整改或补充资料是否产生额外费用，可能导致乙方承担额外成本。',
                  ruleLevel: 2,
                  ruleName: '整改与补充费用',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380045',
                  ruleWarn: '整改与补充资料的费用承担审查',
                },
                ruleTypeName: '整改与补充资料的费用承担审查',
              },
            ],
            id: '1947545414686380042',
            orderNum: 99,
            parentId: '0',
            parentOrderNum: null,
            parentRuleTypeName: null,
            reviewBasis: null,
            ruleItem: null,
            ruleTypeName: '费用与支付条款审查',
          },
          {
            allRuleTypeName: null,
            child: [
              {
                allRuleTypeName: '资料送检与检测责任审查-送检清单提供义务审查',
                child: null,
                id: '1947545414686380047',
                orderNum: 99,
                parentId: '1947545414686380046',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同中乙方是否应提供送检清单供甲方参考的描述。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若未明确乙方提供送检清单的义务，可能导致甲方无法及时送检，影响工程进度。',
                  ruleLevel: 2,
                  ruleName: '送检清单',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380047',
                  ruleWarn: '送检清单提供义务审查',
                },
                ruleTypeName: '送检清单提供义务审查',
              },
              {
                allRuleTypeName: '资料送检与检测责任审查-送检费用承担审查',
                child: null,
                id: '1947545414686380048',
                orderNum: 99,
                parentId: '1947545414686380046',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同中送检费用由甲方承担的描述。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若未明确送检费用由甲方承担，可能导致乙方承担额外成本。',
                  ruleLevel: 2,
                  ruleName: '送检费用',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380048',
                  ruleWarn: '送检费用承担审查',
                },
                ruleTypeName: '送检费用承担审查',
              },
            ],
            id: '1947545414686380046',
            orderNum: 99,
            parentId: '0',
            parentOrderNum: null,
            parentRuleTypeName: null,
            reviewBasis: null,
            ruleItem: null,
            ruleTypeName: '资料送检与检测责任审查',
          },
          {
            allRuleTypeName: null,
            child: [
              {
                allRuleTypeName: '协议终止与费用结清审查-协议终止条件合理性审查',
                child: null,
                id: '1947545414686380050',
                orderNum: 99,
                parentId: '1947545414686380049',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同中协议终止的条件，如工程竣工验收并结清所有费用的描述。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若终止条件不明确或不合理，可能导致协议提前终止或履行障碍。',
                  ruleLevel: 2,
                  ruleName: '协议终止条件',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380050',
                  ruleWarn: '协议终止条件合理性审查',
                },
                ruleTypeName: '协议终止条件合理性审查',
              },
              {
                allRuleTypeName: '协议终止与费用结清审查-费用结清与协议失效关联性审查',
                child: null,
                id: '1947545414686380051',
                orderNum: 99,
                parentId: '1947545414686380049',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同中协议失效与费用结清之间关系的描述。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若未明确费用结清为协议失效的前提，可能导致协议终止后费用纠纷。',
                  ruleLevel: 2,
                  ruleName: '费用结清与协议失效',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380051',
                  ruleWarn: '费用结清与协议失效关联性审查',
                },
                ruleTypeName: '费用结清与协议失效关联性审查',
              },
            ],
            id: '1947545414686380049',
            orderNum: 99,
            parentId: '0',
            parentOrderNum: null,
            parentRuleTypeName: null,
            reviewBasis: null,
            ruleItem: null,
            ruleTypeName: '协议终止与费用结清审查',
          },
          {
            allRuleTypeName: null,
            child: [
              {
                allRuleTypeName: '违约责任与争议解决审查-甲方违约责任审查',
                child: null,
                id: '1947545414686380053',
                orderNum: 99,
                parentId: '1947545414686380052',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同中甲方未按约定提供资料或未按时支付费用的违约责任描述。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若未明确甲方违约责任，可能导致乙方权益无法保障。',
                  ruleLevel: 2,
                  ruleName: '甲方违约责任',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380053',
                  ruleWarn: '甲方违约责任审查',
                },
                ruleTypeName: '甲方违约责任审查',
              },
              {
                allRuleTypeName: '违约责任与争议解决审查-乙方违约责任审查',
                child: null,
                id: '1947545414686380054',
                orderNum: 99,
                parentId: '1947545414686380052',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同中乙方未按约定完成资料整理或未按时提交资料的违约责任描述。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若未明确乙方违约责任，可能导致乙方履约不力，影响工程进度。',
                  ruleLevel: 2,
                  ruleName: '乙方违约责任',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380054',
                  ruleWarn: '乙方违约责任审查',
                },
                ruleTypeName: '乙方违约责任审查',
              },
              {
                allRuleTypeName: '违约责任与争议解决审查-争议解决方式审查',
                child: null,
                id: '1947545414686380055',
                orderNum: 99,
                parentId: '1947545414686380052',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同中争议解决方式的描述，如协商、调解、仲裁或诉讼的约定。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若未明确争议解决方式，可能导致争议处理程序不明确，增加解决成本。',
                  ruleLevel: 2,
                  ruleName: '争议解决方式',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380055',
                  ruleWarn: '争议解决方式审查',
                },
                ruleTypeName: '争议解决方式审查',
              },
            ],
            id: '1947545414686380052',
            orderNum: 99,
            parentId: '0',
            parentOrderNum: null,
            parentRuleTypeName: null,
            reviewBasis: null,
            ruleItem: null,
            ruleTypeName: '违约责任与争议解决审查',
          },
          {
            allRuleTypeName: null,
            child: [
              {
                allRuleTypeName: '合同效力与补充条款审查-合同生效条件审查',
                child: null,
                id: '1947545414686380057',
                orderNum: 99,
                parentId: '1947545414686380056',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同中生效条件的描述，如双方签字盖章后生效的约定。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若未明确生效条件，可能导致合同效力不明确。',
                  ruleLevel: 2,
                  ruleName: '合同生效条件',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380057',
                  ruleWarn: '合同生效条件审查',
                },
                ruleTypeName: '合同生效条件审查',
              },
              {
                allRuleTypeName: '合同效力与补充条款审查-合同正本与副本效力审查',
                child: null,
                id: '1947545414686380058',
                orderNum: 99,
                parentId: '1947545414686380056',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同中正本与副本法律效力的描述。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若未明确正本与副本的法律效力，可能导致合同执行争议。',
                  ruleLevel: 2,
                  ruleName: '合同正本与副本',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380058',
                  ruleWarn: '合同正本与副本效力审查',
                },
                ruleTypeName: '合同正本与副本效力审查',
              },
              {
                allRuleTypeName: '合同效力与补充条款审查-补充协议效力审查',
                child: null,
                id: '1947545414686380059',
                orderNum: 99,
                parentId: '1947545414686380056',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同中补充协议与本协议法律效力的描述。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若未明确补充协议的法律效力，可能导致补充协议无法作为有效依据。',
                  ruleLevel: 2,
                  ruleName: '补充协议',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380059',
                  ruleWarn: '补充协议效力审查',
                },
                ruleTypeName: '补充协议效力审查',
              },
            ],
            id: '1947545414686380056',
            orderNum: 99,
            parentId: '0',
            parentOrderNum: null,
            parentRuleTypeName: null,
            reviewBasis: null,
            ruleItem: null,
            ruleTypeName: '合同效力与补充条款审查',
          },
          {
            allRuleTypeName: null,
            child: [
              {
                allRuleTypeName: '其他条款审查-保密义务审查',
                child: null,
                id: '1947545414686380061',
                orderNum: 99,
                parentId: '1947545414686380060',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同中双方对在履行过程中知悉的商业秘密或其他保密信息的保密义务的描述。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若未明确保密义务，可能导致信息泄露风险。',
                  ruleLevel: 2,
                  ruleName: '保密义务',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380061',
                  ruleWarn: '保密义务审查',
                },
                ruleTypeName: '保密义务审查',
              },
              {
                allRuleTypeName: '其他条款审查-不可抗力条款审查',
                child: null,
                id: '1947545414686380062',
                orderNum: 99,
                parentId: '1947545414686380060',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同中不可抗力的定义及对合同履行影响的描述。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若未明确不可抗力条款，可能导致因不可抗力导致的履约障碍无法处理。',
                  ruleLevel: 2,
                  ruleName: '不可抗力',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380062',
                  ruleWarn: '不可抗力条款审查',
                },
                ruleTypeName: '不可抗力条款审查',
              },
              {
                allRuleTypeName: '其他条款审查-通知与送达方式审查',
                child: null,
                id: '1947545414686380063',
                orderNum: 99,
                parentId: '1947545414686380060',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同中双方通知与送达方式的描述，如书面、电子邮件等。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若未明确通知与送达方式，可能导致信息传递不畅，影响合同履行。',
                  ruleLevel: 2,
                  ruleName: '通知与送达',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380063',
                  ruleWarn: '通知与送达方式审查',
                },
                ruleTypeName: '通知与送达方式审查',
              },
              {
                allRuleTypeName: '其他条款审查-合同解释权审查',
                child: null,
                id: '1947545414686380064',
                orderNum: 99,
                parentId: '1947545414686380060',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同中合同解释权归属的描述，如由甲方或乙方解释。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若未明确合同解释权，可能导致条款理解不一致，引发争议。',
                  ruleLevel: 2,
                  ruleName: '合同解释权',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380064',
                  ruleWarn: '合同解释权审查',
                },
                ruleTypeName: '合同解释权审查',
              },
              {
                allRuleTypeName: '其他条款审查-合同附件审查',
                child: null,
                id: '1947545414686380065',
                orderNum: 99,
                parentId: '1947545414686380060',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同中是否附有相关附件，如工程资料清单、送检清单等。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若未明确附件内容或未附相关附件，可能导致资料整理不完整。',
                  ruleLevel: 2,
                  ruleName: '合同附件',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380065',
                  ruleWarn: '合同附件审查',
                },
                ruleTypeName: '合同附件审查',
              },
            ],
            id: '1947545414686380060',
            orderNum: 99,
            parentId: '0',
            parentOrderNum: null,
            parentRuleTypeName: null,
            reviewBasis: null,
            ruleItem: null,
            ruleTypeName: '其他条款审查',
          },
          {
            allRuleTypeName: null,
            child: [
              {
                allRuleTypeName: '权利义务平衡性审查-乙方权利义务平衡性审查',
                child: null,
                id: '1947545414686380067',
                orderNum: 99,
                parentId: '1947545414686380066',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同中乙方权利与义务是否对等，是否存在单方面加重乙方责任的条款。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若乙方权利义务明显失衡，可能导致乙方履约困难或利益受损。',
                  ruleLevel: 2,
                  ruleName: '权利义务平衡',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380067',
                  ruleWarn: '乙方权利义务平衡性审查',
                },
                ruleTypeName: '乙方权利义务平衡性审查',
              },
              {
                allRuleTypeName: '权利义务平衡性审查-甲方权利义务平衡性审查',
                child: null,
                id: '1947545414686380068',
                orderNum: 99,
                parentId: '1947545414686380066',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同中甲方权利与义务是否对等，是否存在单方面加重甲方责任的条款。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若甲方权利义务明显失衡，可能导致甲方履约困难或利益受损。',
                  ruleLevel: 2,
                  ruleName: '权利义务平衡',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380068',
                  ruleWarn: '甲方权利义务平衡性审查',
                },
                ruleTypeName: '甲方权利义务平衡性审查',
              },
            ],
            id: '1947545414686380066',
            orderNum: 99,
            parentId: '0',
            parentOrderNum: null,
            parentRuleTypeName: null,
            reviewBasis: null,
            ruleItem: null,
            ruleTypeName: '权利义务平衡性审查',
          },
          {
            allRuleTypeName: null,
            child: [
              {
                allRuleTypeName: '法律合规性审查-合同内容合法性审查',
                child: null,
                id: '1947545414686380070',
                orderNum: 99,
                parentId: '1947545414686380069',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同内容是否符合国家法律法规、行业规范及工程资料管理要求。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若合同内容违反法律法规或行业规范，可能导致合同无效或被处罚。',
                  ruleLevel: 1,
                  ruleName: '合同合法性',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380070',
                  ruleWarn: '合同内容合法性审查',
                },
                ruleTypeName: '合同内容合法性审查',
              },
              {
                allRuleTypeName: '法律合规性审查-合同签署主体合法性审查',
                child: null,
                id: '1947545414686380071',
                orderNum: 99,
                parentId: '1947545414686380069',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同签署主体是否具备签署合同的法定资格，如是否具备法人资格或授权委托书。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若签署主体不具备法定资格，可能导致合同无效或效力瑕疵。',
                  ruleLevel: 1,
                  ruleName: '签署主体合法性',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380071',
                  ruleWarn: '合同签署主体合法性审查',
                },
                ruleTypeName: '合同签署主体合法性审查',
              },
            ],
            id: '1947545414686380069',
            orderNum: 99,
            parentId: '0',
            parentOrderNum: null,
            parentRuleTypeName: null,
            reviewBasis: null,
            ruleItem: null,
            ruleTypeName: '法律合规性审查',
          },
          {
            allRuleTypeName: null,
            child: [
              {
                allRuleTypeName: '履约保障与风险控制审查-履约保障措施审查',
                child: null,
                id: '1947545414686380073',
                orderNum: 99,
                parentId: '1947545414686380072',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同中是否约定履约保障措施，如保证金、担保等。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若未约定履约保障措施，可能导致履约风险增加。',
                  ruleLevel: 2,
                  ruleName: '履约保障',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380073',
                  ruleWarn: '履约保障措施审查',
                },
                ruleTypeName: '履约保障措施审查',
              },
              {
                allRuleTypeName: '履约保障与风险控制审查-资料整改与补充责任边界审查',
                child: null,
                id: '1947545414686380074',
                orderNum: 99,
                parentId: '1947545414686380072',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同中乙方在资料整改与补充中的责任范围及期限的描述。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若未明确整改与补充责任边界，可能导致乙方承担额外责任。',
                  ruleLevel: 2,
                  ruleName: '资料整改责任',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380074',
                  ruleWarn: '资料整改与补充责任边界审查',
                },
                ruleTypeName: '资料整改与补充责任边界审查',
              },
              {
                allRuleTypeName: '履约保障与风险控制审查-资料提交期限审查',
                child: null,
                id: '1947545414686380075',
                orderNum: 99,
                parentId: '1947545414686380072',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同中乙方提交资料的时间节点及期限的描述。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若未明确资料提交期限，可能导致乙方工作延误，影响工程进度。',
                  ruleLevel: 2,
                  ruleName: '资料提交期限',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380075',
                  ruleWarn: '资料提交期限审查',
                },
                ruleTypeName: '资料提交期限审查',
              },
            ],
            id: '1947545414686380072',
            orderNum: 99,
            parentId: '0',
            parentOrderNum: null,
            parentRuleTypeName: null,
            reviewBasis: null,
            ruleItem: null,
            ruleTypeName: '履约保障与风险控制审查',
          },
          {
            allRuleTypeName: null,
            child: [
              {
                allRuleTypeName: '合同履行与验收审查-资料验收标准审查',
                child: null,
                id: '1947545414686380077',
                orderNum: 99,
                parentId: '1947545414686380076',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同中资料验收标准的描述，如是否符合工程验收规范或相关主管部门要求。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若未明确资料验收标准，可能导致资料不符合要求，影响工程验收。',
                  ruleLevel: 2,
                  ruleName: '资料验收标准',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380077',
                  ruleWarn: '资料验收标准审查',
                },
                ruleTypeName: '资料验收标准审查',
              },
              {
                allRuleTypeName: '合同履行与验收审查-资料验收程序审查',
                child: null,
                id: '1947545414686380078',
                orderNum: 99,
                parentId: '1947545414686380076',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同中资料验收程序的描述，如是否需经甲方或第三方验收。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若未明确资料验收程序，可能导致验收流程不明确，影响资料归档。',
                  ruleLevel: 2,
                  ruleName: '资料验收程序',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380078',
                  ruleWarn: '资料验收程序审查',
                },
                ruleTypeName: '资料验收程序审查',
              },
              {
                allRuleTypeName: '合同履行与验收审查-资料归档备案责任审查',
                child: null,
                id: '1947545414686380079',
                orderNum: 99,
                parentId: '1947545414686380076',
                parentOrderNum: null,
                parentRuleTypeName: null,
                reviewBasis: null,
                ruleItem: {
                  infoExtractPrompts: '指合同中资料归档备案责任的描述，如是否由甲方负责归档备案。',
                  lawAccording: null,
                  reviewBasis: null,
                  riskJudgePrompts: '若未明确归档备案责任，可能导致资料归档不及时或不完整。',
                  ruleLevel: 2,
                  ruleName: '资料归档备案',
                  ruleReference: null,
                  ruleRisk: null,
                  ruleSuggestion: null,
                  ruleTypeId: '1947545414686380079',
                  ruleWarn: '资料归档备案责任审查',
                },
                ruleTypeName: '资料归档备案责任审查',
              },
            ],
            id: '1947545414686380076',
            orderNum: 99,
            parentId: '0',
            parentOrderNum: null,
            parentRuleTypeName: null,
            reviewBasis: null,
            ruleItem: null,
            ruleTypeName: '合同履行与验收审查',
          },
        ],
      },
    }),
  },
  {
    url: '/api/bff-iterms-saas/mock/ai-reviewruletype/list',
    method: 'post',
    response: () => ({
      code: '000000',
      message: '操作成功！',
      success: true,
      data: {
        contractType: '委托服务合同',
        participants: [
          {
            reviewPosition: '委托方（甲方）',
            participantName: '霞美镇金山村、玉田村、仙河村',
          },
          {
            reviewPosition: '服务提供方（乙方）',
            participantName: '陈小梅',
          },
        ],
      },
    }),
  },
  {
    url: '/api/bff-iterms-saas/mock/llm/llm-get-review-purpose',
    method: 'post',
    response: () => ({
      code: '000000',
      message: 'success',
      success: true,
      data: {
        reviewPurposeList: [
          '审查付款条件与违约责任的合理性',
          '明确交货时间与质量标准的可执行性',
          '评估验收流程与异议处理机制的完整性',
          '确认争议解决条款的适用范围与管辖地',
          '核实合同生效条件与签署要求的合法性',
        ],
      },
    }),
  },
  {
    url: '/api/bff-iterms-saas/mock/customRules/list',
    method: 'post',
    response: () => ({
      code: '000000',
      message: '操作成功！',
      success: true,
      data: [
        {
          ruleCustomId: '1935615333928972289',
          ruleName: '违约惩罚',
        },
        {
          ruleCustomId: '1935615965465325569',
          ruleName: '履约',
        },
        {
          ruleCustomId: '1935670734921838593',
          ruleName: '测试222',
        },
      ],
    }),
  },
  {
    url: '/api/bff-iterms-saas/mock/contract/recent-list',
    method: 'post',
    response: () => ({
      code: '000000',
      message: '操作成功！',
      success: true,
      data: [
        {
          contractId: '1939958369361043457',
          contractName: '协 议 书.docx',
          contractStatus: '编辑中',
          createTime: '2025-07-01 16:04:53',
        },
        {
          contractId: '1939956188603981825',
          contractName: '协 议 书.docx',
          contractStatus: '编辑中',
          createTime: '2025-07-01 15:56:13',
        },
        {
          contractId: '1939955394341216258',
          contractName: '协 议 书.docx',
          contractStatus: '编辑中',
          createTime: '2025-07-01 15:53:03',
        },
      ],
    }),
  },
  {
    url: '/api/bff-iterms-saas/mock/rules/list',
    method: 'get',
    response: () => ({
      code: '000000',
      success: true,
      data: [
        {
          id: '1892151700798705664',
          parentId: 0,
          ruleTypeName: '合同主体',
          parentRuleTypeName: null,
          ruleItem: null,
          child: [
            {
              id: '1892151700798705665',
              parentId: '1892151700798705664',
              ruleTypeName: '买方名称缺失',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 99,
              parentOrderNum: null,
              allRuleTypeName: '合同主体-买方名称缺失',
            },
            {
              id: '1892151700802899968',
              parentId: '1892151700798705664',
              ruleTypeName: '买方地址缺失',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 99,
              parentOrderNum: null,
              allRuleTypeName: '合同主体-买方地址缺失',
            },
          ],
          orderNum: 1,
          parentOrderNum: null,
          allRuleTypeName: null,
        },
        {
          id: '1892151700802899969',
          parentId: 0,
          ruleTypeName: '合同价款',
          parentRuleTypeName: null,
          ruleItem: null,
          child: [
            {
              id: '1892151700802899970',
              parentId: '1892151700802899969',
              ruleTypeName: '合同总金额缺失',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 99,
              parentOrderNum: null,
              allRuleTypeName: '合同价款-合同总金额缺失',
            },
            {
              id: '1892151700802899971',
              parentId: '1892151700802899969',
              ruleTypeName: '合同价款范围未明确',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 99,
              parentOrderNum: null,
              allRuleTypeName: '合同价款-合同价款范围未明确',
            },
            {
              id: '1892151700802899972',
              parentId: '1892151700802899969',
              ruleTypeName: '发票税率缺失',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 99,
              parentOrderNum: null,
              allRuleTypeName: '合同价款-发票税率缺失',
            },
            {
              id: '1892151700802899973',
              parentId: '1892151700802899969',
              ruleTypeName: '合同未明确发票类型',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 99,
              parentOrderNum: null,
              allRuleTypeName: '合同价款-合同未明确发票类型',
            },
            {
              id: '1892151700807094272',
              parentId: '1892151700802899969',
              ruleTypeName: '合同存在价格保证条款',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 99,
              parentOrderNum: null,
              allRuleTypeName: '合同价款-合同存在价格保证条款',
            },
            {
              id: '1892151700807094273',
              parentId: '1892151700802899969',
              ruleTypeName: '合同存在质保金条款',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 99,
              parentOrderNum: null,
              allRuleTypeName: '合同价款-合同存在质保金条款',
            },
          ],
          orderNum: 2,
          parentOrderNum: null,
          allRuleTypeName: null,
        },
        {
          id: '1892151700807094274',
          parentId: 0,
          ruleTypeName: '合同履行',
          parentRuleTypeName: null,
          ruleItem: null,
          child: [
            {
              id: '1892151700807094275',
              parentId: '1892151700807094274',
              ruleTypeName: '卖方收款账户信息缺失',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 1,
              parentOrderNum: null,
              allRuleTypeName: '合同履行-卖方收款账户信息缺失',
            },
            {
              id: '1892151700807094276',
              parentId: '1892151700807094274',
              ruleTypeName: '付款时间缺失',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 2,
              parentOrderNum: null,
              allRuleTypeName: '合同履行-付款时间缺失',
            },
            {
              id: '1892151700807094277',
              parentId: '1892151700807094274',
              ruleTypeName: '付款时间不明确',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 3,
              parentOrderNum: null,
              allRuleTypeName: '合同履行-付款时间不明确',
            },
            {
              id: '1892151700807094278',
              parentId: '1892151700807094274',
              ruleTypeName: '付款方式缺失',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 4,
              parentOrderNum: null,
              allRuleTypeName: '合同履行-付款方式缺失',
            },
            {
              id: '1892151700807094279',
              parentId: '1892151700807094274',
              ruleTypeName: '买方要求先票后款',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 5,
              parentOrderNum: null,
              allRuleTypeName: '合同履行-买方要求先票后款',
            },
            {
              id: '1892151700807094282',
              parentId: '1892151700807094274',
              ruleTypeName: '买方要求先货后款',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 6,
              parentOrderNum: null,
              allRuleTypeName: '合同履行-买方要求先货后款',
            },
            {
              id: '1892151700807094280',
              parentId: '1892151700807094274',
              ruleTypeName: '交付时间缺失',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 7,
              parentOrderNum: null,
              allRuleTypeName: '合同履行-交付时间缺失',
            },
            {
              id: '1892151700807094281',
              parentId: '1892151700807094274',
              ruleTypeName: '交付地点缺失',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 8,
              parentOrderNum: null,
              allRuleTypeName: '合同履行-交付地点缺失',
            },
            {
              id: '1892151700819677184',
              parentId: '1892151700807094274',
              ruleTypeName: '包装责任由卖方承担',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 9,
              parentOrderNum: null,
              allRuleTypeName: '合同履行-包装责任由卖方承担',
            },
            {
              id: '1892151700819677185',
              parentId: '1892151700807094274',
              ruleTypeName: '运输责任由卖方承担',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 10,
              parentOrderNum: null,
              allRuleTypeName: '合同履行-运输责任由卖方承担',
            },
          ],
          orderNum: 3,
          parentOrderNum: null,
          allRuleTypeName: null,
        },
        {
          id: '1892151700819677186',
          parentId: 0,
          ruleTypeName: '质量与验收',
          parentRuleTypeName: null,
          ruleItem: null,
          child: [
            {
              id: '1892151700819677187',
              parentId: '1892151700819677186',
              ruleTypeName: '质量标准缺失',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 99,
              parentOrderNum: null,
              allRuleTypeName: '质量与验收-质量标准缺失',
            },
            {
              id: '1892151700819677188',
              parentId: '1892151700819677186',
              ruleTypeName: '质量保证期限缺失',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 99,
              parentOrderNum: null,
              allRuleTypeName: '质量与验收-质量保证期限缺失',
            },
            {
              id: '1892151700819677189',
              parentId: '1892151700819677186',
              ruleTypeName: '卖方需审核售后服务条款',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 99,
              parentOrderNum: null,
              allRuleTypeName: '质量与验收-卖方需审核售后服务条款',
            },
            {
              id: '1892151700819677190',
              parentId: '1892151700819677186',
              ruleTypeName: '验收标准缺失',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 99,
              parentOrderNum: null,
              allRuleTypeName: '质量与验收-验收标准缺失',
            },
            {
              id: '1892151700819677191',
              parentId: '1892151700819677186',
              ruleTypeName: '验收期限缺失',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 99,
              parentOrderNum: null,
              allRuleTypeName: '质量与验收-验收期限缺失',
            },
            {
              id: '1892151700819677192',
              parentId: '1892151700819677186',
              ruleTypeName: '卖方需审核验收不通过的处理措施',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 99,
              parentOrderNum: null,
              allRuleTypeName: '质量与验收-卖方需审核验收不通过的处理措施',
            },
            {
              id: '1892151700819677193',
              parentId: '1892151700819677186',
              ruleTypeName: '视为验收通过条款缺失',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 99,
              parentOrderNum: null,
              allRuleTypeName: '质量与验收-视为验收通过条款缺失',
            },
          ],
          orderNum: 5,
          parentOrderNum: null,
          allRuleTypeName: null,
        },
        {
          id: '1892151700819677194',
          parentId: 0,
          ruleTypeName: '权利保证与风险转移',
          parentRuleTypeName: null,
          ruleItem: null,
          child: [
            {
              id: '1892151700819677195',
              parentId: '1892151700819677194',
              ruleTypeName: '卖方需审核标的物毁损、灭失的风险转移节点',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 99,
              parentOrderNum: null,
              allRuleTypeName: '权利保证与风险转移-卖方需审核标的物毁损、灭失的风险转移节点',
            },
            {
              id: '1892151700819677196',
              parentId: '1892151700819677194',
              ruleTypeName: '卖方所有权保留条款缺失',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 99,
              parentOrderNum: null,
              allRuleTypeName: '权利保证与风险转移-卖方所有权保留条款缺失',
            },
            {
              id: '1892151700819677197',
              parentId: '1892151700819677194',
              ruleTypeName: '合同存在知识产权无瑕疵保证责任条款',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 99,
              parentOrderNum: null,
              allRuleTypeName: '权利保证与风险转移-合同存在知识产权无瑕疵保证责任条款',
            },
          ],
          orderNum: 6,
          parentOrderNum: null,
          allRuleTypeName: null,
        },
        {
          id: '1892151700819677198',
          parentId: 0,
          ruleTypeName: '不可抗力',
          parentRuleTypeName: null,
          ruleItem: null,
          child: [
            {
              id: '1892151700819677199',
              parentId: '1892151700819677198',
              ruleTypeName: '不可抗力条款缺失',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 99,
              parentOrderNum: null,
              allRuleTypeName: '不可抗力-不可抗力条款缺失',
            },
            {
              id: '1892151700819677200',
              parentId: '1892151700819677198',
              ruleTypeName: '不可抗力免责条款缺失',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 99,
              parentOrderNum: null,
              allRuleTypeName: '不可抗力-不可抗力免责条款缺失',
            },
            {
              id: '1892151700819677201',
              parentId: '1892151700819677198',
              ruleTypeName: '不可抗力前置条件缺失',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 99,
              parentOrderNum: null,
              allRuleTypeName: '不可抗力-不可抗力前置条件缺失',
            },
          ],
          orderNum: 7,
          parentOrderNum: null,
          allRuleTypeName: null,
        },
        {
          id: '1892151700823871488',
          parentId: 0,
          ruleTypeName: '违约与解除',
          parentRuleTypeName: null,
          ruleItem: null,
          child: [
            {
              id: '1892151700823871489',
              parentId: '1892151700823871488',
              ruleTypeName: '买方迟延受领违约责任缺失',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 1,
              parentOrderNum: null,
              allRuleTypeName: '违约与解除-买方迟延受领违约责任缺失',
            },
            {
              id: '1892151700823871490',
              parentId: '1892151700823871488',
              ruleTypeName: '买方逾期付款违约责任缺失',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 2,
              parentOrderNum: null,
              allRuleTypeName: '违约与解除-买方逾期付款违约责任缺失',
            },
            {
              id: '1892151700823871491',
              parentId: '1892151700823871488',
              ruleTypeName: '卖方需审核违约金条款的合理性',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 3,
              parentOrderNum: null,
              allRuleTypeName: '违约与解除-卖方需审核违约金条款的合理性',
            },
            {
              id: '1892151700823871492',
              parentId: '1892151700823871488',
              ruleTypeName: '卖方迟延交付违约责任条款内容确认',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 4,
              parentOrderNum: null,
              allRuleTypeName: '违约与解除-卖方迟延交付违约责任条款内容确认',
            },
            {
              id: '1892151700823871493',
              parentId: '1892151700823871488',
              ruleTypeName: '卖方迟延交付违约金计算基数存在风险',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 5,
              parentOrderNum: null,
              allRuleTypeName: '违约与解除-卖方迟延交付违约金计算基数存在风险',
            },
            {
              id: '1892151700823871494',
              parentId: '1892151700823871488',
              ruleTypeName: '合同存在买方赔偿限额',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 6,
              parentOrderNum: null,
              allRuleTypeName: '违约与解除-合同存在买方赔偿限额',
            },
            {
              id: '1892151700823871495',
              parentId: '1892151700823871488',
              ruleTypeName: '卖方赔偿责任条款合理性确认',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 8,
              parentOrderNum: null,
              allRuleTypeName: '违约与解除-卖方赔偿责任条款合理性确认',
            },
            {
              id: '1892151700823871496',
              parentId: '1892151700823871488',
              ruleTypeName: '违约抵扣条款确认',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 9,
              parentOrderNum: null,
              allRuleTypeName: '违约与解除-违约抵扣条款确认',
            },
            {
              id: '1892151700823871497',
              parentId: '1892151700823871488',
              ruleTypeName: '卖方需审核买方单方解除权的具体条件',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 10,
              parentOrderNum: null,
              allRuleTypeName: '违约与解除-卖方需审核买方单方解除权的具体条件',
            },
          ],
          orderNum: 8,
          parentOrderNum: null,
          allRuleTypeName: null,
        },
        {
          id: '1892151700823871498',
          parentId: 0,
          ruleTypeName: '争议解决',
          parentRuleTypeName: null,
          ruleItem: null,
          child: [
            {
              id: '1892151700823871499',
              parentId: '1892151700823871498',
              ruleTypeName: '争议解决方式缺失',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 99,
              parentOrderNum: null,
              allRuleTypeName: '争议解决-争议解决方式缺失',
            },
            {
              id: '1892151700823871500',
              parentId: '1892151700823871498',
              ruleTypeName: '合同存在或裁或诉条款',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 99,
              parentOrderNum: null,
              allRuleTypeName: '争议解决-合同存在或裁或诉条款',
            },
          ],
          orderNum: 9,
          parentOrderNum: null,
          allRuleTypeName: null,
        },
        {
          id: '1892151700823871501',
          parentId: 0,
          ruleTypeName: '合同通用信息',
          parentRuleTypeName: null,
          ruleItem: null,
          child: [
            {
              id: '1892151700823871502',
              parentId: '1892151700823871501',
              ruleTypeName: '保密期限缺失',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 99,
              parentOrderNum: null,
              allRuleTypeName: '合同通用信息-保密期限缺失',
            },
            {
              id: '1892151700823871503',
              parentId: '1892151700823871501',
              ruleTypeName: '视为送达条款缺失',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 99,
              parentOrderNum: null,
              allRuleTypeName: '合同通用信息-视为送达条款缺失',
            },
            {
              id: '1892151700823871504',
              parentId: '1892151700823871501',
              ruleTypeName: '反商业贿赂条款缺失',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 99,
              parentOrderNum: null,
              allRuleTypeName: '合同通用信息-反商业贿赂条款缺失',
            },
            {
              id: '1892151700823871505',
              parentId: '1892151700823871501',
              ruleTypeName: '合同生效条件缺失',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 99,
              parentOrderNum: null,
              allRuleTypeName: '合同通用信息-合同生效条件缺失',
            },
            {
              id: '1892151700823871506',
              parentId: '1892151700823871501',
              ruleTypeName: '合同签订日期缺失',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 99,
              parentOrderNum: null,
              allRuleTypeName: '合同通用信息-合同签订日期缺失',
            },
            {
              id: '1892151700823871507',
              parentId: '1892151700823871501',
              ruleTypeName: '合同引用失效法律',
              parentRuleTypeName: null,
              ruleItem: null,
              child: null,
              orderNum: 99,
              parentOrderNum: null,
              allRuleTypeName: '合同通用信息-合同引用失效法律',
            },
          ],
          orderNum: 10,
          parentOrderNum: null,
          allRuleTypeName: null,
        },
      ],
    }),
  },
  {
    url: '/api/bff-iterms-saas/mock/contract/list',
    method: 'post',
    response: () => ({
      code: '000000',
      message: '操作成功！',
      success: true,
      data: {
        pageSize: 10,
        pageNum: 1,
        count: 106,
        pages: 0,
        result: [
          {
            contractId: '1939958369361043457',
            contractName: '协 议 书.docx',
            contractStatus: '编辑中',
            createTime: '2025-07-01 16:04:53',
          },
          {
            contractId: '1939956188603981825',
            contractName: '协 议 书.docx',
            contractStatus: '编辑中',
            createTime: '2025-07-01 15:56:13',
          },
          {
            contractId: '1939955394341216258',
            contractName: '协 议 书.docx',
            contractStatus: '编辑中',
            createTime: '2025-07-01 15:53:03',
          },
          {
            contractId: '1938512083190468609',
            contractName: '酒水采购合同.docx',
            contractStatus: '编辑中',
            createTime: '2025-06-27 16:17:51',
          },
          {
            contractId: '1938168029328101377',
            contractName: '买卖合同1.docx',
            contractStatus: '审查完成',
            createTime: '2025-06-26 17:30:42',
          },
          {
            contractId: '1938166976989810689',
            contractName: '买卖合同.docx',
            contractStatus: '编辑中',
            createTime: '2025-06-26 17:26:31',
          },
          {
            contractId: '1938164983814606849',
            contractName: '买卖合同1.docx',
            contractStatus: '编辑中',
            createTime: '2025-06-26 17:18:36',
          },
          {
            contractId: '1938137542281388033',
            contractName: '买卖合同1.docx',
            contractStatus: '编辑中',
            createTime: '2025-06-26 15:29:34',
          },
          {
            contractId: '1938131674068410370',
            contractName: '买卖合同1.docx',
            contractStatus: '审查完成',
            createTime: '2025-06-26 15:06:15',
          },
          {
            contractId: '1938121885712113665',
            contractName: '酒水合同.docx',
            contractStatus: '审查完成',
            createTime: '2025-06-26 14:27:21',
          },
        ],
      },
    }),
  },
  {
    url: '/api/bff-iterms-saas/mock/contract/rule-type/list',
    method: 'post',
    response: () => ({
      code: 200,
      success: true,
      data: [
        {
          typeName: '加工承揽合同',
          itermList: [
            {
              ruleCode: 'J5D0AcbUae7YF6vtUw55xR2d6JCdM5NE',
              rulePosition: '承揽人',
            },
            {
              ruleCode: '9n7xoEtOPSi1ygUqvQQWjb0bPgMc5VmK',
              rulePosition: '定作人',
            },
          ],
        },
        {
          typeName: '通用合同',
          itermList: [
            {
              ruleCode: 'af4c1ua7zplgn9lwbozx3whgzsyye58d',
              rulePosition: '中立',
            },
          ],
        },
        {
          typeName: '买卖合同',
          itermList: [
            {
              ruleCode: 'w6aws5kpa4ohacglejvvpgaamoi1gh42',
              rulePosition: '卖方',
            },
            {
              ruleCode: 'vonv82yq6bwglyclnshvf0gjcx4xtbwj',
              rulePosition: '买方',
            },
          ],
        },
        {
          typeName: '设备买卖合同',
          itermList: [
            {
              ruleCode: 'Wek5cn1ViLFbHhF8V7PMufq5uNgXwaWq',
              rulePosition: '乙方',
            },
          ],
        },
        {
          typeName: '租赁合同',
          itermList: [
            {
              ruleCode: '1r49sk3k2yh9f3c8pz32haj0z34onur7',
              rulePosition: '承租人',
            },
            {
              ruleCode: 'cohb6e3juzn4hxawxjoz9e905cvjw8of',
              rulePosition: '出租人',
            },
          ],
        },
        {
          typeName: '房屋租赁合同',
          itermList: [
            {
              ruleCode: 'WHnVpg2zjiMlrxKk80i2ocv9VVUwUquf',
              rulePosition: '出租人',
            },
            {
              ruleCode: 'pY6HsrhYgf4n2MTb7G6m9nQK5a7KsANF',
              rulePosition: '承租人',
            },
          ],
        },
        {
          typeName: '人力服务合同',
          itermList: [
            {
              ruleCode: 'lhk2uken1ax8u3amg76atmq2ui05to31',
              rulePosition: '受托人',
            },
          ],
        },
        {
          typeName: '保密协议',
          itermList: [
            {
              ruleCode: '9yNOND7JeCKhH3nRlxI2MJUBLKrc84nw',
              rulePosition: '披露方',
            },
            {
              ruleCode: 'SAMo1rqnt5inOu67oorbP5Tqw2yAlkVe',
              rulePosition: '接收方',
            },
          ],
        },
        {
          typeName: '技术服务合同',
          itermList: [
            {
              ruleCode: 'Mf60BaBhdkzZgHjC5kBK8ivJMdebDX6z',
              rulePosition: '委托人',
            },
            {
              ruleCode: 'ezi2PO9WDBkmDI7re2UI95oftyxGfcwR',
              rulePosition: '受托人',
            },
          ],
        },
        {
          typeName: '借款合同',
          itermList: [
            {
              ruleCode: '9kast1Z9XbJIA2IrekrE8kJbDMdcJaIc',
              rulePosition: '出借人',
            },
            {
              ruleCode: '8vRcJmIgCXxp79rCGoStWc3tA8JsblbZ',
              rulePosition: '借款人',
            },
          ],
        },
        {
          typeName: '标准化软件服务合同',
          itermList: [
            {
              ruleCode: 'VR5xPi6DQJ9nbgRYArU4pYarinClxaRQ',
              rulePosition: '使用方',
            },
            {
              ruleCode: 'IdaTqfJTHvfwDeBDzfuxkRt9Q2X49rsU',
              rulePosition: '服务方',
            },
          ],
        },
        {
          typeName: '建设工程合同',
          itermList: [
            {
              ruleCode: 'TlQwHY92xL5UQlpRjq6eeuCnBMRVFCYq',
              rulePosition: '乙方',
            },
          ],
        },
      ],
    }),
  },
]
