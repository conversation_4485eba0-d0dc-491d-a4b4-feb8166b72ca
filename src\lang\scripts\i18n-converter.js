#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { glob } from 'glob'
import { shouldExcludeFile } from './i18n-exclude-config.js'

/**
 * Vue文件中文文本国际化转换脚本
 * 将 <span class="chat-title">查看全部</span> 转换为 <span class="chat-title">{{ $t('查看全部') }}</span>
 */

class I18nConverter {
  constructor(options) {
    this.options = options
    this.chineseTexts = new Set()
    this.existingTranslations = {}
    this.loadExistingTranslations()
  }

  /**
   * 加载现有的翻译文件
   */
  loadExistingTranslations() {
    try {
      if (fs.existsSync(this.options.langFilePath)) {
        const content = fs.readFileSync(this.options.langFilePath, 'utf-8')
        this.existingTranslations = JSON.parse(content)
        console.log(`已加载 ${Object.keys(this.existingTranslations).length} 个现有翻译`)
      }
    } catch (error) {
      console.warn('加载翻译文件失败:', error)
    }
  }

  /**
   * 检测中文字符
   */
  containsChinese(text) {
    return /[\u4e00-\u9fff]/.test(text)
  }

  /**
   * 清理文本，移除多余的空白字符
   */
  cleanText(text) {
    return text.trim().replace(/\s+/g, ' ')
  }

  /**
   * 提取HTML标签内的中文文本
   * 匹配模式：<tag>中文内容</tag> 或 <tag attribute="value">中文内容</tag>
   */
  extractChineseFromHtml(content) {
    // 匹配HTML标签内的纯中文文本（不包含变量插值）
    const htmlTagRegex = /<([a-zA-Z][a-zA-Z0-9\-]*)[^>]*>([^<]*[\u4e00-\u9fff][^<]*)<\/\1>/g

    let match
    let modifiedContent = content

    while ((match = htmlTagRegex.exec(content)) !== null) {
      const fullMatch = match[0]
      const tagName = match[1]
      const innerText = match[2]

      // 跳过已经包含Vue插值的内容
      if (innerText.includes('{{') || innerText.includes('$t(') || innerText.includes('v-')) {
        continue
      }

      const cleanedText = this.cleanText(innerText)

      if (this.containsChinese(cleanedText) && cleanedText.length > 0) {
        this.chineseTexts.add(cleanedText)

        // 构建替换后的内容
        const beforeTag = fullMatch.substring(0, fullMatch.indexOf('>') + 1)
        const afterTag = fullMatch.substring(fullMatch.lastIndexOf('<'))
        const replacement = `${beforeTag}{{ $t('${cleanedText}') }}${afterTag}`

        modifiedContent = modifiedContent.replace(fullMatch, replacement)

        console.log(`发现: "${cleanedText}" -> {{ $t('${cleanedText}') }}`)
      }
    }

    // 处理包含嵌套标签的复杂HTML结构中的中文文本
    modifiedContent = this.extractChineseFromComplexHtml(modifiedContent)

    // 处理 >中文< 模式的文本
    modifiedContent = this.extractChineseFromSimplePattern(modifiedContent)

    // 处理包含Vue插值的混合文本
    modifiedContent = this.extractChineseFromMixedContent(modifiedContent)

    return modifiedContent
  }

  /**
   * 提取复杂HTML结构中的中文文本（包含嵌套标签）
   * 例如：<span><i class="icon"></i>AI生成规则</span>
   */
  extractChineseFromComplexHtml(content) {
    let modifiedContent = content

    // 方法1: 匹配标签后面直接跟着的中文文本（不包含其他标签）
    const simpleTextRegex = />([^<]*[\u4e00-\u9fff][^<]*)</g

    let match
    const replacements = []

    while ((match = simpleTextRegex.exec(content)) !== null) {
      const textContent = match[1]

      // 跳过已经包含Vue插值的内容
      if (textContent.includes('{{') || textContent.includes('$t(') || textContent.includes('v-')) {
        continue
      }

      const cleanedText = this.cleanText(textContent)

      if (this.containsChinese(cleanedText) && cleanedText.length > 0) {
        // 只检查当前批次是否已经处理过，避免重复替换
        if (!replacements.some((r) => r.text === cleanedText)) {
          this.chineseTexts.add(cleanedText)

          // 记录需要替换的内容
          replacements.push({
            original: textContent,
            replacement: `{{ $t('${cleanedText}') }}`,
            text: cleanedText,
          })
        }
      }
    }

    // 方法2: 处理包含嵌套标签的情况，如 <span><i></i>中文</span>
    // 匹配结束标签后面的中文文本
    const nestedTextRegex = /<\/[^>]+>([^<]*[\u4e00-\u9fff][^<]*)</g

    while ((match = nestedTextRegex.exec(content)) !== null) {
      const textContent = match[1]

      // 跳过已经包含Vue插值的内容
      if (textContent.includes('{{') || textContent.includes('$t(') || textContent.includes('v-')) {
        continue
      }

      const cleanedText = this.cleanText(textContent)

      if (this.containsChinese(cleanedText) && cleanedText.length > 0) {
        // 只检查当前批次是否已经处理过，避免重复替换
        if (!replacements.some((r) => r.text === cleanedText)) {
          this.chineseTexts.add(cleanedText)

          // 记录需要替换的内容
          replacements.push({
            original: textContent,
            replacement: `{{ $t('${cleanedText}') }}`,
            text: cleanedText,
          })
        }
      }
    }

    // 执行替换
    for (const replacement of replacements) {
      modifiedContent = modifiedContent.replace(replacement.original, replacement.replacement)
      console.log(`发现复杂HTML: "${replacement.text}" -> {{ $t('${replacement.text}') }}`)
    }

    return modifiedContent
  }

  /**
   * 提取简单模式的中文文本：>中文<
   * 例如：>AI生成规则< -> >{{ $t('AI生成规则') }}<
   */
  extractChineseFromSimplePattern(content) {
    let modifiedContent = content

    // 匹配 >中文< 模式，中文部分不能包含标签
    const simplePatternRegex = />([^<>]*[\u4e00-\u9fff][^<>]*)</g

    let match
    const replacements = []

    while ((match = simplePatternRegex.exec(content)) !== null) {
      const textContent = match[1]

      // 跳过已经包含Vue插值的内容
      if (textContent.includes('{{') || textContent.includes('$t(') || textContent.includes('v-')) {
        continue
      }

      // 跳过属性值（包含=号的内容）
      if (textContent.includes('=')) {
        continue
      }

      const cleanedText = this.cleanText(textContent)

      if (this.containsChinese(cleanedText) && cleanedText.length > 0) {
        // 只检查当前批次是否已经处理过，避免重复替换
        if (!replacements.some((r) => r.text === cleanedText)) {
          this.chineseTexts.add(cleanedText)

          // 记录需要替换的内容
          replacements.push({
            original: textContent,
            replacement: `{{ $t('${cleanedText}') }}`,
            text: cleanedText,
          })
        }
      }
    }

    // 执行替换
    for (const replacement of replacements) {
      modifiedContent = modifiedContent.replace(replacement.original, replacement.replacement)
      console.log(`发现简单模式: "${replacement.text}" -> {{ $t('${replacement.text}') }}`)
    }

    return modifiedContent
  }

  /**
   * 提取包含Vue插值的混合文本中的中文部分
   * 例如：<span>网页{{ index + 1 }}</span> -> <span>{{ $t('网页') }}{{ index + 1 }}</span>
   */
  extractChineseFromMixedContent(content) {
    let modifiedContent = content

    // 匹配标签内包含中文和Vue插值的混合内容
    // 例如：>网页{{ index + 1 }}<
    const mixedContentRegex = />([^<]*[\u4e00-\u9fff][^<]*\{\{[^}]*\}[^<]*)</g

    let match
    const replacements = []

    while ((match = mixedContentRegex.exec(content)) !== null) {
      const fullContent = match[1]

      // 分析内容，提取中文部分和插值部分
      const parts = this.parseMixedContent(fullContent)

      if (parts.length > 1) {
        // 构建新的内容
        let newContent = ''
        for (const part of parts) {
          if (part.type === 'chinese') {
            this.chineseTexts.add(part.text)
            newContent += `{{ $t('${part.text}') }}`
          } else {
            newContent += part.text
          }
        }

        // 记录需要替换的内容
        replacements.push({
          original: fullContent,
          replacement: newContent,
          description: `混合内容: "${fullContent}" -> "${newContent}"`,
        })
      }
    }

    // 执行替换
    for (const replacement of replacements) {
      modifiedContent = modifiedContent.replace(replacement.original, replacement.replacement)
      console.log(`发现${replacement.description}`)
    }

    return modifiedContent
  }

  /**
   * 解析混合内容，分离中文文本和Vue插值
   */
  parseMixedContent(content) {
    const parts = []
    let currentIndex = 0

    // 查找所有的Vue插值 {{ ... }}
    const interpolationRegex = /\{\{[^}]*\}\}/g
    let match

    while ((match = interpolationRegex.exec(content)) !== null) {
      // 添加插值前的文本
      if (match.index > currentIndex) {
        const beforeText = content.substring(currentIndex, match.index)
        const cleanedText = this.cleanText(beforeText)
        if (this.containsChinese(cleanedText) && cleanedText.length > 0) {
          parts.push({
            type: 'chinese',
            text: cleanedText,
          })
        } else if (beforeText.trim()) {
          parts.push({
            type: 'other',
            text: beforeText,
          })
        }
      }

      // 添加插值
      parts.push({
        type: 'interpolation',
        text: match[0],
      })

      currentIndex = match.index + match[0].length
    }

    // 添加最后剩余的文本
    if (currentIndex < content.length) {
      const remainingText = content.substring(currentIndex)
      const cleanedText = this.cleanText(remainingText)
      if (this.containsChinese(cleanedText) && cleanedText.length > 0) {
        parts.push({
          type: 'chinese',
          text: cleanedText,
        })
      } else if (remainingText.trim()) {
        parts.push({
          type: 'other',
          text: remainingText,
        })
      }
    }

    return parts
  }

  /**
   * 提取主模板内容，正确处理嵌套的template标签
   */
  extractMainTemplate(content) {
    // 找到第一个<template>标签
    const templateStartMatch = content.match(/<template[^>]*>/)
    if (!templateStartMatch) {
      return null
    }

    const templateStartIndex = templateStartMatch.index + templateStartMatch[0].length
    let templateDepth = 1
    let currentIndex = templateStartIndex

    // 逐字符扫描，正确计算template标签的嵌套深度
    while (currentIndex < content.length && templateDepth > 0) {
      const remainingContent = content.substring(currentIndex)

      // 查找下一个template相关标签
      const openMatch = remainingContent.match(/<template[^>]*>/)
      const closeMatch = remainingContent.match(/<\/template>/)

      let nextOpenIndex = openMatch ? currentIndex + openMatch.index : Infinity
      let nextCloseIndex = closeMatch ? currentIndex + closeMatch.index : Infinity

      if (nextCloseIndex < nextOpenIndex) {
        // 遇到关闭标签
        templateDepth--
        currentIndex = nextCloseIndex + '</template>'.length
      } else if (nextOpenIndex < nextCloseIndex) {
        // 遇到开启标签
        templateDepth++
        currentIndex = nextOpenIndex + openMatch[0].length
      } else {
        // 没有找到更多标签
        break
      }
    }

    if (templateDepth === 0) {
      const templateContent = content.substring(templateStartIndex, currentIndex - '</template>'.length)
      return [content.substring(templateStartMatch.index, currentIndex), templateContent]
    }

    return null
  }

  /**
   * 提取Vue模板中的其他中文文本
   */
  extractOtherChineseText(content) {
    let modifiedContent = content

    // 匹配属性值中的中文（如 placeholder="中文文本"）
    const attrRegex = /(\w+)=["']([^"']*[\u4e00-\u9fff][^"']*)["']/g
    let match

    while ((match = attrRegex.exec(content)) !== null) {
      const fullMatch = match[0]
      const attrName = match[1]
      const attrValue = match[2]

      // 跳过已经是Vue指令或包含插值的属性
      if (
        attrName.startsWith('v-') ||
        attrName.startsWith(':') ||
        attrName.startsWith('@') ||
        attrValue.includes('$t(') ||
        attrValue.includes('{{')
      ) {
        continue
      }

      const cleanedText = this.cleanText(attrValue)

      if (this.containsChinese(cleanedText) && cleanedText.length > 0) {
        this.chineseTexts.add(cleanedText)

        const replacement = `:${attrName}="$t('${cleanedText}')"`
        modifiedContent = modifiedContent.replace(fullMatch, replacement)

        console.log(`发现属性: "${cleanedText}" -> {{ $t('${cleanedText}') }}`)
      }
    }

    return modifiedContent
  }

  /**
   * 处理JavaScript/TypeScript代码中的中文字符串
   */
  extractChineseFromScript(content) {
    let modifiedContent = content
    const replacements = [] // 存储所有需要替换的位置和内容

    // 匹配字符串中的中文（单引号和双引号）
    const stringRegex = /(['"])((?:[^'"\\\r\n]|\\.)*)(['"])/g
    let match

    while ((match = stringRegex.exec(content)) !== null) {
      const fullMatch = match[0]
      const quote = match[1]
      const stringContent = match[2]
      const endQuote = match[3]

      // 确保引号匹配
      if (quote !== endQuote) continue

      // 检查这个字符串是否已经在$t()调用中
      const matchStart = match.index
      const beforeMatch = content.substring(Math.max(0, matchStart - 50), matchStart)

      // 跳过已经包含$t的字符串内容
      if (stringContent.includes('$t(')) {
        continue
      }

      // 更全面的检查：跳过已经被$t()包装的字符串
      // 检查前面是否有$t(，包括嵌套情况
      if (beforeMatch.match(/\$t\s*\(\s*(\$t\s*\(\s*)*$/)) {
        continue
      }

      // 额外检查：如果前面有多个$t(，说明可能是嵌套调用
      const tCallCount = (beforeMatch.match(/\$t\s*\(/g) || []).length
      const tCloseCount = (beforeMatch.match(/\)/g) || []).length
      if (tCallCount > tCloseCount) {
        // 说明当前字符串在未闭合的$t()调用中
        continue
      }

      // 跳过注释中的中文字符串
      const lineStart = content.lastIndexOf('\n', matchStart) + 1
      const lineContent = content.substring(lineStart, matchStart + fullMatch.length)
      if (lineContent.trim().startsWith('//') || lineContent.includes('/*') || lineContent.includes('*/')) {
        continue
      }

      // 跳过console语句中的中文字符串
      // 检查是否在console调用的参数中
      if (
        beforeMatch.match(
          /console\s*\.\s*(log|error|warn|info|debug|trace|dir|table|time|timeEnd|group|groupEnd|assert)\s*\(/,
        )
      ) {
        // 进一步检查是否在同一行的console调用中
        const lineStart = content.lastIndexOf('\n', matchStart) + 1
        const lineEnd = content.indexOf('\n', matchStart + fullMatch.length)
        const currentLine = content.substring(lineStart, lineEnd === -1 ? content.length : lineEnd)
        if (
          currentLine
            .trim()
            .match(
              /^\s*console\s*\.\s*(log|error|warn|info|debug|trace|dir|table|time|timeEnd|group|groupEnd|assert)\s*\(/,
            )
        ) {
          continue
        }
      }

      // 跳过URL、路径、正则表达式等
      if (
        stringContent.includes('http') ||
        stringContent.includes('/') ||
        stringContent.includes('\\') ||
        stringContent.includes('^') ||
        stringContent.includes('$') ||
        stringContent.includes('*') ||
        stringContent.includes('+') ||
        stringContent.includes('?')
      ) {
        continue
      }

      const cleanedText = this.cleanText(stringContent)

      if (this.containsChinese(cleanedText) && cleanedText.length > 0) {
        this.chineseTexts.add(cleanedText)

        // 记录需要替换的位置和内容
        replacements.push({
          start: matchStart,
          end: matchStart + fullMatch.length,
          original: fullMatch,
          replacement: `$t('${cleanedText}')`,
          text: cleanedText,
        })

        console.log(`发现JS字符串: "${cleanedText}" -> $t('${cleanedText}')`)
      }
    }

    // 从后往前替换，避免位置偏移问题
    replacements.sort((a, b) => b.start - a.start)
    for (const replacement of replacements) {
      modifiedContent =
        modifiedContent.substring(0, replacement.start) +
        replacement.replacement +
        modifiedContent.substring(replacement.end)
    }

    return modifiedContent
  }

  /**
   * 为JS/TS文件添加$t函数导入
   * 注意：只为纯JS/TS文件添加，Vue文件使用全局$t函数
   */
  addI18nImportToJsFile(content) {
    // 检查是否已经导入了$t函数
    if (
      content.includes("import { $t } from '@/utils/i18n'") ||
      content.includes('import { $t }') ||
      content.match(/import\s*{\s*[^}]*\$t[^}]*}\s*from\s*['"]@\/utils\/i18n['"]/)
    ) {
      return content
    }

    let modifiedContent = content

    // 检查现有导入
    const hasImports = /import\s+.*?\s+from\s+['"][^'"]*['"]/.test(content)

    if (hasImports) {
      // 在最后一个import后添加$t导入
      const lastImportMatch = content.match(/import\s+.*?\s+from\s+['"][^'"]*['"]\s*\n?/g)
      if (lastImportMatch) {
        const lastImport = lastImportMatch[lastImportMatch.length - 1]
        const lastImportIndex = content.lastIndexOf(lastImport)
        const insertIndex = lastImportIndex + lastImport.length

        modifiedContent =
          content.slice(0, insertIndex) + "import { $t } from '@/utils/i18n'\n" + content.slice(insertIndex)
      }
    } else {
      // 在文件开头添加导入
      modifiedContent = "import { $t } from '@/utils/i18n'\n\n" + content
    }

    return modifiedContent
  }

  /**
   * 为Vue文件的script标签添加$t函数导入
   * 检查script中是否使用了$t函数，如果使用了但没有导入，则添加导入
   */
  addI18nImportToVueScript(scriptContent, fullScriptTag) {
    // 检查script中是否使用了$t函数
    if (!scriptContent.includes('$t(')) {
      return scriptContent
    }

    // 检查是否已经导入了$t函数
    const hasI18nImport =
      scriptContent.includes("import { $t } from '@/utils/i18n'") ||
      scriptContent.match(/import\s*{\s*[^}]*\$t[^}]*}\s*from\s*['"]@\/utils\/i18n['"]/)

    if (hasI18nImport) {
      return scriptContent
    }

    let modifiedContent = scriptContent

    // 检查是否是setup script
    const isSetupScript = fullScriptTag.includes('setup')

    // 检查现有导入
    const hasImports = /import\s+.*?\s+from\s+['"][^'"]*['"]/.test(scriptContent)

    if (hasImports) {
      // 在最后一个import后添加$t导入
      const lastImportMatch = scriptContent.match(/import\s+.*?\s+from\s+['"][^'"]*['"]\s*\n?/g)
      if (lastImportMatch) {
        const lastImport = lastImportMatch[lastImportMatch.length - 1]
        const lastImportIndex = scriptContent.lastIndexOf(lastImport)
        const insertIndex = lastImportIndex + lastImport.length

        modifiedContent =
          scriptContent.slice(0, insertIndex) + "import { $t } from '@/utils/i18n'\n" + scriptContent.slice(insertIndex)
      }
    } else {
      // 在script内容开头添加导入
      // 找到第一个非空行的位置
      const lines = scriptContent.split('\n')
      let insertIndex = 0

      // 跳过开头的空行和注释
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim()
        if (line && !line.startsWith('//') && !line.startsWith('/*') && !line.startsWith('*')) {
          insertIndex = i
          break
        }
      }

      lines.splice(insertIndex, 0, "import { $t } from '@/utils/i18n'", '')
      modifiedContent = lines.join('\n')
    }

    console.log('已为Vue文件script标签添加$t函数导入')
    return modifiedContent
  }

  /**
   * 处理单个JavaScript/TypeScript文件
   */
  processJsFile(filePath) {
    console.log(`\n处理JS/TS文件: ${filePath}`)

    try {
      const content = fs.readFileSync(filePath, 'utf-8')
      let modifiedContent = content

      // 处理JavaScript/TypeScript代码中的中文字符串
      const originalContent = modifiedContent
      modifiedContent = this.extractChineseFromScript(modifiedContent)

      // 如果内容有变化，检查是否需要添加useI18n导入
      if (modifiedContent !== originalContent && modifiedContent.includes('$t(')) {
        modifiedContent = this.addI18nImportToJsFile(modifiedContent)
      }

      // 如果内容有变化且不是预览模式，则写入文件
      if (modifiedContent !== content && !this.options.dryRun) {
        // 备份原文件
        if (this.options.backup) {
          const backupPath = `${filePath}.backup`
          fs.writeFileSync(backupPath, content, 'utf-8')
          console.log(`已备份到: ${backupPath}`)
        }

        fs.writeFileSync(filePath, modifiedContent, 'utf-8')
        console.log(`已更新文件: ${filePath}`)
      } else if (modifiedContent !== content) {
        console.log(`[预览模式] 文件将被修改: ${filePath}`)
      }
    } catch (error) {
      console.error(`处理文件失败 ${filePath}:`, error)
    }
  }

  /**
   * 处理单个Vue文件
   */
  processVueFile(filePath) {
    console.log(`\n处理文件: ${filePath}`)

    try {
      const content = fs.readFileSync(filePath, 'utf-8')
      let modifiedContent = content

      // 提取template部分进行处理
      const templateMatch = this.extractMainTemplate(content)
      if (templateMatch) {
        const [fullTemplateMatch, templateContent] = templateMatch
        let processedTemplate = templateContent

        // 处理HTML标签内的中文
        processedTemplate = this.extractChineseFromHtml(processedTemplate)

        // 处理属性中的中文
        processedTemplate = this.extractOtherChineseText(processedTemplate)

        // 替换原模板内容
        const newFullTemplate = fullTemplateMatch.replace(templateContent, processedTemplate)
        modifiedContent = content.replace(fullTemplateMatch, newFullTemplate)
      }

      // 提取script部分进行处理
      const scriptMatch = content.match(/<script[^>]*>([\s\S]*?)<\/script>/i)
      if (scriptMatch) {
        const scriptContent = scriptMatch[1]
        let processedScript = scriptContent

        // 处理JavaScript/TypeScript代码中的中文字符串
        const originalScript = processedScript
        processedScript = this.extractChineseFromScript(processedScript)

        // 检查script中是否使用了$t函数，如果使用了但没有导入，则添加导入
        processedScript = this.addI18nImportToVueScript(processedScript, scriptMatch[0])

        // 替换原script内容
        modifiedContent = modifiedContent.replace(scriptMatch[1], processedScript)
      }

      // 如果内容有变化且不是预览模式，则写入文件
      if (modifiedContent !== content && !this.options.dryRun) {
        // 备份原文件
        if (this.options.backup) {
          const backupPath = `${filePath}.backup`
          fs.writeFileSync(backupPath, content, 'utf-8')
          console.log(`已备份到: ${backupPath}`)
        }

        fs.writeFileSync(filePath, modifiedContent, 'utf-8')
        console.log(`已更新文件: ${filePath}`)
      } else if (modifiedContent !== content) {
        console.log(`[预览模式] 文件将被修改: ${filePath}`)
      }
    } catch (error) {
      console.error(`处理文件失败 ${filePath}:`, error)
    }
  }

  /**
   * 更新语言文件
   */
  updateLanguageFile() {
    if (this.chineseTexts.size === 0) {
      console.log('没有发现新的中文文本')
      return
    }

    console.log(`\n发现 ${this.chineseTexts.size} 个新的中文文本`)

    // 合并新的翻译
    const updatedTranslations = { ...this.existingTranslations }

    this.chineseTexts.forEach((text) => {
      if (!updatedTranslations[text]) {
        updatedTranslations[text] = text // 默认翻译为自身
      }
    })

    if (!this.options.dryRun) {
      // 确保目录存在
      const dir = path.dirname(this.options.langFilePath)
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true })
      }

      // 写入更新后的翻译文件
      fs.writeFileSync(this.options.langFilePath, JSON.stringify(updatedTranslations, null, 2), 'utf-8')
      console.log(`已更新语言文件: ${this.options.langFilePath}`)
    } else {
      console.log(`[预览模式] 将添加 ${this.chineseTexts.size} 个新翻译到语言文件`)
    }

    // 显示新增的翻译
    console.log('\n新增的翻译:')
    this.chineseTexts.forEach((text) => {
      console.log(`  "${text}": "${text}"`)
    })
  }

  /**
   * 执行转换
   */
  async convert() {
    console.log('开始Vue文件中文国际化转换...')
    console.log(`源目录: ${this.options.sourceDir}`)

    if (this.options.targetFolders) {
      console.log(`指定文件夹: ${this.options.targetFolders.join(', ')}`)
    }

    console.log(`语言文件: ${this.options.langFilePath}`)
    console.log(`预览模式: ${this.options.dryRun ? '是' : '否'}`)
    console.log(`备份文件: ${this.options.backup ? '是' : '否'}`)

    try {
      let files = []

      // 检查是否是单个文件
      if (
        this.options.sourceDir.endsWith('.vue') ||
        this.options.sourceDir.endsWith('.js') ||
        this.options.sourceDir.endsWith('.ts')
      ) {
        if (fs.existsSync(this.options.sourceDir)) {
          files = [this.options.sourceDir]
        }
      } else {
        // 构建搜索模式
        let searchPatterns = []

        if (this.options.targetFolders && this.options.targetFolders.length > 0) {
          // 如果指定了文件夹，只在这些文件夹中搜索
          for (const folder of this.options.targetFolders) {
            // 支持绝对路径和相对路径
            let folderPath
            if (path.isAbsolute(folder)) {
              folderPath = folder.replace(/\\/g, '/')
            } else {
              folderPath = path.join(this.options.sourceDir, folder).replace(/\\/g, '/')
            }

            console.log(`搜索文件夹: ${folderPath}`)

            searchPatterns.push(`${folderPath}/**/*.vue`, `${folderPath}/**/*.js`, `${folderPath}/**/*.ts`)
          }
        } else {
          // 默认搜索整个源目录
          const basePath = this.options.sourceDir.replace(/\\/g, '/')
          searchPatterns.push(`${basePath}/**/*.vue`, `${basePath}/**/*.js`, `${basePath}/**/*.ts`)
        }

        // 执行搜索
        console.log('搜索模式:', searchPatterns)
        for (const pattern of searchPatterns) {
          const foundFiles = await glob(pattern, { ignore: this.options.excludePatterns })
          console.log(`模式 "${pattern}" 找到 ${foundFiles.length} 个文件`)
          files.push(...foundFiles)
        }

        // 去重
        files = [...new Set(files)]
      }

      console.log(`找到 ${files.length} 个文件`)

      // 处理每个文件
      for (const file of files) {
        // 检查是否应该排除此文件
        if (shouldExcludeFile(file)) {
          console.log(`跳过排除的文件: ${file}`)
          continue
        }

        if (file.endsWith('.vue')) {
          this.processVueFile(file)
        } else if (file.endsWith('.js') || file.endsWith('.ts')) {
          this.processJsFile(file)
        }
      }

      // 更新语言文件
      this.updateLanguageFile()

      console.log('\n转换完成!')
    } catch (error) {
      console.error('转换过程中出现错误:', error)
    }
  }
}

// 默认配置
const defaultOptions = {
  sourceDir: 'src',
  langFilePath: 'src/lang/zh.json',
  backup: false, // 不生成备份文件
  dryRun: false,
  excludePatterns: [
    '**/node_modules/**',
    '**/dist/**',
    '**/*.d.ts',
    // 排除特定的第三方SDK文件
    '**/web-office-sdk-solution-*.js',
    '**/geetestSdk.js',
    // 可以添加更多需要排除的文件模式
    '**/vendor/**',
    '**/lib/**',
    '**/libs/**',
  ],
}

// 命令行参数解析
function parseArgs() {
  const args = process.argv.slice(2)
  const options = { ...defaultOptions }

  for (let i = 0; i < args.length; i++) {
    const arg = args[i]

    switch (arg) {
      case '--source':
      case '-s':
        options.sourceDir = args[++i]
        break
      case '--lang':
      case '-l':
        options.langFilePath = args[++i]
        break
      case '--backup':
        options.backup = true
        break
      case '--folders':
      case '-f':
        // 解析文件夹参数，支持逗号分隔的多个文件夹
        const foldersArg = args[++i]
        if (foldersArg) {
          options.targetFolders = foldersArg.split(',').map((folder) => folder.trim())
        }
        break
      case '--dry-run':
      case '-d':
        options.dryRun = true
        break
      case '--help':
      case '-h':
        console.log(`
Vue文件中文国际化转换工具

用法: node scripts/i18n-converter.js [选项]

选项:
  -s, --source <dir>     源代码目录 (默认: src)
  -l, --lang <file>      语言文件路径 (默认: src/lang/zh.json)
  -f, --folders <dirs>   指定要处理的文件夹，多个文件夹用逗号分隔
  --backup              备份原文件 (默认不备份)
  -d, --dry-run         预览模式，不实际修改文件
  -h, --help            显示帮助信息

自动排除的文件:
  - 第三方SDK: web-office-sdk-solution-*.js, geetestSdk.js
  - 依赖目录: node_modules, dist, vendor, lib, libs
  - 类型定义: *.d.ts

示例:
  node scripts/i18n-converter.js --dry-run
  node scripts/i18n-converter.js --source src/views --backup
  node scripts/i18n-converter.js --source src/views --lang src/lang/zh.json
  node scripts/i18n-converter.js --folders views/chat
  node scripts/i18n-converter.js --folders views/chat,views/user --dry-run
        `)
        process.exit(0)
    }
  }

  return options
}

// 主函数
async function main() {
  const options = parseArgs()
  const converter = new I18nConverter(options)
  await converter.convert()
}

// 执行
main().catch(console.error)
