<script setup lang="ts">
import type { FormInstance, ElScrollbar } from 'element-plus'
import type { IExtractField } from '@/services/extraction'

import { $t } from '@/utils/i18n'
const props = defineProps<{
  extractForm: object
  extractList: IExtractField[]
  tabActive: string
}>()

const emits = defineEmits(['addField', 'removeFields', 'startExtraction', 'startExtractionFulfillment'])

const isExtract = inject('isExtract')
const isFulfillment = inject('isFulfillment')

const scrollbar = ref<InstanceType<typeof ElScrollbar> | null>(null)
const extractFormRef = ref<FormInstance>()

// 新增输入框
const addField = async () => {
  emits('addField')
  // 点击新增字段，滚动到底部
  await nextTick()
  const wrap = scrollbar.value?.wrapRef as HTMLElement
  if (wrap) {
    wrap.scrollTo({
      top: wrap.scrollHeight,
      behavior: 'smooth',
    })
  }
}

// 移除输入框
const removeFields = (index: number) => {
  emits('removeFields', index)
}

const startExtraction = async () => {
  try {
    await extractFormRef.value?.validate()
    if (props.tabActive === 'left') {
      emits('startExtraction')
    } else if (props.tabActive === 'right') {
      emits('startExtractionFulfillment')
    }
  } catch (err) {
    ElMessage.warning($t('请填写所有字段内容'))
  }
}
</script>

<template>
  <div class="form-wrap">
    <el-scrollbar ref="scrollbar" wrap-class="scrollbar-wrapper">
      <!-- :model="extractForm"  -->
      <el-form ref="extractFormRef" :model="{ extractList }" class="extract-form">
        <div
          v-for="(field, index) in extractList"
          :key="index"
          class="extract-form-item"
          @mouseenter="field.isHovered = true"
          @mouseleave="field.isHovered = false"
        >
          <el-form-item
            :prop="`extractList.${index}.label`"
            :rules="[{ required: true, message: '字段内容不能为空', trigger: 'blur' }]"
          >
            <i v-if="field.isHovered" class="icon-is-del iconfont remove-icon" @click.prevent="removeFields(index)"></i>
            <el-input
              v-model="field.label"
              type="textarea"
              class="custom-textarea"
              :placeholder="$t('请输入字段')"
              :autosize="{ minRows: 1, maxRows: 2 }"
              required
            ></el-input>
          </el-form-item>
        </div>
      </el-form>
    </el-scrollbar>
    <div class="extract-add">
      <el-button @click="addField" class="secondary">
        <i class="el-icon-plus add-icon"></i>
        {{ $t('新增字段') }}
      </el-button>
      <el-button type="primary" :disabled="!isExtract || !isFulfillment" @click="startExtraction">{{
        $t('开始抽取')
      }}</el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.form-wrap {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  .el-scrollbar {
    flex: 1;
    overflow: hidden;
  }
  .extract-form {
    width: 100%;
    height: 100%;
    padding: 16px 10px 16px 16px;
    .extract-form-item {
      position: relative;
      :deep(.el-textarea__inner) {
        height: 38px;
        padding: 8px 22px 8px 16px;
        font-size: 14px;
        line-height: 22px;
        color: #262626;
        resize: none;
      }
      .remove-icon {
        position: absolute;
        top: 50%;
        right: 11px;
        z-index: 1;
        cursor: pointer;
        transform: translateY(-50%);
      }
    }
  }
  .extract-add {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 48px;
    padding: 0 10px 10px 16px;
    background-color: #fff;
    .add-icon {
      margin-right: 4px;
      font-weight: 700;
    }
    .el-button:first-child {
      flex: 1;
    }
    .el-button:last-child {
      flex: 2;
    }
  }
}
</style>
