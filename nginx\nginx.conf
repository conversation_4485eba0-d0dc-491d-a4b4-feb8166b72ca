user www-data;
worker_processes auto;
pid /run/nginx.pid;
include /etc/nginx/modules-enabled/*.conf;

events {
	worker_connections 768;
	# multi_accept on;
}

http {

	##
	# Basic Settings
	##

	sendfile on;
	tcp_nopush on;
	tcp_nodelay on;
	keepalive_timeout 65;
	types_hash_max_size 2048;
	server_tokens off;

	# server_names_hash_bucket_size 64;
	# server_name_in_redirect off;

	include /etc/nginx/mime.types;
	default_type application/octet-stream;

	##
	# SSL Settings
	##

	ssl_protocols TLSv1 TLSv1.1 TLSv1.2; # Dropping SSLv3, ref: POODLE
	ssl_prefer_server_ciphers on;

	##
	# Logging Settings
	##

	access_log /var/log/nginx/access.log;
	error_log /var/log/nginx/error.log debug;

	##
	# Gzip Settings
	##

	gzip on;
	gzip_disable "msie6";

	# gzip_vary on;
	# gzip_proxied any;
	# gzip_comp_level 6;
	# gzip_buffers 16 8k;
	# gzip_http_version 1.1;
	# gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

	##
	# Virtual Host Configs
	##
	
	
	

	server {
		listen 80; 
		server_name 127.0.0.1; 

		## HSTS settings by Ford.CHEN
		add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always; 
		add_header Cache-Control max-age=0;

		location / {
			expires 365d;
			if ($request_filename ~* .*\.(?:htm|html)$){
			add_header Cache-Control "private, no-store, no-cache, must-revalidate, proxy-revalidate";
			}
			root  /www/html/;
			index  index.html index.htm;
			try_files $uri $uri/ /index.html;

		}
		
		error_page  404              /index.html;
	}

}
