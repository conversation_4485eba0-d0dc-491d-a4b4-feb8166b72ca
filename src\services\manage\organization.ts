import { post, upload, download } from '@/services'
import type { IResponse } from '@/services'

// 类型定义
interface IAddOrgParams {
  orgName: string
  rootFlag: number
  parentId: number
}

interface IEditOrgParams {
  orgId: number
  orgName: string
  parentId: number
}

interface IDeleteOrgParams {
  orgIdList: string[]
}

interface IGetUserByOrgIdParams {
  orgId: string
  subFlag?: boolean
  searchContent?: string
  page: number
  pageSize: number
}

interface IQueryOrgPageListParams {
  searchContent?: string
  page?: number
  pageSize?: number
}

interface IAddUserParams {
  [key: string]: any
}

interface IEditUserParams {
  [key: string]: any
}

interface IBatchImportUserParams {
  [key: string]: any
}

interface IDeleteUserParams {
  userIdList: number[]
}
interface ICheckUserNameParams {
  userName: string
}
/**
 * 新增组织
 * @param { *orgName *rootFlag *parentId }
 * @returns
 */
export function addOrgRequest<T>(data: IAddOrgParams): Promise<IResponse<T>> {
  return post<T>(`/org/save-org`, data)
}

/**
 * 编辑组织
 * @param { *orgId *orgName *parentId }
 * @returns
 */
export function editOrgRequest<T>(data: IEditOrgParams): Promise<IResponse<T>> {
  return post<T>(`/org/update-org`, data)
}

/**
 * 删除组织
 * @param { *orgIdList[array] }
 * @returns
 */
export function deleteOrgRequest<T>(data: IDeleteOrgParams): Promise<IResponse<T>> {
  return post<T>(`/org/delete-org`, data)
}

/**
 *  通过组织id获取用户
 *  @param { *orgId:组织id, subFlag:是否查询子级, searchContent:搜索内容 }
 */
export function getUserByOrgId<T>(data: IGetUserByOrgIdParams): Promise<IResponse<T>> {
  return post<T>(`/org/get-user-list-by-orgId`, data)
}

/**
 *  分页获取组织信息
 *  @param { searchContent, page, pageSize }
 */
export function queryOrgPageList<T>(data: IQueryOrgPageListParams): Promise<IResponse<T>> {
  return post<T>(`/org/get-org-list`, data)
}

// 新增用户
export function addUserRequest<T>(data: IAddUserParams): Promise<IResponse<T>> {
  return post<T>(`/org/user/save-user`, data)
}

// 编辑用户
export function editUserRequest<T>(data: IEditUserParams): Promise<IResponse<T>> {
  return post<T>(`/org/user/update-user`, data)
}

// 批量导入用户
export function batchImportUser<T>(
  data: IBatchImportUserParams,
  fn?: (progress: number) => void,
): Promise<IResponse<T>> {
  return upload<T>(`/org/user/import-batch-user`, data, fn)
}

// 下载用户导入模板
export function templateDown<T>(data?: any): Promise<IResponse<T>> {
  return download<T>(`/合同产品-用户导入模板.xlsx`, data)
}

/**
 * 下载错误信息
 * @param {*} checkKey
 * @returns
 */
export function downloadErrorInfo(params: any) {
  return download(`/org/user/export-org-user-error`, params)
}

// 删除用户
export function deleteUserRequest<T>(data: IDeleteUserParams): Promise<IResponse<T>> {
  return post<T>(`/org/user/delete-user`, data)
}

// 检查用户名
export function checkUserName<T>(data: ICheckUserNameParams): Promise<IResponse<T>> {
  return post<T>(`/user/checkUserName`, data)
}
