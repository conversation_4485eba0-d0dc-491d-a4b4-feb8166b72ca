declare global {
  interface Window {
    itermsENV: { APP: 'saas' | 'pro' | 'local' }
  }
}

if (import.meta.env.VITE_ENV === 'development') {
  window.itermsENV.APP = import.meta.env.VITE_APP
}

// 版本配置
export const APP_VERSION = window.itermsENV.APP || 'saas'
// 版本判断
export const isPro = APP_VERSION === 'pro'
export const isSaas = APP_VERSION === 'saas'
export const isLocal = APP_VERSION === 'local'

// API 配置
export const BASE_API = '/api/bff-iterms-saas'
export const BASE_URL = '/'
export const PDF_BASE_URL = '/'

export const ENV_MODE = import.meta.env.VITE_ENV || 'development'
export const isDev = ENV_MODE === 'development'
export const isProduction = ENV_MODE === 'production'

// 版本信息
export const VERSION_INFO = {
  version: APP_VERSION,
  env: ENV_MODE,
  isPro,
  isSaas,
  isDev,
  isProduction,
  buildTime: typeof __BUILD_TIME__ !== 'undefined' ? __BUILD_TIME__ : new Date().toISOString(),
}
