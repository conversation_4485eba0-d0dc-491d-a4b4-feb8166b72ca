<script setup lang="ts">
import { useSummaryService } from './useSummaryService'
const { getSummaryOutput, summary, chatRef, answerRef, clear, isLoading, generateSummary, handleChatScroll } =
  useSummaryService()

onBeforeUnmount(() => {
  // clear()
})
</script>
<template>
  <div style="background-color: var(--bg-color)">
    <div class="summary-wrap">
      <span v-if="isLoading && !summary" class="result-summary-content"
        >{{ $t('正在提取摘要中') }}<i class="loading-dot">.</i> <i class="loading-dot">.</i><i class="loading-dot">.</i>
      </span>

      <el-scrollbar
        v-show="summary"
        ref="answerRef"
        class="result-summary-content markdown-body"
        @mousewheel="handleChatScroll"
      >
        <div v-html="getSummaryOutput"></div>
      </el-scrollbar>
    </div>
    <div ref="chatRef" class="box-footer">{{ $t('以上内容由AI生成') }}</div>
  </div>
</template>
<style scoped lang="scss">
.summary-wrap {
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: space-between;
  height: calc(100vh - 4.5rem);
  .result-summary-content {
    display: inline-block;
    width: 100%;
    min-height: 6.75rem;
    padding: 0.5rem 1rem 0;
    margin-bottom: 0;
    overflow-y: auto;
    word-wrap: break-word;
    background: var(--bg-color);
    border-radius: 4px;
  }
}
.box-footer {
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: center;
  width: 33vw;
  padding: 6px 0 4px;
  color: #bdbdbd;
  text-align: center;
}
.loading-dot {
  animation: loading 0.9s infinite ease-in-out;
}
.loading-dot:nth-child(1) {
  animation-delay: 0.3s;
}
.loading-dot:nth-child(2) {
  animation-delay: 0.6s;
}
.loading-dot:nth-child(3) {
  animation-delay: 0.9s;
}

@keyframes loading {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
</style>
