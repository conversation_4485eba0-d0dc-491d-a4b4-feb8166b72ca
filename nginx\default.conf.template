upstream iterms-manage {
    server ${API_HOST};
}

underscores_in_headers on;

server {
    listen      	80;
    # listen  [::]:80;
    server_name  localhost;

    access_log  /var/log/nginx/nginx-access.log  main;

    location / {
        expires 365d;
        if ($request_filename ~* .*\.(?:htm|html)$){
          add_header Cache-Control "private, no-store, no-cache, must-revalidate, proxy-revalidate";
        }
        root   /www/html/;
        index  index.html index.htm;
        try_files $uri $uri/ /index.html;
        access_log off;  # 静态资源不打印日志
    }

    location ^~ /api/ping {
      proxy_http_version 1.1;
      default_type application/json;
      return 200 '{"message": "Hello, world!"}';
    }

    location ^~ /api {
      proxy_http_version 1.1;
      proxy_buffering off;
      proxy_cache off;
      proxy_read_timeout 8h;
      proxy_send_timeout 8h;
      proxy_set_header Host $host;
      add_header Accept-Ranges bytes;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection "Upgrade";
    # proxy_pass http://base-iterms-saas-gateway;

      proxy_pass http://iterms-manage;
    }

}