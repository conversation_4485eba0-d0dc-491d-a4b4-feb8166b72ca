<script setup lang="ts">
import DiffList from './components/DiffList.vue'
import { TabNameEnum } from './types/enums'
import Header from '@/layout/components/Header.vue'
import { useRouter } from 'vue-router'

const active = ref(TabNameEnum.TEXT)

const router = useRouter()
const goto = () => {
  router.push('/createComparison')
}
</script>

<template>
  <Header :title="$t('文本比对')" :show-back="true" @goto="goto"></Header>
  <div class="page-wrap">
    <el-tabs v-model="active">
      <el-tab-pane :label="$t('文本比对')" :name="TabNameEnum.TEXT"></el-tab-pane>
      <el-tab-pane :label="$t('语义比对')" :name="TabNameEnum.SEMANTICS"></el-tab-pane>
    </el-tabs>
    <DiffList v-show="active === TabNameEnum.TEXT" />
    <DiffList v-show="active !== TabNameEnum.TEXT" :is-semantics="true" />
  </div>
</template>

<style scoped lang="scss">
.page-wrap {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: calc(100% - 56px);
  padding: 0 16px;
  background-color: var(--bg-color);
}
:deep(.el-tabs__nav-wrap) {
  &::after {
    display: none;
  }
}
:deep(.el-tabs__header) {
  padding-left: 12px;
  margin: 12px 0 0;
}
</style>
