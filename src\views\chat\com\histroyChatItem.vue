<template>
  <div>
    <div class="recent-chats-item-container" @click="focusChat(chatItem)">
      <div class="recent-chats-item-header">
        <p class="recent-chats-item-title">{{ chatItem.chatTitle }}</p>
        <div class="edit-button">
          <el-popover
            popper-class="edit-button-popover"
            ref="popover"
            v-model="actionVisible"
            placement="bottom-end"
            :offset="0"
            :popper-style="{ minWidth: '104px', padding: '4px', width: 'auto' }"
            :show-arrow="false"
          >
            <template #reference>
              <div>
                <i class="iconfont" style="font-size: 1.25rem; cursor: pointer" @click.stop="actionVisible = true"
                  >&#xe642;</i
                >
              </div>
            </template>
            <div class="action-buttons" v-c="closePopover">
              <div class="action-button" @click.stop="rename">
                <i class="iconfont" style="font-size: 1.25rem">&#xe66b;</i>
                <div class="action-button-text">{{ $t('重命名') }}</div>
              </div>
              <div class="action-button" @click.stop="deleteChat">
                <i class="iconfont" style="font-size: 1.25rem">&#xe673;</i>
                <div class="action-button-text">{{ $t('删除') }}</div>
              </div>
            </div>
          </el-popover>
        </div>
      </div>
      <div class="recent-chats-item-content">
        <p class="recent-chats-item-text">{{ chatItem.lastAnswerContent }}</p>
      </div>
    </div>
    <el-dialog v-model="editVisible" :show-overlay="true" :show-close="true" @close="editVisible = false" width="400px">
      <template #header>
        <div>
          <h2 style="font-size: 1.125rem; color: var(--iterms-text-color)">{{ $t('编辑对话名称') }}</h2>
        </div>
      </template>
      <div><el-input v-model="chatName" class="edit-chat-title-input" :clearable="false" /></div>
      <template #footer>
        <div style="text-align: right">
          <el-button size="small" style="margin-right: 0.5rem" @click="editVisible = false">{{ $t('取消') }}</el-button>
          <el-button type="primary" size="small" @click="confirmEditChat">{{ $t('确定') }}</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-model="deleteVisible"
      :show-overlay="true"
      :show-close="true"
      @close="deleteVisible = false"
      width="400px"
    >
      <template #header>
        <div>
          <h2 style="font-size: 1.125rem; color: var(--iterms-text-color)">{{ $t('确认删除吗？') }}</h2>
        </div>
      </template>
      <div>{{ $t('删除后无法恢复，是否继续删除？') }}</div>
      <template #footer>
        <div style="text-align: right">
          <el-button size="small" style="margin-right: 0.5rem" @click="deleteVisible = false">{{
            $t('取消')
          }}</el-button>
          <el-button type="primary" size="small" @click="confirmDel">{{ $t('删除') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { RESPONSE_CODE_SUCCESS } from '@/constants'
import { ElMessage as Msg } from 'element-plus'
import { useChatStore } from '@/stores'
import { ClickOutside as vC } from 'element-plus'
import { useRouter } from 'vue-router'
import type { IChatItem } from '@/types/chat'
import ChatApi from '@/services/chat'

import { $t } from '@/utils/i18n'
const props = defineProps({
  chatItem: {
    type: Object as () => IChatItem,
    required: true,
  },
})

const emits = defineEmits(['edit-name', 'delete-chat'])

const router = useRouter()
const chatStore = useChatStore()
const actionVisible = ref(false)
const deleteVisible = ref(false)
const editVisible = ref(false)
const chatName = ref(props.chatItem.chatTitle)

const rename = () => {
  editVisible.value = true
  actionVisible.value = false
}
const deleteChat = () => {
  deleteVisible.value = true
  actionVisible.value = false
}

const focusChat = async (item: IChatItem) => {
  chatStore.chatActive.setChatId(item.id)
  router?.push('/chat/' + item.id)
}

const closePopover = () => {
  actionVisible.value = false
}

const confirmEditChat = async () => {
  if (!chatName.value) {
    Msg.error($t('请输入正确的名称'))
    return
  }
  const { code, message } = await ChatApi.editChatTitle(props.chatItem.id, chatName.value)
  if (RESPONSE_CODE_SUCCESS == code) {
    Msg.success(message)
    emits('edit-name', props.chatItem)
    editVisible.value = false
  } else {
    Msg.error(message)
  }
}

const confirmDel = async () => {
  const { code, message } = await ChatApi.deleteChats([props.chatItem.id])
  if (RESPONSE_CODE_SUCCESS == code) {
    chatStore.deleteChatsFromStore(props.chatItem.id)
    Msg.success(message)
    emits('delete-chat', props.chatItem)
    deleteVisible.value = false
  } else {
    Msg.error(message)
  }
}

onMounted(() => {})
</script>

<style scoped>
.recent-chats-item-container {
  display: flex;
  flex-direction: column;
  width: 50vw;
  margin-top: 1.25rem;
  color: #262626;
  cursor: pointer;
  .recent-chats-item-header {
    display: flex;
    align-items: center;
    height: 1.5rem;
    .recent-chats-item-title {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 1rem;
      font-weight: 500;
      line-height: 1.5rem;
      white-space: nowrap;
    }
    .edit-button {
      margin-right: 0;
      margin-left: auto;
    }
  }
  .recent-chats-item-content {
    height: 2.75rem;
    margin-top: 0.5rem;
    .recent-chats-item-text {
      display: -webkit-box;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      font-size: 0.875rem;
      font-weight: 400;
      line-height: 1.25rem;
      color: var(--is-color-7d7b89);
      -webkit-box-orient: vertical;
    }
  }
}
.action-buttons {
  display: flex;
  flex-direction: column;
  .action-button {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 5.75rem;
    height: 2rem;
    padding: 0.25rem 0.5rem;
    cursor: pointer;
    &:hover {
      background-color: var(--input-bg);
      border-radius: 0.25rem;
    }
    svg {
      margin-right: 0.5rem;
    }
    .action-button-text {
      margin-left: 0.25rem;
      font-size: 1rem;
      line-height: 1.5rem;
      color: #262626;
    }
  }
}
.edit-chat-title-input {
  width: 100%;
}
</style>
