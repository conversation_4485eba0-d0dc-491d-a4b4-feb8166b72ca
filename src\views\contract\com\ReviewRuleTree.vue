<script setup lang="ts">
import RuleDialog from './RuleDialog.vue'
import type { ReviewRuleType, ReviewRuleTypeItem } from './Model'
import { delCustomRule } from '@/services/contract'

import { $t } from '@/utils/i18n'
const isAllChecked = ref(false)
const treeRef = ref()
const customTreeRef = ref()
const ruleRef = ref()
const emit = defineEmits(['del', 'create', 'edit'])
const props = defineProps({
  data: {
    type: Array as PropType<ReviewRuleType[]>,
    required: true,
  },
  customData: {
    type: Array as PropType<ReviewRuleType[]>,
    default: () => [],
  },
  ruleCode: {
    type: String,
    default: '',
  },
})
const dataProps = ref({
  children: 'child',
  label: 'ruleTypeName',
})

const showCustomAddDialog = () => {
  ruleRef.value.show(props.ruleCode)
}
const getCheckedNum = (node: any) => {
  if (node.level === 1) {
    return node.childNodes.filter((item: any) => item.checked).length
  } else {
    return ''
  }
}
const handleClick = (node: ReviewRuleTypeItem) => {
  node.isSelected = !node.isSelected
  updateCheckStatus()
}

const getAllNodeIds = (data: any) => {
  const ids: string[] = []
  const traverse = (nodes: Array<any>) => {
    nodes.forEach((node) => {
      ids.push(node.id)
      if (node.child && node.child.length > 0) {
        traverse(node.child)
      }
    })
  }
  traverse(data)
  return ids
}

const handleEdit = (node: ReviewRuleTypeItem) => {
  ruleRef.value.show(props.ruleCode, node)
}

const handleDel = (node: ReviewRuleTypeItem) => {
  ElMessageBox.confirm($t('删除后，则不会审查该项，是否要删除？'), $t('提示'), {
    confirmButtonText: $t('确认'),
    cancelButtonText: $t('取消'),
    confirmButtonClass: 'el-button--danger',
    customClass: 'cus-message-box warn',
    showClose: false,
  })
    .then(async () => {
      const { code } = await delCustomRule(node.id)
      if (code === '000000') {
        emit('del', node.id)
        ElMessage.success($t('删除成功'))
      } else {
        ElMessage.error($t('删除失败'))
      }
    })
    .catch(() => {})
}

const updateCheckStatus = async () => {
  const checkedKeys = await treeRef.value!.getCheckedKeys()
  const allIds = await getAllNodeIds(props.data)
  if (props.customData.length) {
    const customCheckKeys = await customTreeRef.value!.getCheckedKeys()
    const allCustomIds = await getAllNodeIds(props.customData)
    const arr1 = [...checkedKeys, ...customCheckKeys]
    const arr2 = [...allIds, ...allCustomIds]
    isAllChecked.value = arr1.length === arr2.length
  } else {
    isAllChecked.value = checkedKeys.length === allIds.length
  }
}
const selectAll = async () => {
  const allIds = await getAllNodeIds(props.data)
  await treeRef.value!.setCheckedKeys(allIds)
  if (props.customData.length) {
    const allCustomIds = await getAllNodeIds(props.customData)
    if (customTreeRef.value) await customTreeRef.value!.setCheckedKeys(allCustomIds)
  }

  updateCheckStatus()
}
const getSelectedKeys = async () => {
  const allIds = await getAllNodeIds(props.data)
  if (props.customData.length) {
    const allCustomIds = await getAllNodeIds(props.customData)
    return [...allIds, ...allCustomIds]
  } else {
    return allIds
  }
}

const unSelectAll = async () => {
  await treeRef.value!.setCheckedKeys([])
  if (props.customData.length) {
    await customTreeRef.value!.setCheckedKeys([])
  }

  updateCheckStatus()
}
const handleSelectAll = async (val: any) => {
  if (val) {
    selectAll()
  } else {
    unSelectAll()
  }
}
const watchData = watch(
  [() => props.data.length, () => props.customData.length],
  ([len1, len2]) => {
    if (len1 || len2) {
      selectAll()
    }
  },
  {
    immediate: true,
  },
)
const rulesRef = ref()
const scrollToBottom = () => {
  if (rulesRef.value) {
    rulesRef.value.scrollTo(0, rulesRef.value.scrollHeight)
  }
}
defineExpose({
  getSelectedKeys,
  scrollToBottom,
})
onUnmounted(() => {
  watchData()
})
</script>
<template>
  <div style="margin-top: 1rem">
    <div style="display: flex; justify-content: space-between">
      <div style="display: flex; align-items: center; font-size: 0.875rem; color: #262626">
        <span style="margin-right: 1rem">{{ $t('审查规则') }}</span>
        <span>
          <el-checkbox v-model="isAllChecked" @change="handleSelectAll">{{ $t('全选') }}</el-checkbox>
        </span>
      </div>

      <div class="add-rule" @click="showCustomAddDialog">
        <i class="iconfont icon-is-plus" style="margin-right: 0.25rem; font-size: 0.75rem"></i>
        <span>{{ $t('添加审查规则') }}</span>
      </div>
    </div>

    <!------->
    <div ref="rulesRef" style="height: calc(100vh - 290px); overflow: auto">
      <el-tree
        ref="treeRef"
        style="max-width: 600px"
        :data="data"
        node-key="id"
        show-checkbox
        :props="dataProps"
        default-expand-all
        @check="handleClick"
      >
        <template #default="{ node, data }">
          <div class="custom-tree-node">
            <div>{{ data.ruleTypeName }}</div>
            <div v-if="data.child" style="display: flex; width: 100px">
              <span class="custom-tree-node-checked">{{ $t('已选择') }}{{ getCheckedNum(node) }}{{ $t('项') }}</span>
              <i class="iconfont icon-is-arrow-right node-arrow" />
            </div>
          </div>
        </template>
      </el-tree>

      <!--------->
      <el-divider v-if="customData.length" />
      <el-tree
        v-if="customData.length"
        ref="customTreeRef"
        style="max-width: 600px"
        :data="customData"
        node-key="id"
        show-checkbox
        :props="dataProps"
        default-expand-all
        @check="handleClick"
      >
        <template #default="{ node, data }">
          <div class="custom-tree-node" style="padding-right: 0.5rem">
            <div style="display: flex; align-items: center; justify-content: space-between; width: 100%">
              <div>{{ data.ruleTypeName }}</div>
              <div v-if="!data.child" class="node-icon">
                <i class="iconfont icon-is-rename" @click.stop="handleEdit(data)" />
                <i class="iconfont icon-is-shanchu" @click.stop="handleDel(data)" />
              </div>
            </div>
            <div v-if="data.child" style="display: flex; align-items: center; width: 6.25rem">
              <span class="custom-tree-node-checked">{{ $t('已选择') }}{{ getCheckedNum(node) }}{{ $t('项') }}</span>
              <i class="iconfont icon-is-arrow-right node-arrow" />
            </div>
          </div>
        </template>
      </el-tree>
    </div>
  </div>
  <RuleDialog
    ref="ruleRef"
    @create="emit('create')"
    @edit="
      (data) => {
        emit('edit', data)
      }
    "
  ></RuleDialog>
</template>
<style lang="scss" scoped>
.custom-tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.node-icon {
  display: none;
  .icon-is-rename {
    margin-right: 8px;
    color: #bdbdbd;
  }
  .icon-is-shanchu {
    color: #bdbdbd;
  }
}
.add-rule {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  color: var(--is-color-773bef);
  cursor: pointer;
}
:deep(.el-tree-node__content:has(.expanded)) {
  .node-arrow {
    transform: rotate(-270deg);
  }
}
:deep(.el-tree-node) {
  margin-bottom: 6px;
}
:deep(.el-tree-node__expand-icon) {
  visibility: hidden;
  padding: 3px !important;
}
:deep(.el-tree-node__content) {
  &:hover {
    background-color: #f1f5ff;
    .node-icon {
      display: flex;
      align-items: center;
    }
  }
}
:deep(.el-tree > div) {
  border: 1px solid var(--page-header-line);
  border-radius: 4px;
}
:deep(.el-tree > div > div) {
  &:first-child {
    height: 38px;
  }
}
</style>
