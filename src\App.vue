<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import en from 'element-plus/es/locale/lang/en'
import zhTw from 'element-plus/es/locale/lang/zh-tw'
import { useLanguage } from '@/composables/useLanguage'
import { initGlobalT } from '@/utils/i18n'

// 初始化语言设置
const { initLanguage } = useLanguage()
const { locale } = useI18n()

// Element Plus 语言包映射
const elementLocaleMap = {
  zh_CN: zhCn,
  en_US: en,
  zh_HK: zhTw,
}

// 计算当前Element Plus语言包
const elementLocale = computed(() => {
  return elementLocaleMap[locale.value as keyof typeof elementLocaleMap] || zhCn
})

onMounted(() => {
  // 初始化语言设置
  initLanguage()
  // 初始化全局翻译函数
  initGlobalT()
})
</script>

<template>
  <el-config-provider :locale="elementLocale">
    <router-view />
  </el-config-provider>
</template>
