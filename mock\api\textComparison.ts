export const textComparison = [
  {
    url: '/api/bff-iterms-saas/compare/recent',
    method: 'get',
    response: () => ({
      code: '000000',
      data: [
        {
          compareMessage: '',
          createBy: 'zwm_demo',
          createTime: '2025-07-01 15:36:27',
          id: '1939951214163124226',
          leftFilename: '1不良品暂存设备合同.pdf',
          leftPageTotal: 6,
          progressNum: 100,
          rightFilename: '1不良品暂存设备合同_纯图版.pdf',
          rightPageTotal: 6,
          similarity: 0,
          updateTime: '2025-07-01 15:36:27',
        },
        {
          compareMessage: '',
          createBy: 'zwm_demo',
          createTime: '2025-06-18 19:43:24',
          id: '1935302321853153281',
          leftFilename: '5页合同.doc',
          leftPageTotal: 5,
          progressNum: 100,
          rightFilename: '51.页合同.docx',
          rightPageTotal: 1,
          similarity: 14,
          updateTime: '2025-06-18 19:43:28',
        },
        {
          compareMessage: '',
          createBy: 'zwm_demo',
          createTime: '2025-06-13 16:34:39',
          id: '1933442878412288002',
          leftFilename: '11111.docx',
          leftPageTotal: 1,
          progressNum: 100,
          rightFilename: '11111XXdsax.docx',
          rightPageTotal: 1,
          similarity: 81,
          updateTime: '2025-06-13 16:34:39',
        },
      ],
      message: '请求成功',
      success: true,
    }),
  },
]
