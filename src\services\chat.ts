import request, { post, get } from '@/services'
import type { IResponse } from '@/services'

// 接口参数类型定义
export interface IGetChatsListParams {
  currentPageNo?: number
  pageSize?: number
  chatTitle?: string
}

export interface ICompletionsParams {
  chatId: string
  callback: (progressEvent: any) => void
}

export interface IFeedbackParams {
  chatId: string
  messageId: string
  feedback: {
    rating: string | null
    type?: string
    content?: string
  }
}

export function completions<T>(params: ICompletionsParams): Promise<IResponse<T>> {
  const { chatId, callback } = params
  return get<T>('/chat/completions', { chatId }, { onDownloadProgress: callback })
}

export function chatMessages<T>(chatId: string): Promise<IResponse<T>> {
  return post<T>('/chat/getChatMessages', { chatId })
}

export function deleteLatest<T>(chatId: string): Promise<IResponse<T>> {
  return post<T>('/chat/deleteLatestChatMessage', { chatId })
}

export function getChatsList<T>(params: IGetChatsListParams = {}): Promise<IResponse<T>> {
  const { currentPageNo = 1, pageSize = 20, chatTitle = '' } = params
  return post<T>('/chat/page', { currentPageNo, pageSize, chatTitle })
}

export function getModels<T>(): Promise<IResponse<T>> {
  return post<T>('/model/list')
}

export function getChatDetail<T>(chatId: string): Promise<IResponse<T>> {
  return post<T>('/chat/detail', { chatId })
}

export function deleteChats<T>(chatIds: string[]): Promise<IResponse<T>> {
  return post<T>('/chat/delete', { chatIds })
}

export function getChatAssistant<T>(): Promise<IResponse<T>> {
  return post<T>('/chat/assistantList')
}

export function getTemplateAssistant<T>(docTypeCode: string): Promise<IResponse<T>> {
  return post<T>('/docType/assistant', { docTypeCode })
}

export function getDocumentLabel<T>(): Promise<IResponse<T>> {
  return post<T>('/label/list')
}

export function getDocumentType<T>(): Promise<IResponse<T>> {
  return post<T>('/docType/list')
}

export function getChatFile(uuid: string): Promise<ArrayBuffer> {
  return request({
    url: `/chat/get-file/${uuid}`,
    method: 'get',
    responseType: 'arraybuffer',
  })
}

export function createDifyChat<T>(chatTitle: string): Promise<IResponse<T>> {
  return post<T>('/chat/createChat', { chatTitle })
}

export function getSuggest<T>(chatId: string, messageId: string): Promise<IResponse<T>> {
  return post<T>('/chat/getMessageSuggested', { chatId, messageId })
}

export function editChatTitle<T>(chatId: string, chatTitle: string): Promise<IResponse<T>> {
  return post<T>('/chat/editChat', { chatId, chatTitle })
}

export function stopChat<T>(taskId: string): Promise<IResponse<T>> {
  return post<T>('/chat/stopChat', { taskId })
}

export function docDetail<T>(docId: string): Promise<IResponse<T>> {
  return post<T>('/case/detail', { docId })
}

export function feedback<T>(params: IFeedbackParams): Promise<IResponse<T>> {
  const { chatId, messageId, feedback } = params
  return post<T>('/chat/messageFeedback', { chatId, messageId, feedback })
}

export default {
  completions,
  chatMessages,
  deleteLatest,
  getChatsList,
  getModels,
  getChatDetail,
  deleteChats,
  getChatAssistant,
  getTemplateAssistant,
  getDocumentLabel,
  getDocumentType,
  getChatFile,
  createDifyChat,
  getSuggest,
  editChatTitle,
  stopChat,
  docDetail,
  feedback,
}
