<script setup lang="ts">
import type { IExtractField } from '@/services/extraction'

defineProps<{
  extractList: IExtractField[]
}>()

const emits = defineEmits(['searchPosition', 'redraw'])

const handleLocation = (content: string) => {
  emits('searchPosition', content)
}
const redraw = () => {
  emits('redraw', 'left')
}
</script>

<template>
  <div class="after-extract-wrap">
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <div class="after-extract-inner">
        <div v-for="(field, index) in extractList" :key="index" class="after-extract-item">
          <div class="after-extract-item-title">
            <span>{{ field.label }}</span>
            <span class="btn" @click="handleLocation(field.value)"
              ><i class="icon-is-position"></i>{{ $t('原文') }}</span
            >
          </div>
          <div class="after-extract-item-context">{{ field.value }}</div>
        </div>
      </div>
    </el-scrollbar>
    <div class="re-extract">
      <el-button @click="redraw" class="secondary">{{ $t('返回重抽') }}</el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.after-extract-wrap {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 16px 0 0 16px;
  .el-scrollbar {
    height: 100%;
    .after-extract-inner {
      padding-right: 10px;
    }
    .after-extract-item {
      margin-bottom: 16px;
      font-size: 14px;
      &-title {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        font-weight: 500;
        line-height: 20px;
        color: #221d39;
        .btn {
          display: flex;
          align-items: center;
          line-height: 20px;
          cursor: pointer;
          i {
            margin-right: 4px;
          }
          &:hover {
            color: var(--main-font);
          }
        }
      }
      &-context {
        padding: 8px;
        font-weight: 400;
        line-height: 24px;
        color: #464359;
        text-align: justify;
        background-color: #f2f3f6;
        border-radius: 4px;
      }
    }
  }
  .re-extract {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 48px;
    padding: 0 10px 16px 16px;
    background-color: #fff;
    .el-button {
      width: 100%;
    }
  }
}
</style>
