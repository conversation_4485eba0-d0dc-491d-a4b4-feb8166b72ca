import { createI18n } from 'vue-i18n'
import en from './en.json'
import zh from './zh.json'
import hk from './zh-HK.json'

// 定义消息类型
export interface MessageSchema {
  [key: string]: string
}

const messages = {
  en_US: en,
  zh_CN: zh,
  zh_HK: hk,
}

const lang = 'zh_CN' as const
// const lang = 'zh_CN' as const

const i18n = createI18n<[MessageSchema], 'zh_CN' | 'en_US' | 'zh_HK'>({
  locale: lang,
  fallbackLocale: 'zh_CN',
  messages,
  legacy: false, // 使用 Composition API 模式
})

export default i18n
