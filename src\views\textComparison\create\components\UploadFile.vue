<script setup lang="ts">
import { getFileSuffixByFileName } from './utils'
import type { UploadFile, UploadInstance } from 'element-plus'
import SvgIcon from '@/components/Icons.vue'

import { $t } from '@/utils/i18n'
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  maxFile: {
    type: Number,
    default: 100,
  },
  limitFileType: {
    type: Array,
    default() {
      return ['doc', 'docx', 'pdf', 'png', 'jpg', 'jpeg']
    },
  },
  sourceType: {
    type: Number,
    default: 1,
  },
})

const emits = defineEmits(['afterUpload'])

const originData = ref({
  loading: false,
  fileName: '',
  extension: '',
  docId: null,
  text: '',
})
const fileList = ref([])
const curFile = ref<Blob>()
const refUpload = ref<UploadInstance>()

const acceptList = computed(() => {
  return props.limitFileType.map((v) => `.${v}`).join(',')
})

const messageError = computed(() => {
  return props.limitFileType.join('、')
})

const iconClass = computed(() => {
  return props.sourceType === 1 ? 'source-icon' : 'target-icon'
})

const imageLogoCLass = computed(() => {
  return ['pic-image', props.sourceType === 2 ? 'target-image' : '', getFileSuffixByFileName(originData.value.fileName)]
})
const tips = computed(() => {
  return curFile.value ? originData.value.fileName : $t('点击或拖拽文件到此处上传')
})
const isShowReupload = computed(() => {
  return !!originData.value.fileName
})

function sourceChangeFile(file: UploadFile) {
  const fileInput = { target: { files: [file.raw] } }
  uploadFileChange(fileInput)
  refUpload.value?.clearFiles()
}
async function uploadFileChange(fileInput: { target: { files: any[] } }) {
  const fileName = fileInput.target.files[0].name
  const size = fileInput.target.files[0].size
  // 判断文件大小
  if (size > props.maxFile * 1024 * 1024) {
    return ElMessage.error(`文件大小不能超过${props.maxFile}M`)
  }
  const extension = fileName.substr(fileName.lastIndexOf('.') + 1).toLowerCase()
  if (!props.limitFileType.includes(extension)) {
    return ElMessage.error(`文件类型必需为 ${messageError.value}`)
  }
  curFile.value = fileInput.target.files[0]
  emits('afterUpload', curFile.value)
  originData.value.fileName = fileName
  originData.value.loading = false
}
</script>

<template>
  <div class="item">
    <div class="title"><SvgIcon :name="iconClass" class="source-icon" />{{ $t('上传') }}{{ title }}</div>
    <el-upload
      ref="refUpload"
      class="upload-demo"
      action="#"
      drag
      :multiple="false"
      :accept="acceptList"
      :limit="1"
      :auto-upload="false"
      :on-change="sourceChangeFile"
      :file-list="fileList"
    >
      <div class="el-upload__text">
        <div :class="imageLogoCLass"></div>
        <div class="tip-1">
          <div class="filename" :title="tips">{{ tips }}</div>
          <div v-if="isShowReupload" class="reupload-box">{{ $t('重新上传') }}</div>
        </div>
        <div class="tip-2">
          {{ $t('支持') }}{{ messageError }}{{ $t('格式，文件≤') }}{{ maxFile
          }}{{ $t('M 注意：不能直接修改后缀名 .doc为 .docx,否则会出错') }}
        </div>
      </div>
    </el-upload>
    <!-- <input
      ref="uploadFile"
      name="file"
      type="file"
      :accept="acceptList"
      multiple
      style="display: none"
      @change="uploadFileChange($event)"
    /> -->
  </div>
</template>

<style lang="scss" scoped>
.item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: calc(50% - 50px);
  .title {
    display: flex;
    align-items: center;
    width: 90%;
    min-width: 546px;
    height: 22px;
    margin: 40px 0 12px;
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
    color: #262626;
  }
  .up-btn {
    color: #276ef9;
    background: var(--bg-color);
    border-color: #276ef9;
  }
  .pic-image {
    z-index: 1;
    align-self: center;
    width: 90px;
    height: 90px;
    margin: 65px auto 17px;
    text-align: center;
    background-image: url('@/assets/images/textComparison/origin.svg');
    background-repeat: no-repeat;
    background-position-x: center;
    background-position-y: center;
    background-size: contain;
  }
  .target-image {
    background-image: url('@/assets/images/textComparison/target.svg');
  }
  .upload-demo {
    width: 90%;
    min-width: 546px;
    height: 300px;
    :deep(.el-upload) {
      width: 100%;
    }
    :deep(.el-upload-dragger) {
      width: 100%;
      min-width: 546px;
      height: 300px;
    }
  }
  .tip-1 {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 380px;
    height: 22px;
    margin: 24px auto 4px;
    overflow: hidden;
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
    color: #262626;
  }
  .filename {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
    white-space: nowrap;
  }
  .tip-2 {
    display: flex;
    width: 380px;
    height: 18px;
    margin: 0 auto 16px;
    font-size: 12px;
    font-weight: 400;
    line-height: 18px;
    color: #929292;
  }
  .tip-3 {
    display: inline-block;
    max-width: 450px;
    height: 20px;
    margin-left: 6px;
    overflow: hidden;
    text-overflow: ellipsis; /* for Opera */
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    vertical-align: middle;
    color: #262626;
    white-space: nowrap;
  }
  .reupload {
    display: inline-block;
    width: 18px;
    height: 18px;
    margin-left: 16px;
    vertical-align: middle;
  }
  .pdf {
    background-image: url('@/assets/images/textComparison/pdf.svg');
  }
  .doc {
    background-image: url('@/assets/images/textComparison/doc.svg');
  }
  .docx {
    background-image: url('@/assets/images/textComparison/docx.svg');
  }
  .wps {
    background-image: url('@/assets/images/textComparison/wps.svg');
  }
  .png {
    background-image: url('@/assets/images/textComparison/png.svg');
  }
  .tiff,
  .tif {
    background-image: url('@/assets/images/textComparison/tiff.svg');
  }
  .jpg {
    background-image: url('@/assets/images/textComparison/jpg.svg');
  }
  .jpeg {
    background-image: url('@/assets/images/textComparison/jpeg.svg');
  }
}
.source-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 8px;
}
.reupload-box {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: var(--main-bg);
}
</style>
