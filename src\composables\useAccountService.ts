import { LOGIN_SUCCESS, RESPONSE_CODE_SUCCESS } from '@/constants'
import UserA<PERSON> from '@/services/user'
import { computed, ref } from 'vue'
import { useUserStore, useChatStore } from '@/stores'
import '@/utils/geetestSdk.js'
import emitter from '@/utils/eventsBus'
import { $t } from '@/utils/i18n'
export function useAccountService() {
  const chatStore = useChatStore()
  const userStore = useUserStore()
  const userName = ref('')
  const codeBtnVal = ref($t('发送验证码'))
  const isAgreed = ref(false)
  const password = ref('')
  const account = ref('')
  const verificationCode = ref('')
  const identityAuthenticationVisible = ref(false)
  enum SCENE_CODE_TYPE {
    LOGIN = '0',
    RESETPWD = '1',
  }
  /**
   * 极验验证码初始化
   * @param param0
   * @param product
   * @returns
   */
  const initGeetest = ({ gt, challenge, new_captcha, success }: any, product = 'bind') => {
    return new Promise((resolve) => {
      const { initGeetest } = window as any

      if (initGeetest) {
        initGeetest(
          {
            gt,
            challenge,
            offline: !success,
            new_captcha,
            success,
            width: '100%',
            height: '40px',
            product,
            bg_color: 'gray',
          },
          resolve,
        )
      }
    })
  }
  /**
   * 极验验证码验证
   * @param captchaObj
   * @returns
   */
  async function verifyGeetest(captchaObj: any) {
    return new Promise((resolve, reject) => {
      captchaObj.appendTo('#loginGeetest')
      captchaObj
        .onReady(() => {
          captchaObj.verify()
        })
        .onSuccess(() => {
          const data = captchaObj.getValidate()
          resolve(data)
        })
        .onError((e: any) => {
          reject(e)
        })
    })
  }

  const getGeetest = async () => {
    const res = await UserApi.geetestCall()
    if (res.code === RESPONSE_CODE_SUCCESS) {
      const { data } = res
      const captchaObj: any = await initGeetest(data, 'bind')
      captchaObj.onClose(() => {
        setTimeout(() => {
          captchaObj.destroy()
        }, 0)
        throw new Error($t('极验验证关闭'))
      })

      const captchaData = await verifyGeetest(captchaObj)
      return captchaData
    }
    ElMessage.error($t('极验验证码初始化失败'))
    return null
  }
  // const getMemberGeetest = async () => {
  //   const res = await UserApi.memberGeetestCall()
  //   if (res.code === RESPONSE_CODE_SUCCESS) {
  //     const { data } = res
  //     const captchaObj: any = await initGeetest(data, 'bind')
  //     captchaObj.onClose(() => {
  //       setTimeout(() => {
  //         captchaObj.destroy()
  //       }, 0)
  //       throw new Error('极验验证关闭')
  //     })

  //     const captchaData = await verifyGeetest(captchaObj)
  //     return captchaData
  //   }
  //   Msg.error('极验验证码初始化失败')
  //   return null
  // }
  //userName.value = '18600000000'
  const checkUserName = () => {
    if (!userName.value) {
      ElMessage.warning($t('请输入手机号'))
      return false
    }
    if (userName.value.length !== 11 || /^1[3-9]\d{9}$/.test(userName.value) === false) {
      ElMessage.warning($t('手机号格式不正确'))
      return false
    }
    return true
  }
  const checkCode = () => {
    if (!verificationCode.value) {
      ElMessage.warning($t('请输入验证码'))
      return false
    }
    if (verificationCode.value.length !== 6) {
      ElMessage.warning($t('验证码长度不正确'))
      return false
    }
    if (/^\d{6}$/.test(verificationCode.value) === false) {
      ElMessage.warning($t('验证码格式不正确'))
      return false
    }
    return true
  }

  const validateAccount = () => {
    // 至少8位，包含大小写字母、数字和特殊字符
    // const regExp = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[$@$!%*?&])[A-Za-z\d$@$!%*?&]{8,}$/
    if (!account.value) {
      ElMessage.warning($t('请输入账号'))
      return false
    }
    // if (!regExp.test(account.value)) {
    //   ElMessage.warning('账号格式不正确')
    //   return false
    // }
    return true
  }
  const validatePassword = () => {
    // 至少8位，包含大小写字母、数字和特殊字符
    // const regExp = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[$@$!%*?&])[A-Za-z\d$@$!%*?&]{8,}$/
    if (!password.value) {
      ElMessage.warning($t('请输入密码'))
      return false
    }
    // if (!regExp.test(password.value)) {
    //   ElMessage.warning('密码格式不正确')
    // }
    return true
  }
  /**
   * 下发验证码
   * @returns
   */
  const countdown = ref<number>(60)
  const timer = ref<any>(null)
  const startCountdown = () => {
    countdown.value = 60
    timer.value = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer.value)
        timer.value = null
        codeBtnVal.value = $t('重新发送')
        countdown.value = 60
      }
    }, 1000)
  }
  const stopCountdown = () => {
    if (timer.value) {
      clearInterval(timer.value)
      countdown.value = 60
      timer.value = null
    }
  }

  const getLoginCode = async () => {
    if (!checkUserName()) {
      return
    }
    if (countdown.value < 60) {
      return
    }
    // 发送验证码前，先获取极验验证码
    const captchaData = await getGeetest()
    if (!captchaData) {
      return
    }

    const { geetest_challenge, geetest_validate, geetest_seccode } = captchaData as any
    const { code: resCode, message } = await UserApi.sendLoginCode({
      userName: userName.value,
      scene: SCENE_CODE_TYPE.LOGIN,
      challenge: geetest_challenge,
      validate: geetest_validate,
      seccode: geetest_seccode,
    })
    if (resCode === RESPONSE_CODE_SUCCESS) {
      ElMessage.success(message)
      startCountdown()
    } else {
      ElMessage.error(message)
      codeBtnVal.value = $t('重新发送')
      stopCountdown()
    }
  }

  const getChangeManagerCode = async () => {
    if (countdown.value < 60) {
      return
    }
    // 发送验证码前，先获取极验验证码
    const captchaData = await getGeetest()
    if (!captchaData) {
      return
    }
    const { geetest_challenge, geetest_validate, geetest_seccode } = captchaData as any
    const { code: resCode, message } = await UserApi.sendChangeManagerCode({
      scene: SCENE_CODE_TYPE.LOGIN,
      challenge: geetest_challenge,
      validate: geetest_validate,
      seccode: geetest_seccode,
    })
    if (resCode === RESPONSE_CODE_SUCCESS) {
      ElMessage.success(message)
      startCountdown()
    } else {
      ElMessage.error(message)
      codeBtnVal.value = $t('重新发送')
      stopCountdown()
    }
  }

  const codeBtnName = computed(() => {
    if (countdown.value !== 60) {
      return `${countdown.value}s后重发`
    } else {
      return codeBtnVal.value
    }
  })

  const showAgreement = ref(false)

  const doLoginByCode = async () => {
    if (!checkUserName()) {
      return
    }
    if (!checkCode()) {
      return
    }
    if (isAgreed.value === false) {
      showAgreement.value = true
      return
    }
    const {
      code: resCode,
      message,
      data,
    } = await UserApi.doLoginByCode<string>(userName.value, verificationCode.value, SCENE_CODE_TYPE.LOGIN)
    if (resCode === RESPONSE_CODE_SUCCESS) {
      userStore.setToken(data)
      userStore.setIsLogin(true)
      userStore.setShowLoginBox(false)
      await userStore.loadUserInfo()
      userName.value = ''
      verificationCode.value = ''
      ElMessage.success($t('登录成功'))
      emitter.emit(LOGIN_SUCCESS)
      emitter.emit('refresh-contract-list')
      stopCountdown()
      return true
    } else {
      ElMessage.error(message)
      return false
    }
  }

  const doLoginByPwd = async () => {
    if (!validateAccount()) {
      return
    }
    if (!validatePassword()) {
      return
    }
    if (isAgreed.value === false) {
      showAgreement.value = true
      return
    }
    const { code: resCode, message, data } = await UserApi.doLoginByPwd<string>(account.value, password.value)
    if (resCode === RESPONSE_CODE_SUCCESS) {
      userStore.setToken(data)
      userStore.setIsLogin(true)
      userStore.setShowLoginBox(false)
      await userStore.loadUserInfo()
      account.value = ''
      password.value = ''
      ElMessage.success($t('登录成功'))
      emitter.emit(LOGIN_SUCCESS)
      emitter.emit('refresh-contract-list')
      stopCountdown()
      return true
    } else {
      ElMessage.error(message)
      return false
    }
  }

  const deregisterAccount = async () => {
    if (!checkCode()) {
      return
    }
    const { code: loginResCode, message } = await UserApi.checkChangeManagerCode(verificationCode.value)
    if (loginResCode === RESPONSE_CODE_SUCCESS) {
      const { code: deleteUserCode, message } = await UserApi.deleteUser()
      if (deleteUserCode === RESPONSE_CODE_SUCCESS) {
        identityAuthenticationVisible.value = false
        userStore.setIsLogin(false)
        userStore.initUserInfo()
        ElMessage.success(message)
        userName.value = ''
        verificationCode.value = ''
        chatStore.historyChatList = []
      } else {
        ElMessage.error(message)
        return false
      }
    } else {
      ElMessage.error(message)
      return false
    }
  }
  const clearLoginForm = () => {
    userName.value = ''
    verificationCode.value = ''
    isAgreed.value = false
  }

  return {
    codeBtnName,
    showAgreement,
    doLoginByCode,
    doLoginByPwd,
    deregisterAccount,
    getLoginCode,
    getChangeManagerCode,
    isAgreed,
    checkUserName,
    checkCode,
    userName,
    verificationCode,
    identityAuthenticationVisible,
    clearLoginForm,
    account,
    password,
  }
}
