<script lang="ts" setup>
import { useAppStore } from '@/stores/app'
import { getRecentCompareRecordList, deleteCompareRecord } from '@/services/textComparison'
import { useRouter } from 'vue-router'
import type { ICompareResponse } from '@/services/textComparison'
import { statusEnum } from '../../list/types/enums'
import type { IStatusOption } from '../../list/types'
import { $t } from '@/utils/i18n'

const tableData = ref<ICompareResponse[]>([])
const loading = ref(false)
const pollInterval = 30000 // 30s 轮询一次

const comparisonTipsVisible = ref(true)

const store = useAppStore()
const userCode = store.userInfo?.id

const router = useRouter()

const hasComparisonTips = localStorage.getItem(`comparisonTips_${userCode}`)
if (hasComparisonTips == 'false' || hasComparisonTips == 'true') {
  // tips 状态值存在（可能是 true 或 false）
  comparisonTipsVisible.value = hasComparisonTips == 'true' ? true : false
} else {
  // tips 状态值不存在（undefined/null）
  comparisonTipsVisible.value = true // 默认显示提示信息
}

// 使用计算属性确保状态选项在语言切换时能够响应式更新
const statusOption = computed<IStatusOption[]>(() => [
  { value: statusEnum.QUEUE, label: $t('排队中'), color: 'var(--main-bg)' },
  { value: statusEnum.IN_PROGRESS, label: $t('比对中'), color: 'var(--main-bg)' },
  { value: statusEnum.SUCCESS, label: $t('比对成功'), color: 'var(--is-color-1b9275)' },
  { value: statusEnum.FAILED, label: $t('比对失败'), color: 'var(--is-color-e6555e)' },
])

const closeTips = () => {
  comparisonTipsVisible.value = false
  localStorage.setItem(`comparisonTips_${userCode}`, comparisonTipsVisible.value + '')
}

const getTableData = async () => {
  loading.value = true

  try {
    const { data } = await getRecentCompareRecordList<ICompareResponse[]>()
    console.log('d', data)
    tableData.value = data || []
  } catch (error) {
    console.error(error)
  } finally {
    loading.value = false
  }
}

const timer = setInterval(() => {
  getTableData()
}, pollInterval)

getTableData()

onUnmounted(() => {
  clearInterval(timer)
  // 近期列表数据为空时，不修改 tips 状态
  if (tableData.value.length > 0) {
    comparisonTipsVisible.value = false
    localStorage.setItem(`comparisonTips_${userCode}`, comparisonTipsVisible.value + '')
  }
})

const getProgressPercentage = (progressNum: number) => {
  // 比对失败 / 未开始 时显示 0%
  if (progressNum === statusEnum.FAILED || progressNum === statusEnum.QUEUE) return statusEnum.QUEUE
  return progressNum
}

const getProgressColor = (progressNum: number) => {
  if (progressNum === statusEnum.FAILED) return '#E6555E' // 比对失败
  if (progressNum === statusEnum.SUCCESS) return '#1B9275' // 比对成功
  return undefined
}

// 比对状态 -  比对中：1-99；比对成功：100；比对失败：-1；排队中：0；
const setTagStatus = (val: number, type: 'color' | 'label') => {
  const tag = statusOption.value.find((item) => {
    if (val > statusEnum.QUEUE && val < statusEnum.SUCCESS) return item.value == 1
    return item.value == val
  })
  return tag ? tag[type] : ''
}

const gotoComparisonList = () => {
  router.push('/comparisonList')
}

const getComparisonInfo = (id: string) => {
  window.open(`/comparison?compareId=${id}`)
}

const delComparisonInfo = (id: string) => {
  ElMessageBox.confirm($t('确认删除比对记录吗?'), $t('提示'), {
    confirmButtonText: $t('确认'),
    cancelButtonText: $t('取消'),
    confirmButtonClass: 'el-button--danger',
    customClass: 'cus-message-box warn',
    showClose: false,
  })
    .then(async () => {
      await deleteCompareRecord(id)
      getTableData()
      ElMessage({
        type: 'success',
        message: $t('删除成功!'),
      })
    })
    .catch(() => {
      console.log('取消删除比对记录')
    })
}
</script>

<template>
  <div v-if="tableData.length" v-loading="loading" class="recent-wrap">
    <div class="recent-title">
      <span>{{ $t('近期比对') }}</span>
      <el-button type="primary" link @click="gotoComparisonList">{{ $t('查看更多') }}</el-button>
    </div>
    <div class="recent-list">
      <el-table
        ref="table"
        height="300"
        :header-cell-style="{
          'background-color': '#fff',
          color: '#7D7B89',
          'font-size': '14px',
          'line-height': '22px',
        }"
        :row-style="{ height: '46px' }"
        :cell-style="{ padding: '4px 0 0 0', 'font-size': '14px', color: '#221D39' }"
        :data="tableData"
      >
        <el-table-column :label="$t('原稿文档')" :show-overflow-tooltip="true" min-width="200">
          <template v-slot="scope">
            <span>{{ scope.row.leftFilename || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('比对文档')" :show-overflow-tooltip="true" min-width="200">
          <template v-slot="scope">
            <span>{{ scope.row.rightFilename || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('比对状态')" width="200">
          <template v-slot="scope">
            <span
              class="status-dot"
              :style="{ 'background-color': setTagStatus(scope.row.progressNum, 'color') }"
            ></span>
            <span>{{ setTagStatus(scope.row.progressNum, 'label') }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('比对进度')" min-width="300">
          <template v-slot="scope">
            <div class="progress-wrap" :class="{ 'failed-progress': scope.row.progressNum == -1 }">
              <el-progress
                :percentage="getProgressPercentage(scope.row.progressNum)"
                :stroke-width="6"
                :color="getProgressColor(scope.row.progressNum)"
              ></el-progress>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="$t('完成时间')" width="200">
          <template v-slot="scope">
            <span>{{ scope.row.updateTime ? scope.row.updateTime : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('操作')" width="200" fixed="right">
          <template v-slot="scope">
            <el-button
              v-if="scope.row.progressNum !== 0"
              type="primary"
              link
              @click="getComparisonInfo(scope.row.id)"
              >{{ $t('查看') }}</el-button
            >
            <el-divider direction="vertical" />
            <el-button type="primary" link @click="delComparisonInfo(scope.row.id)">{{ $t('删除') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div v-if="comparisonTipsVisible" class="tips-txt">
        <el-card>
          <p>{{ $t('比对结束后可在此查看比对结果') }}</p>
          <span @click="closeTips">{{ $t('我知道了') }}</span>
        </el-card>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.recent-wrap {
  width: 100%;
  margin-top: 48px;
  .recent-title {
    position: relative;
    display: flex;
    align-items: center;
    height: 22px;
    &::before {
      position: absolute;
      top: 3px;
      left: 0;
      width: 2px;
      height: 16px;
      content: '';
      background: linear-gradient(356.12deg, var(--main-font) 0.32%, var(--main-bg) 97.58%);
      border-radius: 2px;
    }
    span {
      padding: 0 12px 0 8px;
      font-size: 16px;
      font-weight: 500;
      line-height: 100%;
      color: #262626;
      text-align: center;
      letter-spacing: 0;
    }
  }
  .recent-list {
    position: relative;
    .progress-wrap {
      width: 90%;
      :deep(.el-progress) {
        margin-left: 6px;
      }
    }

    /* 检查失败的进度条特殊样式 */
    .failed-progress {
      :deep(.el-progress-bar) {
        :deep(.el-progress-bar__outer) {
          background-color: var(--is-color-e6555e);
        }
        .el-progress-bar__inner {
          width: 100% !important;
          background-color: var(--is-color-e6555e);
        }
      }
    }

    /* 比对状态样式 */
    .status-dot {
      display: inline-block;
      width: 5px;
      height: 5px;
      margin-right: 5px;
      margin-bottom: 2px;
      border-radius: 50%;
    }
    .tips {
      margin-right: 10px;
    }
    .tips-txt {
      position: absolute;
      top: -55px;
      right: 150px;
      z-index: 9;
      line-height: 22px;
      :deep(.el-card__body) {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        justify-content: space-between;
        width: 286px;
        height: 102px;
        padding: 16px;
        color: var(--bg-color);
        background: linear-gradient(291.96deg, var(--main-font) 18.03%, var(--main-font) 88.73%);
      }
      p {
        width: 100%;
        text-align: left;
      }
      span {
        display: inline-block;
        width: 88px;
        height: 32px;
        line-height: 32px;
        color: var(--main-font);
        text-align: center;
        cursor: pointer;
        background-color: var(--bg-color);
        border-radius: 4px;
      }
      &::after {
        position: absolute;
        right: 15px;
        bottom: -8px;
        z-index: 9;
        width: 0;
        height: 0;
        margin-left: -10px; /* 半宽度偏移，确保三角形居中 */
        content: '';
        border-top: 10px solid var(--main-font); /* 颜色可以自定义  */
        border-right: 10px solid transparent;
        border-left: 10px solid transparent;
      }
    }
  }
}
</style>
