module.exports = {
  extends: [
    'stylelint-config-standard-scss', // configure for SCSS
    'stylelint-config-recommended-vue', // add overrides for .Vue files
    'stylelint-prettier/recommended',
    'stylelint-config-recess-order', // use the recess order for properties
  ],
  plugins: ['stylelint-order'],
  rules: {
    'selector-pseudo-element-no-unknown': [
      true,
      {
        ignorePseudoElements: ['deep'],
      },
    ],
    'font-family-no-missing-generic-family-keyword': [
      true,
      {
        ignoreFontFamilies: ['iconfont', 'element-icons'],
      },
    ],
    // 'block-no-empty': true,
    // 禁止低优先级的选择器出现在高优先级的选择器之后。
    'no-descending-specificity': null,
    // 不验证@未知的名字，为了兼容scss的函数
    'at-rule-no-unknown': null,
    // 禁止空注释
    'comment-no-empty': true,
    'import-notation': null,
    'selector-class-pattern': null, // 取消类名的命名规范
    'rule-empty-line-before': 'never', // 禁止规则前有空行。
    'scss/load-partial-extension': null,
    'font-family-no-duplicate-names': [true, { ignoreFontFamilyNames: ['/^My Font /', 'monospace'] }],
  },
}
