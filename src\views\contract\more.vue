<script lang="ts" setup>
import { type IContractSearchParams } from './com/Model.ts'
import type { FormInstance } from 'element-plus'
import router from '@/router'
import Header from '@/layout/components/Header.vue'
import DataTable from './com/DataTable.vue'
import { useContractListService } from './com/useContractListService.ts'
const emit = defineEmits(['search'])
const formRef = ref<FormInstance>()
const {
  userId,
  recentDatas,
  loading,
  total,
  pageSize,
  page,
  contractDatas,
  loadContracts,
  delReviewInfo,
  getReviewInfo,

  handleSizeChange,
  handleCurrentChange,
} = useContractListService()
const searchParams = ref<IContractSearchParams>({ contractName: '', dateRange: [], status: '' })

function handleReset() {
  formRef.value?.resetFields()
  emit('search')
}
function handleSearch() {
  emit('search')
}

const goto = () => {
  router.push('/contract')
}
let timer: any
onMounted(() => {
  if (userId.value) {
    loadContracts()
    timer = setInterval(() => {
      loadContracts()
    }, 30000)
  }
})
onUnmounted(() => {
  clearInterval(timer)
})
</script>
<template>
  <div class="contract-more">
    <Header :title="$t('合同文档')" :show-back="true" @goto="goto"></Header>
    <!-- <div class="search-wrap">
      <el-form ref="formRef" :model="searchParams" label-width="80px" :inline="true" style="width: 100%">
        <el-form-item :label="$t('合同名称')">
          <el-input
            v-model="searchParams!.contractName"
            clearable
            :placeholder="$t('请输入')"
            style="width: 12.5rem"
            @clear="handleSearch"
          />
        </el-form-item>
        <el-form-item :label="$t('创建时间')">
          <el-date-picker
            v-model="searchParams!.dateRange"
            type="daterange"
            range-:separator="$t('至')"
            start-:placeholder="$t('开始日期')"
            end-:placeholder="$t('结束日期')"
            value-format="YYYY-MM-DD"
            clearable
            @change="handleSearch"
            @clear="handleSearch"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item :label="$t('审查状态')">
          <el-select
            v-model="searchParams!.status"
            :placeholder="$t('请选择')"
            style="width: 12.5rem"
            clearable
            @change="handleSearch"
          >
            <el-option
              v-for="item in statusOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <div class="btn-wrap">
          <el-button type="primary" @click.stop="handleSearch">{{ $t('查询') }}</el-button>
          <el-button class="secondary" @click="handleReset">{{ $t('重置') }}</el-button>
        </div>
      </el-form>
    </div> -->
    <DataTable
      :data="contractDatas"
      :loading="loading"
      @view="getReviewInfo"
      @del="
        (id) => {
          delReviewInfo(id, 'list')
        }
      "
    />
    <div style="display: flex; justify-content: flex-end; margin-top: 1.25rem">
      <el-pagination
        v-show="total > 0"
        :current-page="page"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="pageSize"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.contract-more {
  display: flex;
  flex-direction: column;
  padding: 0.625rem 1.5rem;
}
.search-wrap {
  display: flex;
  flex-wrap: wrap;
  row-gap: 16px;
  padding: 16px 0;
  background-color: var(--bg-color);
  .condition {
    display: flex;
    align-items: center;
    width: 33.3%;
    &-label {
      width: 90px;
      padding-left: 12px;
      color: #606266;
    }
    .el-input {
      width: 100%;
    }
    .el-date-editor {
      width: 100% !important;
    }
    .el-select {
      width: 100%;
    }
    .el-cascader {
      width: 100%;
    }
  }
  .btn-wrap {
    display: flex;
    flex: 1;
    justify-content: flex-start;
    padding-left: 12px;
  }
}
</style>
