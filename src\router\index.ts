import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { BASE_URL } from '@/utils/config'

const modules: Record<string, RouteRecordRaw[]> = import.meta.glob('./modules/*.ts', {
  import: 'default',
  eager: true,
})

const routes: RouteRecordRaw[] = []
for (const path in modules) {
  routes.push(...modules[path])
}

const router = createRouter({
  history: createWebHistory(BASE_URL),
  routes,
  scrollBehavior: () => ({ top: 0 }),
})

export default router
