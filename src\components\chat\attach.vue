<template>
  <div class="upload-com">
    <div v-if="getFiles.length" class="carousel">
      <ul ref="attachScrollerRef" class="attachs" style="padding-top: 0.75rem">
        <li v-for="(item, i) in getFiles" :key="i">
          <div class="item-close" @click.stop="handlerRemove(item)">
            <i class="iconfont" style="font-size: 0.75rem; color: #000">&#xe643;</i>
          </div>
          <div v-if="!item.isImg()" :class="{ 'attach-item': true, 'attach-item-fail': item.status == 'fail' }">
            <div>
              <Icons :name="item.fileSuffix" width="2rem" height="2rem" />
            </div>
            <div class="attach-item-right">
              <div class="inner-title">{{ item.fileName }}</div>
              <div v-if="item.status == 'success'" class="inner-desc">
                {{ item.fileSuffix == 'web' ? item.suffixName : item.getFileSize() }}
              </div>
              <div v-if="item.status == 'uploading' || item.status == 'ready'" class="inner-desc">
                {{ $t('上传中...') }}
              </div>
              <div v-if="item.status == 'fail'" class="inner-desc">{{ $t('上传失败...') }}</div>
            </div>

            <div v-if="item.status != 'success' && item.status != 'fail'" class="progress-container">
              <div class="progress-bar" :style="{ width: `${item.percentage}%` }"></div>
            </div>
          </div>
          <div v-else class="attach-item-img">
            <div
              v-if="item.status != 'success'"
              v-loading="
                () => {
                  return item.status != 'success'
                }
              "
              class="progress-circle"
            ></div>
            <!-- <div v-if="item.status != 'success'" class="progress-text">{{ item.percentage }}%</div> -->
            <div v-if="item.status == 'fail'" class="progress-text" style="color: rgb(243 32 81 / 100%)">
              {{ $t('上传失败...') }}
            </div>
            <img :src="item.url" />
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, type PropType, computed } from 'vue'
import Icons from '@/components/Icons.vue'
import { IUploadFile } from '@/components/chat/helper'
const emits = defineEmits(['remove'])

const props = defineProps({
  files: {
    type: Array as PropType<IUploadFile[]>,
    default() {
      return []
    },
  },
  width: {
    type: String,
    default() {
      return '43.75rem'
    },
  },
})
const attachScrollerRef = ref()

const getFiles = computed(() => {
  return props.files
})

const handlerRemove = (file: IUploadFile) => {
  emits('remove', file)
}
</script>
<style lang="scss" scoped>
:deep(.el-loading-spinner) {
  --el-loading-spinner-size: 1.5rem;
}
.upload-com {
  display: flex;
  align-items: center;
  width: calc(100% - 32px);
  padding: 0 0.75rem;
  .attach-img-plus {
    display: flex !important;
  }
}
.carousel {
  position: relative;
  z-index: 1;
  width: calc(100% - 0px);
  text-align: center;
  background-color: var(--bg-color);
}
.carousel ul {
  z-index: 2;
  display: flex;
  height: fit-content;
  padding: 0;
  margin: 0 auto;
  list-style: none;
  transition: all 0.5s;
  animation: all 5s linear;
}
.carousel ul:hover {
  animation-play-state: paused;
}
.carousel ul li {
  position: relative;
  z-index: 3;
  display: flex;
  flex-shrink: 0;
  height: fit-content;
  margin-left: 0.5rem;
  .item-close {
    position: absolute;
    top: -0.5rem;
    right: -0.375rem;
    z-index: 999;
    display: none;
    width: 0.875rem;
    height: 0.875rem;
    cursor: pointer;
  }
  .error {
    border: 1px solid rgb(243 32 81 / 100%);
    border-radius: var(--kaka-border-radius-4);
  }
}
.carousel ul li:hover {
  .item-close {
    display: block;
  }
}
.carousel ul li:first-child {
  margin-left: 0;
  border: red;
}
.carousel ul:last-child {
  margin-left: 0;
}
.attach-item-img {
  position: relative;
  z-index: 99;
  display: flex;
  align-items: center;
  width: fit-content;
  border: 1px solid var(--page-header-line);
  border-radius: 0.5rem;
  > img {
    width: 3.25rem;
    height: 3.25rem;
    border-radius: 0.5rem;
  }
}
.attach-item {
  display: flex;
  align-items: center;
  width: fit-content;
  padding: 0.5rem;
  background-color: #f2f3f666;
  border-radius: 0.5rem;
  .attach-item-right {
    width: fit-content;
    margin-left: 0.5rem;
    .inner-title {
      width: 9rem;
      height: 1.25rem;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 0.875rem;
      font-weight: 400;
      line-height: 1.25rem;
      color: rgb(0 0 0 / 88%);
      text-align: left;
      white-space: nowrap;
    }
    .inner-desc {
      display: flex;
      align-items: center;
      height: 1rem;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 0.75rem;
      font-weight: 300;
      line-height: 1rem;
      color: rgb(0 0 0 / 45%);
      white-space: nowrap;
    }
  }
}
.attach-item:first-of-type {
  margin-left: 0;
}
.attach-item-fail {
  border: 1px solid var(--is-color-e6555e);
  .attach-item-right {
    .inner-desc {
      color: var(--is-color-e6555e);
    }
  }
}
.carousel-arrow {
  position: absolute;
  top: 60%;
  z-index: 10;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1.75rem;
  height: 1.75rem;
  padding: 0;
  margin: 0;
  font-size: 1rem;
  text-align: center;
  cursor: pointer;
  outline: none;
  background: rgb(255 255 255 / 100%);
  background-color: var(--bg-color);
  border: none;
  border-radius: 50%;
  box-shadow: 0 1px 4px rgb(0 0 0 / 50%);
  transform: translateY(-50%);
  transition: 0.3s;
}
.arrow-left {
  left: 0.0625rem;
}
.arrow-right {
  right: 0.0625rem;
}
.progress-container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: calc(100% - 8px);
  margin: 0 4px;
  overflow: hidden;
  border-radius: 5px;
}

/* 进度条样式 */
.progress-bar {
  width: 0%;
  height: 2px;
  line-height: 2px;
  color: white;
  text-align: center;
  background-color: #2196f3;
  border-radius: var(--kaka-border-radius-2);
  transition: width 0.5s ease; /* 添加过渡效果 */
}
.progress-circle {
  position: absolute;
  z-index: 999;
  width: 3.25rem;
  height: 3.25rem;
  background: gray;
  border-radius: 0.5rem;
  opacity: 0.9;
  :deep(.el-loading-mask .el-loading-spinner) {
    margin-top: 0;
    transform: translateY(calc(-50% + 0.125rem));
  }
}

/* 定义百分比文本的样式 */
.progress-text {
  position: absolute;
  z-index: 999;
  width: 68px;
  height: 68px;
  font-size: 14px;
  font-weight: 500;
  line-height: 68px;
  vertical-align: middle;
  color: var(--bg-color);
}
</style>
