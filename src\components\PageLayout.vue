<script setup lang="ts">
const slots = useSlots()

const hasSidebar = ref(false)

onMounted(() => {
  // 检测 sidebar 插槽是否有内容
  const sidebarSlot = slots.sidebar?.()
  hasSidebar.value = !!(sidebarSlot && sidebarSlot.length > 0)
})
</script>

<template>
  <div class="page-wrap">
    <!-- 上方标题栏 -->
    <div class="header" v-if="$slots.headerName || $slots.headerBtn">
      <slot name="headerName"></slot>
      <!-- 插槽示例代码（保持类名一致）
       <template #headerName>
        <span class="header-name">{{ $t('自定义标题栏') }}</span>
       </template> 
       -->
      <slot name="headerBtn"></slot>
      <!-- 插槽示例代码（多个按钮需要包一下）
       <template #headerBtn>
          <span>
            <el-button type="primary">{{ $t('操作按钮') }}</el-button>
            <el-button class="secondary">{{ $t('操作按钮') }}</el-button>
          </span> 
      </template> 
      -->
    </div>
    <div class="container">
      <!-- 左侧边栏 -->
      <div v-if="hasSidebar" class="sidebar">
        <slot name="sidebar"></slot>
      </div>
      <!-- 右侧内容 -->
      <div class="content" :class="{ 'full-width': !hasSidebar }">
        <!-- 右侧标题模块 -->
        <div v-if="$slots.title" class="title">
          <slot name="title"></slot>
        </div>
        <!-- 搜索模块 -->
        <div v-if="$slots.search" class="search">
          <slot name="search"></slot>
          <!-- 一个搜索框表单的插槽示例代码
            <template #search>
              <div class="condition">
                <span class="condition-label">{{ $t('搜索框名称') }}</span>
                <el-input clearable :placeholder="$t('请输入搜索内容')" />
              </div>
              ……
              <div class="btn-wrap">
                <el-button class="btn" type="primary">{{ $t('查 询') }}</el-button>
                <el-button class="secondary">{{ $t('重 置') }}</el-button>
              </div>
            </template> 
           -->
        </div>
        <!-- 操作按钮 -->
        <div v-if="$slots.operation" class="page-handle">
          <slot name="operation"></slot>
          <!-- 插槽示例代码
            <template #operation>
              <el-button type="primary" @click="updateTable">{{ $t('刷新') }}</el-button>
            </template> 
          -->
        </div>
        <!-- 表格模块 -->
        <div v-if="$slots.table" class="table">
          <slot name="table"></slot>
          <!-- 插槽示例代码
            <template #table>
              <el-table></el-table>
              <el-pagination></el-pagination>
            </template> 
            -->
        </div>
        <!-- 底部 -->
        <div v-if="$slots.footer" class="footer">
          <slot name="footer"></slot>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.page-wrap {
  display: flex;

  // height: calc(100% - 56px);
  flex: 1;
  flex-direction: column;
  height: 100%;
  padding: 0 24px;
  overflow: hidden;

  /* 上方标题栏 */
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 54px;
    border-bottom: solid 1px var(--page-header-line);
    :deep(.header-name) {
      font-size: 16px;
      font-weight: 600;
      letter-spacing: 1px;
    }
  }
  .container {
    display: flex;
    flex: 1;

    /* 预留左侧侧边栏(宽度可调) */
    .sidebar {
      flex-shrink: 0;

      /* 禁止收缩 */
      width: 240px;

      /* 固定宽度 */
      height: 100%;
      border-right: 1px solid #eee;
    }

    /* 右侧内容 */
    .content {
      display: flex;
      flex: 1;
      flex-direction: column;
      min-width: 0;

      /* 修复内容溢出问题 */

      /* 右侧标题模块 */
      .title {
        padding: 16px 16px 0;
        font-size: 16px;
      }

      /* 搜索模块 */
      .search {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        padding: 16px 0;
        background-color: var(--minor-bg);

        /* 搜索框表单 */
        :deep(.condition) {
          display: flex;
          align-items: center;
          width: 32%;
          min-width: 300px;
          .condition-label {
            width: 100px;
            padding-right: 12px;
            color: var(--popper-font);
            text-align: right;
          }

          // 输入框、下拉选择框、日期范围选择框……（待扩展）
          :deep(.el-input) {
            width: 100%;
          }
          :deep(.el-date-editor) {
            width: 100% !important;
          }
          :deep(.el-select) {
            width: 100%;
          }
          :deep(.el-cascader) {
            width: 100%;
          }
        }
      }

      /* 操作按钮 */
      .page-handle {
        padding: 0 16px 16px;
        text-align: right;
      }

      /* 表格模块 */
      .table {
        display: flex;
        flex: 1;
        flex-direction: column;
        min-height: 0; // ⭐ 关键，防止被撑开
        overflow: hidden; // 防止外溢
      }
    }
  }
}
</style>
