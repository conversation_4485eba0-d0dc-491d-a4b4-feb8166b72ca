<template>
  <div class="box-wrap">
    <span
      v-for="(item, idx) in showList"
      :key="idx"
      class="text-btn"
      :class="{ 'disabled-btn': item.disabled }"
      :style="{ color: item.color || '#773BEF' }"
      @click="item.fun(handleData)"
      >{{ item.name }}</span
    >
    <el-dropdown v-if="moreList.length" placement="bottom">
      <span class="text-btn btn-last">{{ $t('更多') }}</span>
      <template #dropdown>
        <el-dropdown-menu class="more-menu">
          <el-dropdown-item v-for="(item, idx) in moreList" :key="idx" class="more-item">
            <span @click="item.fun(handleData)">{{ item.name }}</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup lang="ts">
import { watchEffect, ref } from 'vue'
import { isOwnAuth } from '@/utils'

interface HandleItem {
  name: string
  fun: (data: any) => void
  disabled?: boolean
  color?: string
  auth?: string
}

interface Props {
  handleList: HandleItem[]
  handleData: any
  ownAuth?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  ownAuth: false,
})

const showList = ref<HandleItem[]>([])
const moreList = ref<HandleItem[]>([])

watchEffect(() => {
  let list = props.handleList || []

  if (!props.ownAuth || !isOwnAuth(props.handleData?.createBy)) {
    list = list.filter((item) => {
      if (!item.auth) return true
      // TODO: 需要根据实际的权限检查方法进行调整
      // 可以通过组合式函数或者其他方式来处理权限检查
      return true // 暂时允许所有权限
    })
  }

  if (list.length > 3) {
    showList.value = list.slice(0, 2)
    moreList.value = list.slice(2)
  } else {
    showList.value = list
    moreList.value = []
  }
})
</script>

<style lang="scss" scoped>
.box-wrap {
  display: flex;
  .text-btn {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: var(--main-bg);
    white-space: nowrap;
    cursor: pointer;
    &:not(:last-child) {
      &::after {
        display: inline-block;
        height: 10px;
        margin: 0 10px;
        content: '';
        border-right: 1px solid #e9e9e9;
      }
    }
  }
  .btn-last {
    &::after {
      border-right: none !important;
    }
  }
  .disabled-btn {
    color: #c0c4cc !important;
    pointer-events: none !important;
  }
}
</style>
