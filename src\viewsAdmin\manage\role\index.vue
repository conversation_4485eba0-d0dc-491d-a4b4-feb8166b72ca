<script setup lang="ts">
import PageLayout from '@/components/PageLayout.vue'
import SearchBox from './components/SearchBox.vue'
import TableBox from './components/TableBox.vue'
import SaveRoleDialog from './components/SaveRoleDialog.vue'
import type { IRoleQueryData } from '@/services/manage/index'

import { $t } from '@/utils/i18n'
const saveRoleDialogRef = ref<InstanceType<typeof SaveRoleDialog>>()
const TableBoxRef = ref()
const updateTable = (searchParams?: IRoleQueryData) => {
  TableBoxRef.value?.getPageRoleList(searchParams)
}

const add = () => {
  saveRoleDialogRef.value?.openDialog()
}
</script>

<template>
  <div class="container">
    <PageLayout>
      <template #headerName>
        <span class="header-name">{{ $t('角色管理') }}</span>
      </template>
      <template #search>
        <SearchBox @search="updateTable"></SearchBox>
      </template>
      <template #operation>
        <el-button type="primary" @click="add">{{ $t('新增角色') }}</el-button>
      </template>
      <template #table>
        <TableBox ref="TableBoxRef" />
      </template>
    </PageLayout>
    <SaveRoleDialog ref="saveRoleDialogRef" @updateList="updateTable"></SaveRoleDialog>
  </div>
</template>

<style lang="scss" scoped>
.container {
  position: relative;
  height: 100vh;
}
</style>
