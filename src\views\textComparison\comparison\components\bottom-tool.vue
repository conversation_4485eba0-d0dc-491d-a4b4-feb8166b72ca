<script setup>
// import { generalDataKey } from '../types/index'
const generalData = inject('generalData')
const emits = defineEmits(['handleScale'])

const zoomNum = ref(100)
const similarityColor = computed(() => {
  if (generalData.value.similarity >= 100) {
    return 'color-insert'
  } else if (generalData.value.similarity >= 70 && generalData.value.similarity <= 99) {
    return 'color-delete'
  } else {
    return 'color-change'
  }
})

function handleAlign(val) {
  generalData.value.isAlign = val
}

function handleZoom(cur) {
  zoomNum.value = cur
  emits('handleScale', cur)
}

function changeFullScreen() {
  if (!generalData.value.isFullScreen) {
    const el = document.documentElement
    // 不同浏览器兼容方法调用
    if (el.requestFullScreen) {
      el.requestFullScreen()
    } else if (el.webkitRequestFullScreen) {
      el.webkitRequestFullScreen()
    } else if (el.mozRequestFullScreen) {
      el.mozRequestFullScreen()
    } else if (el.msRequestFullScreen) {
      el.msRequestFullScreen()
    }
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen()
    } else if (document.mozCancelFullScreen) {
      document.mozCancelFullScreen()
    } else if (document.webkitCancelFullScreen) {
      document.webkitCancelFullScreen()
    } else if (document.msCancelFullScreen) {
      document.msCancelFullScreen()
    }
  }
  // generalData.value.isFullScreen = !generalData.value.isFullScreen;
  // generalData.value.isShowTop = false;
}
onMounted(() => {
  document.addEventListener('webkitfullscreenchange', changeScreen)
  document.addEventListener('mozfullscreenchange', changeScreen)
  document.addEventListener('fullscreenchange', changeScreen)
  document.addEventListener('msfullscreenchange', changeScreen)
})

onBeforeMount(() => {
  document.removeEventListener('webkitfullscreenchange', changeScreen)
  document.removeEventListener('mozfullscreenchange', changeScreen)
  document.removeEventListener('fullscreenchange', changeScreen)
  document.removeEventListener('msfullscreenchange', changeScreen)
})

function changeScreen() {
  if (document.fullscreenElement) {
    generalData.value.isFullScreen = true
    generalData.value.isShowTop = false
  } else {
    generalData.value.isFullScreen = false
    generalData.value.isShowTop = true
  }
}
</script>

<template>
  <div class="tool-wrap" :class="{ 'hide-bottom-bar': generalData.isFullScreen }">
    <div class="color-list">
      <span>{{ $t('比对差异：') }}</span>
      <div>
        <span class="color-item bg-insert"></span>
        <span class="txt">{{ $t('新增') }}</span>
      </div>
      <div>
        <span class="color-item bg-change"></span>
        <span class="txt">{{ $t('修改') }}</span>
      </div>
      <div>
        <span class="color-item bg-delete"></span>
        <span class="txt">{{ $t('删除') }}</span>
      </div>
      <div class="similar">
        <label>{{ $t('相似度：') }}</label>
        <span class="similar-num" :class="similarityColor">{{ generalData.similarity }}%</span>
      </div>
    </div>
    <div class="tool-right">
      <div class="align">
        <el-button type="primary" link class="align-btn" @click="changeFullScreen"
          ><i class="icon-fdd-a-quanping2"></i>{{ generalData.isFullScreen ? '收起全屏' : '查看全屏' }}</el-button
        >
      </div>
      <div class="align">
        <el-checkbox v-model="generalData.isAlign" @change="handleAlign">{{ $t('同步滚动') }}</el-checkbox>
      </div>
      <div class="zoom">
        <span class="zoom-text">{{ zoomNum }}%</span>
        <el-input-number
          size="small"
          class="zoom-de"
          v-model="zoomNum"
          :step="5"
          :min="50"
          :max="200"
          @change="handleZoom"
        >
        </el-input-number>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
$insert: #1fac78;
$delete: #f85951;
$change: #ffc600;
.color-insert {
  color: $insert;
}
.color-delete {
  color: $delete;
}
.color-change {
  color: $change;
}
.bg-insert {
  background: $insert;
}
.bg-delete {
  background: $delete;
}
.bg-change {
  background: $change;
}
.tool-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 48px;
  padding: 0 20px;
  background: var(--bg-color);
  .color-list {
    display: flex;
    flex: 1;
    justify-content: flex-start;
    .color-item {
      display: inline-block;
      width: 16px;
      height: 6px;
      margin: 0 5px 2px 10px;
      vertical-align: middle;
    }
    .similar {
      padding-left: 20px;
    }
  }
  .tool-right {
    display: flex;
    align-items: center;
    .align {
      margin: 0 20px;
    }
    .zoom {
      position: relative;
      .zoom-text {
        position: absolute;
        left: 34px;
        z-index: 1;
        width: 56px;
        height: 26px;
        line-height: 26px;
        text-align: center;
      }
      .zoom-de {
        width: 120px;
        :deep(span) {
          background-color: initial;
          border: none;
          i {
            font-size: 14px;
            font-weight: bold;
            color: #262626;
          }
        }
        :deep(span.is-disabled) {
          i {
            color: #c0c4cc;
          }
        }
        :deep(input) {
          font-size: 14px;
          border: none;
          opacity: 0;
        }
      }
    }

    // .icon-fdd-a-quanping2 {
    //   font-size: 14px;
    // }
  }
}
.hide-bottom-bar {
  position: absolute;
  bottom: 0;
  opacity: 0;
  &:hover {
    opacity: 1;
  }
}
:deep(.align-btn) {
  span {
    display: flex;
    align-items: center;
  }
}
</style>
