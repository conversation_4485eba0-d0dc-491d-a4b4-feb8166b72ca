// 聊天项接口定义
export interface IChatItem {
  id: string
  userId: string
  corpId?: string
  memberId?: string
  chatTitle: string
  conversationId: string
  chatStatus: number
  createTime: string
  lastAnswerContent?: string
}

// 聊天列表响应接口定义
export interface IChatsListResponse {
  pageNum: number
  pageSize: number
  total: number
  totalPages: number
  list: IChatItem[]
}

// 聊天消息接口定义
export interface IChatMessage {
  id: string
  chatId: string
  messageId: string | null
  chatContent: string
  chatRole: 'user' | 'assistant'
  chatModel: string | null
  sort: number
  messageStatus: number
  followUp: string[] | null
  feedback: any | null
  createTime: string
}

export interface InputReturnResult {
  content: string
  searchType?: string[]
  deepSeek: number
  netsearch: number
  attachInfo: any[]
  chatType: number
}

export interface AttachmentInter {
  fileSuffix: string
  fileName: string
  fileSize: string
  fileId: string
  name: string
  error: string
  attachType: number
  id: string
  status: string
  completed: boolean
}

export interface AiResponseIter {
  event_type: string
  outputs: {
    text: string
    json: any
  }
}

export interface InnerStyConfInter {
  attachShow: boolean
  headerShow: boolean
  uploadPopShow: boolean
  uploadShow: boolean
  lineShow: boolean
  leftFooterBarShow: boolean
  grid: string
}

export interface UploadRawFile extends File {
  uid: number
  isDirectory?: boolean
}

export type UploadStatus = 'ready' | 'uploading' | 'success' | 'fail'
export interface UploadFile {
  name: string
  percentage?: number
  status: UploadStatus
  size?: number
  response?: unknown
  uid: number
  url?: string
  raw?: UploadRawFile
  type: string
}

export interface DifyConversationParams {
  content: string
  attachInfo: any
  // labelId: string
  searchTypes: string[]
  docTypeCode: string
  thinking?: number
  webSearch?: number
  // chatType: number
}

export type ConversationCallback = (isCompleted?: boolean) => void

export interface DifyMessageResponse {
  event: string
  answer: string
  data: any
  messageId: string
  taskId: string
}

export interface feedbackParams {
  rating: string | null
  type?: string
  content?: string
}

export interface TabItem {
  title: string
  count: number
  type: string
  items: any[]
}

export interface CaseJudgementType {
  court_believe_words?: string
  court_ascertained_words?: string
  judge_result_words?: string
}

export interface CaseItem {
  type: string
  court: string
  title: string
  uniqid: string
  data: CaseJudgementType
  caseJudgetType: keyof CaseJudgementType
}

export interface WebItem {
  title: string
  content: string
  url: string
}
