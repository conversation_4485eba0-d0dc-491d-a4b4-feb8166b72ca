<script setup>
// import TextOverTip from '@/components/TextOverTip.vue'
import ExportDialog from './export-dialog.vue'
import { querySemantics } from '@/services/textComparison'
// import { BASE_URL } from '@/utils/config'
import SvgIcon from '@/components/Icons.vue'

import { $t } from '@/utils/i18n'
const props = defineProps({
  diffList: {
    type: Array,
    default() {
      return []
    },
  },
  notationList: {
    type: Array,
    default() {
      return []
    },
  },
  semanticData: {
    type: Object,
    default() {
      return {}
    },
  },
})

const emits = defineEmits(['drawDiffLine', 'drawNotation', 'clearDiffLine'])

const tabActive = ref('all')
const itemActive = ref(0)

const targetDiffList = ref([])
const notationFlag = ref(false)
const anchorDiff = ref(null)
const isBoxShaow = ref(false)
function changeTab() {
  // tabActive.value = val;
  itemActive.value = -1
  notationFlag.value = false
  emits('clearDiffLine')
  if (tabActive.value === 'all') {
    targetDiffList.value = props.diffList.filter((item) => {
      return item.diffType !== 'EQUAL'
    })
  } else {
    targetDiffList.value = props.diffList.filter((item) => {
      return item.diffType === tabActive.value.toUpperCase()
    })
  }
  nextTick(() => {
    onScroll()
  })
}
changeTab()

function getCount(val) {
  let list
  if (val === 'all') {
    list = props.diffList.filter((item) => {
      return item.diffType !== 'EQUAL'
    })
  } else {
    list = props.diffList.filter((item) => {
      return item.diffType === val.toUpperCase()
    })
  }
  return list.length
}
const allCount = computed(() => {
  return getCount('all')
})
const insertCount = computed(() => {
  return getCount('insert')
})
const deleteCount = computed(() => {
  return getCount('delete')
})
const changeCount = computed(() => {
  return getCount('change')
})
const notationCount = computed(() => {
  if (!props.notationList) return 0
  return props.notationList.length > 99 ? '99+' : props.notationList.length
})
const compAllCount = computed(() => {
  return allCount.value < 99 ? allCount.value : '99+'
})

function getDiffType(val) {
  const type = val.toLowerCase()
  if (type === 'insert') return $t('新增')
  if (type === 'delete') return $t('删除')
  if (type === 'change') return $t('修改')
}

const isCollapse = ref(false)
function handleCollapse() {
  if (isCollapse.value) {
    emits('clearDiffLine')
    itemActive.value = -1
  }

  isCollapse.value = !isCollapse.value
}

function clearActive() {
  itemActive.value = -1
}

function drawDiffLine(val, id) {
  itemActive.value = id
  emits('drawDiffLine', val)
  // emits("drawNotation", val);
}

function drawDiffNotation(val, id) {
  itemActive.value = id
  emits('drawNotation', val)
}

const refDiffList = ref(null)

function selectItem(val) {
  const index = targetDiffList.value.findIndex((item) => {
    return (
      item.leftY == val.leftY &&
      item.rightY == val.rightY &&
      item.leftPageNumber == val.leftPageNumber &&
      item.rightPageNumber == val.rightPageNumber
    )
  })
  itemActive.value = index
  const top = refDiffList.value.scrollTop
  const height = refDiffList.value.clientHeight
  const curH = (index + 1) * 99
  if (top > curH) {
    refDiffList.value.scrollTop = curH - height
  } else if (top + height < curH) {
    refDiffList.value.scrollTop = curH - height
  }
}

function selectDiff(val) {
  const index = targetDiffList.value.findIndex((item) => {
    return item.index === val.index
  })
  if (index > -1) {
    drawDiffLine(targetDiffList.value[index], index)
  }
}

function viewNotation() {
  // tabActive.value = "";
  notationFlag.value = true
  targetDiffList.value = props.notationList
}
// function viewDiff() {
//   notationFlag.value = false;
// }

const emptyTexxt = computed(() => {
  if (notationFlag.value) return $t('只显示左侧文档的批注')
  return $t('暂无数据')
})

const refExport = ref(null)
function exportResult() {
  refExport.value.open()
}

function onScroll() {
  if (anchorDiff.value && anchorDiff.value.getBoundingClientRect) {
    let flag = false
    const anchor = anchorDiff.value.getBoundingClientRect()
    if (anchor.y >= document.body.clientHeight) {
      flag = true
    } else {
      flag = false
    }
    isBoxShaow.value = flag
  }
}

const semanticsLoading = ref(false)

async function viewSemantics() {
  try {
    semanticsLoading.value = true
    const { data: compareId } = await querySemantics(props.semanticData)

    window.open(`/semantics?compareId=${compareId}`)
  } catch (error) {
    console.warn(error)
  } finally {
    semanticsLoading.value = false
  }
}

onMounted(() => {
  onScroll()
})

defineExpose({
  selectItem,
  selectDiff,
  clearActive,
  viewNotation,
  drawDiffLine,
  drawDiffNotation,
})
</script>

<template>
  <div class="diff-wrap" :class="{ 'collapse-right': isCollapse }">
    <template v-if="!isCollapse">
      <el-tabs v-show="!notationFlag" v-model="tabActive" @tab-click="changeTab">
        <el-tab-pane name="all">
          <template v-slot:label>
            <span class="tab-item">{{ $t('全部') }}<el-badge
                v-if="allCount > 0"
                :value="allCount"
                :max="99"
                type="primary"
                class="tab-item-badge"
              ></el-badge>
            </span>
          </template>
        </el-tab-pane>
        <el-tab-pane name="insert">
          <template v-slot:label>
            <span class="tab-item"
              >{{ $t('新增') }}<el-badge v-if="insertCount > 0" :value="insertCount" :max="99" type="primary" class="tab-item-badge">
              </el-badge>
            </span>
          </template>
        </el-tab-pane>
        <el-tab-pane name="change">
          <template v-slot:label>
            <span class="tab-item"
              >{{ $t('修改') }}<el-badge v-if="changeCount > 0" :value="changeCount" :max="99" type="primary" class="tab-item-badge">
              </el-badge>
            </span>
          </template>
        </el-tab-pane>
        <el-tab-pane name="delete">
          <template v-slot:label>
            <span class="tab-item"
              >{{ $t('删除') }}<el-badge v-if="deleteCount > 0" :value="deleteCount" :max="99" type="primary" class="tab-item-badge">
              </el-badge>
            </span>
          </template>
        </el-tab-pane>
      </el-tabs>
      <div v-if="targetDiffList.length" ref="refDiffList" class="diff-list" @scroll="onScroll">
        <template v-if="!notationFlag">
          <div
            v-for="(item, id) in targetDiffList"
            :key="id"
            class="diff-list-item"
            :class="{ 'item-active': itemActive == id }"
            @click="drawDiffLine(item, id)"
          >
            <span class="item-angle" :class="`item-${item.diffType.toLowerCase()}`"></span>
            <div>{{ getDiffType(item.diffType) }}</div>
            <div v-if="item.leftText" class="item-wrap">
              <span>{{ $t('左:') }}</span>
              <span class="item-text">{{ item.leftText }}</span>
            </div>
            <div v-if="item.rightText" class="item-wrap">
              <span>{{ $t('右:') }}</span>
              <span class="item-text">{{ item.rightText }}</span>
            </div>
          </div>
        </template>
        <template v-else>
          <div
            v-for="(item, id) in targetDiffList"
            :key="id"
            class="notation-list-item"
            :class="{ 'item-active': itemActive == id }"
            @click="drawDiffNotation(item, id)"
          >
            <div class="item-title">
              <img src="@/assets/images/textComparison/head.png" alt="" class="item-head" />
              <span class="item-name">{{ item.titlePopup }}</span>
              <span class="item-time">{{ item.modifiedDate }}</span>
            </div>
            <div class="item-content">
              <!-- <text-over-tooltip ref-name="testName1" class-name="fs5" :content="item.contents"></text-over-tooltip> -->
            </div>
            <div v-if="item.replyList && item.replyList.length" class="reply-item-wrap">
              <div v-for="(reply, idx) in item.replyList" :key="idx" class="reply-item">
                <div class="item-title">
                  <img src="@/assets/images/textComparison/head.png" alt="" class="item-head" />
                  <span class="item-name">{{ reply.titlePopup }}</span>
                  <span class="item-time">{{ reply.modifiedDate }}</span>
                </div>
                <div class="item-content">
                  <!-- <text-over-tooltip
                    ref-name="testName1"
                    class-name="fs5"
                    :content="reply.contents"
                  ></text-over-tooltip> -->
                </div>
              </div>
            </div>
          </div>
        </template>
        <!-- 末尾锚点 -->
        <span ref="anchorDiff" class="nchor-diff"></span>
      </div>
      <el-empty v-else :description="emptyTexxt"></el-empty>
    </template>
    <div class="collapse" @click="handleCollapse">
      <SvgIcon v-if="!isCollapse" name="risk-close" class="risk-close" color="red" />
      <SvgIcon v-else name="risk-open" class="risk-close" color="red" />
    </div>
    <div
      v-if="!isCollapse"
      class="notation-btn diff-btn"
      :class="{ 'notation-active': !notationFlag }"
      @click="changeTab"
    >
      <span class="notation-btn-text">{{ $t('查看差异') }}</span><span class="notation-btn-num">（{{ compAllCount }}）</span>
    </div>
    <!-- <div v-if="!isCollapse" class="notation-btn" :class="{ 'notation-active': notationFlag }" @click="viewNotation">
      <span class="notation-btn-text">{{ $t('查看批注') }}</span><span class="notation-btn-num">（{{ notationCount }}）</span>
    </div> -->
    <div
      v-loading="semanticsLoading"
      element-loading-spinner="el-icon-loading"
      class="notation-btn semantics"
      @click="viewSemantics"
    >
      <span class="notation-btn-text">{{ $t('语义差异') }}</span>
    </div>
    <div :class="{ 'export-wrap': true, 'box-shadow': isBoxShaow }">
      <el-button type="primary" link class="export-btn" @click="exportResult"
        ><i class="icon-is-upload"></i>{{ $t('导出差异结果') }}</el-button
      >
    </div>
    <export-dialog ref="refExport"></export-dialog>
  </div>
</template>

<style lang="scss" scoped>
.diff-wrap {
  position: relative;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 300px;
  height: 100%;
  padding: 15px 0 15px 15px;
  padding-bottom: 40px;
  background: var(--bg-color);
  border-left: 1px solid #e9e9e9;
  transition: width 0.2s;
  :deep(.el-tabs) {
    width: 270px;
    margin-left: 0;
  }
  :deep(.el-tabs__nav-wrap) {
    &::after {
      width: 97%;
    }
  }
  .export-wrap {
    position: absolute;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 12px 0;
    background: var(--bg-color);
  }
  .tab-item {
    position: relative;
    &-badge {
      position: absolute;
      z-index: 2;
    }
  }
  .diff-list {
    width: 100%;
    overflow-y: auto;
    .diff-list-item {
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      width: calc(100% - 12px);
      height: 84px;
      padding: 0 0 0 10px;
      margin-top: 15px;
      overflow: hidden;
      color: #303133;
      cursor: pointer;
      border: 1px solid #e9e9e9;
      border-radius: 4px;
      &:hover {
        border: 1px solid var(--is-color-773bef);
      }
      .item-wrap {
        display: flex;
      }
      .item-text {
        display: inline-block;
        max-width: 230px;
        margin-left: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .item-angle {
        position: absolute;
        top: -10px;
        left: -10px;
        border-right: 10px solid transparent;
        border-bottom: 10px solid transparent;
        border-left: 10px solid transparent;
        transform: rotate(135deg);
      }
      .item-insert {
        border-top: 10px solid #1fac78;
      }
      .item-delete {
        border-top: 10px solid #f85951;
      }
      .item-change {
        border-top: 10px solid #ffc600;
      }
    }
    .item-active {
      border: 1px solid var(--is-color-773bef) !important;
    }
    .notation-list-item {
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      width: 100%;
      min-height: 64px;
      padding: 10px;
      margin-top: 15px;
      color: #303133;
      cursor: pointer;
      border: 1px solid #e9e9e9;
      border-radius: 8px;
      &:hover {
        border: 1px solid var(--is-color-773bef);
      }
      .item-title {
        display: flex;
        justify-content: space-between;
        padding-right: 10px;
        .item-head {
          width: 12px;
          height: 12px;
          margin-bottom: 2px;
        }
        .item-name {
          flex: 1;
          padding-left: 8px;
          overflow: hidden;
          text-overflow: ellipsis;
          font-size: 12px;
          color: #929292;
          white-space: nowrap;
        }
        .item-time {
          font-size: 12px;
          color: #929292;
        }
      }
      .item-content {
        flex: 1;
        align-items: center;
        padding-top: 16px;
      }
      .reply-item-wrap {
        padding-left: 20px;
        margin-top: 20px;
        .reply-item {
          margin-bottom: 20px;
          .item-content {
            padding-top: 8px !important;
          }
        }
      }
    }
  }
  .collapse {
    position: absolute;
    top: 50vh;
    left: -27px;
    width: 26px;
    height: 40px;
    cursor: pointer;
    transform: translateY(-50%);
    .risk-close {
      width: 26px;
      height: 40px;
      border: none;
    }
  }
  .notation-btn {
    position: absolute;
    top: 140px;
    left: -30px;
    z-index: 10;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 32px;
    padding: 7px 0;
    color: #929292;
    cursor: pointer;
    user-select: none;
    background: rgb(255 255 255 / 100%);
    border-radius: 4px;
    box-shadow: 0 2px 6px 0 rgb(0 0 0 / 12%);
    opacity: 1;
    &:active {
      opacity: 0.5;
    }
    &-text {
      display: inline-block;
      width: 14px;
    }
    &-num {
      font-size: 12px;
      line-height: 20px;
    }
  }
  .semantics {
    top: 152px;
  }
  .notation-active {
    color: var(--is-color-773bef);
    pointer-events: none;
  }
  .diff-btn {
    top: 30px !important;
  }
}
.collapse-right {
  width: 0;
  padding: 0;
  .export-wrap {
    display: none;
  }
}
:deep(.export-btn) {
  span {
    display: flex;
    align-items: center;
  }
}
.box-shadow {
  box-shadow: 0 2px 6px 0 rgb(0 0 0 / 12%);
}
.icon-is-tubiao {
  margin-right: 10px;
}
</style>
