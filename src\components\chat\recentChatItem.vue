<template>
  <div>
    <div
      class="recent-chats-item-container"
      :class="route.params.chatId === chatItem.id ? 'active' : ''"
      @click="focusChat(chatItem)"
    >
      <div class="recent-chats-item-title">{{ chatItem.chatTitle }}</div>
      <div class="edit-button">
        <el-popover
          popper-class="edit-button-popover"
          ref="popover"
          :visible="actionVisible"
          placement="bottom-start"
          :popper-style="{ minWidth: '104px', padding: '4px', width: 'fit-content' }"
          :show-arrow="false"
        >
          <template #reference>
            <div>
              <i class="iconfont icon-action" @click.stop="actionVisible = true">&#xe642;</i>
            </div>
          </template>
          <div class="action-buttons" v-c="closePopover">
            <div class="action-button" @click.stop="rename">
              <i class="iconfont icon-action">&#xe66b;</i>
              <div class="action-button-text">{{ $t('重命名') }}</div>
            </div>
            <div class="action-button" @click.stop="deleteChat">
              <i class="iconfont icon-action">&#xe673;</i>
              <div class="action-button-text">{{ $t('删除') }}</div>
            </div>
          </div>
        </el-popover>
      </div>
    </div>
    <el-dialog v-model="editVisible" @close="editVisible = false" width="400px">
      <template #header>
        <div>{{ $t('编辑对话名称') }}</div>
      </template>
      <div><el-input v-model="chatName" class="edit-chat-title-input" :clearable="false" /></div>
      <template #footer>
        <div style="text-align: right">
          <el-button size="small" @click="editVisible = false">{{ $t('取消') }}</el-button>
          <el-button type="primary" size="small" @click="confirmEditChat">{{ $t('确定') }}</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="deleteVisible" @close="deleteVisible = false" width="400px">
      <template #header>
        <div>
          {{ $t('删除') }}
        </div>
      </template>
      <div>{{ $t('删除后无法恢复，是否继续删除？') }}</div>
      <template #footer>
        <div style="text-align: right">
          <el-button size="small" @click="deleteVisible = false">{{ $t('取消') }}</el-button>
          <el-button type="primary" size="small" @click="confirmDel">{{ $t('删除') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { RESPONSE_CODE_SUCCESS } from '@/constants'
import { ChatItem } from '@/stores/modules/chat/helper'
import { useChatStore } from '@/stores'
import { ClickOutside as vC } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import ChatApi from '@/services/chat'
import { $t } from '@/utils/i18n'

const props = defineProps({
  chatItem: {
    type: Object as () => ChatItem,
    required: true,
  },
})

const emits = defineEmits(['edit-name', 'delete-chat'])
const route = useRoute()
const router = useRouter()
const chatStore = useChatStore()
const actionVisible = ref(false)
const deleteVisible = ref(false)
const editVisible = ref(false)
const chatName = ref(props.chatItem.chatTitle)

const rename = () => {
  editVisible.value = true
  actionVisible.value = false
}
const deleteChat = () => {
  deleteVisible.value = true
  actionVisible.value = false
}
const focusChat = async (item: ChatItem) => {
  chatStore.chatActive.setChatId(item.id)
  router?.push('/chat/' + item.id)
}

const closePopover = () => {
  actionVisible.value = false
}

const confirmEditChat = async () => {
  if (!chatName.value) {
    ElMessage.error($t('请输入正确的名称'))
    return
  }
  const { code, message } = await ChatApi.editChatTitle(props.chatItem.id, chatName.value)
  if (RESPONSE_CODE_SUCCESS == code) {
    ElMessage.success(message)
    emits('edit-name', { id: props.chatItem.id, chatTitle: chatName.value })
    actionVisible.value = false
    editVisible.value = false
  } else {
    ElMessage.error(message)
  }
}

const confirmDel = async () => {
  const { code, message } = await ChatApi.deleteChats([props.chatItem.id])
  if (RESPONSE_CODE_SUCCESS == code) {
    if (chatStore.chatActive.chatId === props.chatItem.id) {
      chatStore.chatActive.initData()
      router.push('/chat')
    }
    chatStore.deleteChatsFromStore(props.chatItem.id)
    ElMessage.success(message)
    emits('delete-chat', props.chatItem.id)
    actionVisible.value = false
    deleteVisible.value = false
  } else {
    ElMessage.error(message)
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-tooltip__trigger) {
  display: flex;
  align-items: center;
}
.recent-chats-item-container {
  display: flex;
  align-items: center;
  height: 2.5rem;
  padding: 0 0.5rem;
  margin: 0.25rem 0;
  margin-right: 0.5rem;
  margin-left: 1.5rem;
  font-size: 0.875rem;
  line-height: 2.5rem;
  cursor: pointer;
  .recent-chats-item-title {
    width: 9.25rem;
    height: 1rem;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 0.875rem;
    line-height: 1rem;
    white-space: nowrap;
  }
  .edit-button {
    display: none;
    height: fit-content;
    margin-right: 0.5rem;
    margin-left: auto;
    .icon-action {
      font-size: 1.25rem;
      cursor: pointer;
    }
  }
}
.recent-chats-item-container:hover {
  background-color: var(--input-bg);
  border-radius: 0.25rem;
  .recent-chats-item-title {
    width: 7.5rem;
  }
  .edit-button {
    display: block;
  }
}
.active {
  color: var(--main-font);
  background-color: rgb(73 46 209 / 7%) !important;
  border-radius: 0.25rem;
}
.action-buttons {
  display: flex;
  flex-direction: column;
  .action-button {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 5.75rem;
    height: 2rem;
    padding: 0.25rem 0.5rem;
    cursor: pointer;
    &:hover {
      background-color: var(--input-bg);
      border-radius: 0.25rem;
    }
    svg {
      margin-right: 0.5rem;
    }
    div {
      font-size: 1rem;
      line-height: 1.5rem;
      color: #262626;
    }
    .action-button-text {
      margin-left: 0.25rem;
      font-size: 0.875rem;
    }
  }
}
.edit-chat-title-input {
  width: 100%;
}
</style>
