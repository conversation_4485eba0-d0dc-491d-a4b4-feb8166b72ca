// 彩云翻译配置文件
// 请在这里填入您的彩云翻译API配置信息

export const CAIYUN_TRANSLATE_CONFIG = {
  // 彩云翻译Token
  token: 'e488myh66tkajx95dflm', // 请填入您的彩云翻译token

  // API地址
  apiUrl: 'http://api.interpreter.caiyunai.com/v1/translator'
}

// 使用说明：
// 1. 请到彩云小译开放平台申请API: https://dashboard.caiyunapp.com/
// 2. 注册账号并创建应用获取token
// 3. 将token填入上面的配置中
// 4. 保存文件后重新运行翻译脚本

// 彩云翻译支持的语言方向：
// zh2en: 中文 -> 英文
// zh2ja: 中文 -> 日文  
// zh2ko: 中文 -> 韩文
// zh2fr: 中文 -> 法文
// zh2es: 中文 -> 西班牙文
// zh2ru: 中文 -> 俄文
// en2zh: 英文 -> 中文
// ja2zh: 日文 -> 中文
// auto2zh: 自动检测 -> 中文

// 注意：请不要将此文件提交到版本控制系统中，以保护您的API密钥安全
