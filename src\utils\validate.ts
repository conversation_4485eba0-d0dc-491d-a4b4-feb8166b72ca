import { $t } from '@/utils/i18n'

type Callback = (error?: Error) => void

export function validUsername(rule: any, value: string, callback: Callback) {
  if (value === '') {
    callback(new Error($t('请输入用户名')))
  } else if (value.length < 1 || value.length > 60) {
    callback(new Error($t('请输入正确的用户名信息')))
  } else {
    callback()
  }
}

export function validPassword(rule: any, value: string, callback: Callback) {
  if (value === '' || value === undefined || value === null) {
    callback(new Error($t('请输入密码')))
  } else if (value.length < 6 || value.length > 50) {
    callback(new Error($t('请输入6-50个字符')))
  } else {
    callback()
  }
}
