.el-pagination {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
  .el-pager {
    margin-top: 2px;
    .number {
      min-width: 24px;
      height: 24px;
      padding: 0 !important;
      margin-right: 8px;
      font-weight: 400 !important;
      line-height: 24px;
      border: 1px solid #e6e8ea;
      border-radius: 4px;
      &:last-child {
        margin-right: 0;
      }
    }
    li.is-active {
      border: 1px solid var(--main-bg);
    }
    li.number.active + li {
      border-left: 1px solid #e6e8ea;
    }
  }
}
.el-table--enable-row-hover .el-table__body tr:hover > td {
  background-color: #f1f5ff !important;
}

@mixin text-hidden {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
