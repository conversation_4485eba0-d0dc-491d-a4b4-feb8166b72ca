<script setup lang="ts">
import WpsOffice from '@/components/wps/index.vue'
import { pdfViewer } from '@iterms/pdf-viewer'
import ContractUpload from './components/ContractUpload.vue'
import UploadDialog from './components/UploadDialog.vue'
import ExtractionFields from './components/ExtractionFields/index.vue'

import { createContact } from '@/services/contract'
import { contractRefKey } from './types'
import type { ExtractionCantract, DocumentViewerRef } from './types'
import { RESPONSE_CODE_SUCCESS } from '@/constants'

import { useUserStore } from '@/stores'
import { PDF_BASE_URL } from '@/utils/config'

const token = ref('')
const fileType = ref('')
const fileId = ref('')

const useStore = useUserStore()
onMounted(() => {
  token.value = useStore.token
})

const contractInfo = ref<ExtractionCantract>({
  contractUrl: '',
  contractName: '',
  isDocFile: false,
})

const contractRef = ref<DocumentViewerRef>()

const uploadDialogRef = ref<InstanceType<typeof UploadDialog>>()

const handleUpload = () => {
  uploadDialogRef.value?.dialogShow()
}

const orginBlob = ref<Blob>()
const showOffice = ref(false)
// const showPostil = ref(true) ////////
const isExtract = ref(false)
const isFulfillment = ref(false)

// const extractionFields = ref(null)
// const extractFieldRef = ref(null)

const handleUploadFile = async (val: ExtractionCantract) => {
  showOffice.value = false

  // extractFieldRef.value.isExtracted = false
  // extractFieldRef.value.isFulfillmented = false
  contractInfo.value = val
  fileType.value = !contractInfo.value.isDocFile ? 'pdf' : 'wps/doc/docx'
  if (contractInfo.value.isDocFile) {
    // wps 显示逻辑（word）
    const { code, data } = await createContact({
      fileCode: contractInfo.value.contractUrl,
      fileName: contractInfo.value.contractName,
    })
    if (code === RESPONSE_CODE_SUCCESS) {
      const { contractId } = data as { contractId: string }
      fileId.value = contractId
    }
  } else {
    // pdf 显示逻辑
    orginBlob.value = contractInfo.value.fileBlob
  }

  nextTick(() => {
    showOffice.value = true
    isExtract.value = true
    isFulfillment.value = true
    //   nextTick(() => {
    //     contractRef.value.init(contractInfo.value.contractUrl)
    //     if (!contractInfo.value.isDocFile) {
    //       // extractFieldRef.value.startExtraction();
    //       isExtract.value = true
    //       isFulfillment.value = true
    //     }
    //   })
  })
}

provide('contractInfo', contractInfo)
provide(contractRefKey, contractRef)
provide('isExtract', isExtract)
provide('isFulfillment', isFulfillment)
</script>

<template>
  <div class="home-page">
    <div class="page-left">
      <contract-upload v-if="!contractInfo.contractUrl" @uploadFile="handleUploadFile"></contract-upload>
      <div class="office-box" v-if="showOffice">
        <div class="header-box">
          <div class="header-info">
            <span class="contract-name"> {{ contractInfo.contractName }} </span>
            <el-button class="upload-button" @click="handleUpload">{{ $t('重新上传') }}</el-button>
          </div>
        </div>
        <WpsOffice
          ref="contractRef"
          :file-id="fileId"
          :office-type="fileType"
          :token="token"
          appId=""
          :isRevison="false"
          class="office-frame"
          v-if="contractInfo.isDocFile && fileId"
        ></WpsOffice>
        <pdfViewer ref="contractRef" v-else :file="orginBlob" :base-path="PDF_BASE_URL" :key="'bbb'"></pdfViewer>
      </div>
    </div>
    <extraction-fields ref="extractFieldRef" :contract-info="contractInfo"></extraction-fields>
    <UploadDialog ref="uploadDialogRef" @upload-succ="handleUploadFile" />
  </div>
</template>

<style lang="scss" scoped>
.home-page {
  display: flex;
  flex: 1;
  width: 100%;
  height: 100vh;
  min-height: 90%;
  overflow: auto;
  .page-left {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    background: #fff;
    .office-box {
      display: flex;
      flex: 1;
      flex-direction: column;
      width: 100%;
      height: 100%;
      .header-box {
        z-index: 2;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        width: 100%;
        height: 56px;
        padding: 12px 24px;
        line-height: 24px;
        background: #fff;
        .header-info {
          display: flex;
          flex: 1;
          align-items: center;
          width: 0;
          margin-top: 4px;
          .contract-name {
            margin-right: 8px;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 16px;
            line-height: 32px;
            color: #262626;
            white-space: nowrap;
          }
        }
      }
      .office-frame {
        :deep(.web-office-iframe) {
          height: calc(100% - 3px) !important;
        }
      }
    }
  }
}
.home-full {
  height: calc(100vh - 0px);
}
</style>
