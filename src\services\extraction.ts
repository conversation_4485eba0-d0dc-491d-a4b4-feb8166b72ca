import { post } from '@/services'
import type { IResponse } from '@/services'

// 基本字段抽取表单数据&基本字段抽取处理之后 的字段类型
export interface IExtractField {
  label: string
  value: string
  isHovered?: boolean
}
// 履约信息抽取表单数据&履约信息抽取处理之后 的字段类型
export interface IExtractPerformance {
  label: string // 字段标签（前端显示用）
  value: string // 处理后的字段值（空值转为"无"）
  locationText: string // 位置文本（空值转为空字符串）
  extractValueList?: string[] // 保留原始值数组（如果存在）
}

// 基本信息抽取
// 请求参数
export interface IModelExtractFieldParams {
  fileUrl?: string
  fileCode?: string
  fileContent?: string
  contractName: string
  extractKeyWords: string[]
}
// 响应参数
export interface IExtractFieldResponse {
  extractKeyWord: string
  extractValue: string
  order: number
}
export interface IExtractFieldsList {
  result: IExtractFieldResponse[]
}
// 接口
export function modelExtractField<T>(data: IModelExtractFieldParams): Promise<IResponse<T>> {
  return post<T>(`/info-extract/base`, data)
}

// 履约字段抽取(台账抽取)
// 响应参数
export interface IExtractPerformanceResponse {
  extractKeyWord: string
  extractValue: string
  order: number
  extractValueList?: string[]
  locationText: string // 定位用的字段
}
export interface IExtractPerformanceList {
  result: IExtractPerformanceResponse[][]
}
// 接口
export function modelExtractPerformance<T>(data: IModelExtractFieldParams): Promise<IResponse<T>> {
  return post<T>(`/info-extract/performance`, data)
}
