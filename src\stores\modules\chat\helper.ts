import { unionBy } from 'lodash-es'
import { Cache } from '@/utils/cache'
import JSON5 from 'json5'
import { IUploadFile } from '@/components/chat/helper'
import { $t } from '@/utils/i18n'

// /**
//  * content_type : text json
//  * event_type : "ChatAgent"=真正的回答 "FollowUpQuery"=追问 "Workflow"=法律知识问答
//  */
export const enum EventTypeEnum {
  CHATAGENT = 'ChatAgent',
  FOLLOWUP = 'FollowUpQuery',
  WORKFLOW = 'Workflow',
  THOUGHT = 'thought',
  BAIDU = 'BaiduSearchWithModel',
  DEEP_THOUGHT = 'chat_reasoning',
}

//大数据返回数据的状态
export const enum ChatEeventStatus {
  DONE = 'done',
  RUNNING = 'running',
}

//对话消息角色
export enum ChatRoleEnum {
  QUESTION = 'user',
  ANSWER = 'assistant',
  SUGGEST = 'suggest',
}

//对话状态
export enum ChatStatusEnum {
  PREPARE = 'prepare',
  PROGRESS = 'progress',
  RUNNING = 'running',
  ERROR = 'error',
  STOP = 'stop',
  DONE = 'done',
  FILE_CONTENT_EXTRACTOR = 'file_content_extractor',
  LAW_SEARCHING = 'law_searching',
  CASE_SEARCHING = 'case_searching',
  WEB_SEARCHING = 'web_searching',
  ARTICLE_SEARCHING = 'article_searching',
  DEEPTHINKING = 'deepthinking',
}

export const CHAT_STATUS_MAP = {
  [ChatStatusEnum.PREPARE]: $t('运行中'),
  [ChatStatusEnum.PROGRESS]: $t('运行中'),
  [ChatStatusEnum.STOP]: $t('思考已终止'),
  [ChatStatusEnum.FILE_CONTENT_EXTRACTOR]: $t('文件解析中'),
  [ChatStatusEnum.LAW_SEARCHING]: $t('法律检索中'),
  [ChatStatusEnum.CASE_SEARCHING]: $t('类案检索中'),
  [ChatStatusEnum.WEB_SEARCHING]: $t('联网搜索中'),
  [ChatStatusEnum.ARTICLE_SEARCHING]: $t('文章检索中'),
  [ChatStatusEnum.DEEPTHINKING]: $t('思考中'),
  [ChatStatusEnum.RUNNING]: $t('生成中'),
  [ChatStatusEnum.DONE]: $t('已完成深度思考'),
  [ChatStatusEnum.ERROR]: $t('运行错误'),
}

export enum ChatProcessEnum {
  RUNNING = 'running',
  DONE = 'done',
}

class deepThinkData {
  title = ''
  text = ''
  textList: QuoteObj[] = []
  status = ChatProcessEnum.RUNNING
  constructor(title: string) {
    this.title = title
  }
  setTextList(textList: QuoteObj[]) {
    if (this.textList.length == 0) {
      this.textList = textList
    } else {
      this.textList = unionBy(this.textList, textList, 'text')
    }
  }
  setTitle(title: string) {
    this.title = title
    return this
  }
  setStatus(status: ChatProcessEnum) {
    this.status = status
    return this
  }
  setText(text: string) {
    this.text = text
    return this
  }
}

export class ChatAcitve {
  chatId = ''
  searchTypes: string[] = []
  question = ''
  docTypeCode = ''
  thinking = 0
  webSearch = 0
  chatAttachInfo: any = []
  taskId = ''
  workflowStatus = ''
  constructor() {}
  initData() {
    this.chatId = ''
    this.searchTypes = []
    this.question = ''
    this.docTypeCode = ''
    this.thinking = 0
    this.webSearch = 0
    this.chatAttachInfo = []
    this.taskId = ''
    this.workflowStatus = ''
    return this
  }
  setWorkflowStatus(status: string) {
    this.workflowStatus = status
    return this
  }
  getWorkflowStatus() {
    return this.workflowStatus || ''
  }
  setThinking(thinking: number): ChatAcitve {
    this.thinking = thinking
    return this
  }
  getThinking() {
    return this.thinking || 0
  }
  setWebSearch(webSearch: number): ChatAcitve {
    this.webSearch = webSearch
    return this
  }
  getWebSearch() {
    return this.webSearch || 0
  }
  setDocTypeCode(typeCode: string) {
    this.docTypeCode = typeCode
    return this
  }
  setSearchTypes(searchType: string[]) {
    this.searchTypes = searchType
    return this
  }
  setQuestion(question: string) {
    this.question = question
    return this
  }
  getQuestion() {
    return this.question
  }
  setChatAttachInfo(attachInfo: IUploadFile[]): ChatAcitve {
    this.chatAttachInfo = attachInfo
    return this
  }

  removeChatActiveInfo() {
    this.chatAttachInfo.length = 0
    this.question = ''
    return this
  }
  getChatAttachInfo() {
    return this.chatAttachInfo || []
  }
  setChatId(chatId: string): ChatAcitve {
    this.chatId = chatId
    return this
  }
  getChatId() {
    return this.chatId
  }
  setTaskId(taskId: string): ChatAcitve {
    this.taskId = taskId
    return this
  }
  getTaskId() {
    return this.taskId
  }
}
interface ChatItemParams {
  id: string
  chatTitle: string
  edit?: boolean
  focus?: boolean
  visible?: boolean
  conversationId: string
  createTime: string
  userId: string
  lastAnswerContent?: string
  corpId?: string
  memberId?: string
  chatStatus: number
}
export class ChatItem {
  id = ''
  chatTitle = ''
  edit: boolean = false
  focus: boolean = false
  visible: boolean = false
  conversationId: string = ''
  createTime = ''
  userId = ''
  lastAnswerContent: string = ''
  corpId?: string
  memberId?: string
  chatStatus: number = 0
  constructor(params?: ChatItemParams) {
    Object.assign(this, params || {})
  }

  static create = (params: ChatItemParams) => {
    return new ChatItem(params)
  }
}

export const enum AboutContentEnum {
  WEB = 'web',
  CASES = 'cases',
  LAW = 'law',
  ARTICLE = 'article',
  FOLLOWUP = 'followup',
  KNOWLEDGE = 'knowledge',
}

export class QuoteObj {
  text = ''
  isTitle = false
  constructor(text: string, isTitle = false) {
    this.text = text
    this.isTitle = isTitle
  }
}
export class ChatMessage {
  cache = new Cache()
  id = ''
  //消息id(answer才有)
  messageId = ''
  chatId = ''
  chatContent = ''
  chatRole = ''
  showThoughtProcessBar = true
  copyContent = { editText: '', cleanText: '' }
  deepThinkData = new deepThinkData($t('思考过程'))
  quoteAbout = new deepThinkData('')
  label?: { id: string; labelName: string }
  isDeepSeek: number = 0
  isWebSearch: number = 1
  chatAttachInfos: IUploadFile[] = []
  status = ChatStatusEnum.PREPARE /// loading ,progress,stop
  showTools = false
  isLast = false
  feedback?: { rating: string; type: string; content: string }
  suggest: string[] = []
  messageStatus = 1
  constructor() {}
  setStatus(status: ChatStatusEnum) {
    this.status = status
  }
  setContent(content: string) {
    this.chatContent = content
  }

  referenceStr = ''
  referenceBufferEnd = false
  outputEnd = false

  thinkingBuffer: string[] = []
  getFlowUp(contentStr: string) {
    const contentData = JSON5.parse(contentStr)
    const suggest = contentData.filter((data: any) => data.event_type == EventTypeEnum.FOLLOWUP)
    if (suggest.length && suggest[0].outputs && suggest[0].outputs.json) {
      this.suggest = suggest[0].outputs.json.follow_up_querys
    }
  }

  setUnionData(key: string, value: string) {
    if (this.cache.has(key)) {
      this.cache.put(key, [...value])
    } else {
      this.cache.put(key, [value])
    }
  }
  getDifyThinkingContent(text: string) {
    const regex = /<think[^>]*>([\s\S]*?)<\/think>/i
    const match = text.match(regex)
    if (match) {
      this.deepThinkData.setText(match[1])
    }
  }
  getDifyMainContent(text: string) {
    const outsideRegex = /<\/think>([\s\S]*)$/i
    const outsideMatch = text.match(outsideRegex)
    this.chatContent = outsideMatch ? outsideMatch[1].trim() : text
  }
  titleStr: string[] = [$t('已阅读：')]
  setDifyStatus(data: any) {
    if (data.nodeType === 'llm') {
      if (data.title === 'deepseek-r1') {
        this.setStatus(ChatStatusEnum.DEEPTHINKING)
      }
    } else if (data.nodeType === 'tool') {
      if (data.title == 'law_search') {
        this.setStatus(ChatStatusEnum.LAW_SEARCHING)
      } else if (data.title == 'case_search') {
        this.setStatus(ChatStatusEnum.CASE_SEARCHING)
      } else if (data.title == 'web_search') {
        this.setStatus(ChatStatusEnum.WEB_SEARCHING)
      } else if (data.title == 'article_search') {
        this.setStatus(ChatStatusEnum.ARTICLE_SEARCHING)
      }
    }
  }
  buildDifySerachResult(data: any, needShowdeepThinkData = false) {
    if (!data) {
      return
    }
    if (data.nodeType === 'answer') {
      if (data.outputs && data.outputs.answer) {
        const answerText = data.outputs.answer
        if (answerText) {
          if (needShowdeepThinkData) {
            this.getDifyThinkingContent(answerText)
          }

          this.getDifyMainContent(answerText)
        }
      }
    } else if (data.nodeType === 'tool') {
      if (data.title == 'law_search' || data.title == 'law_search_dev') {
        if (data.outputs && data.outputs.text) {
          const unionData = JSON5.parse(data.outputs.text)
          if (unionData && unionData.text && Array.isArray(unionData.text)) {
            const laws = unionData.text
            if (laws.length) {
              this.setUnionData(AboutContentEnum.LAW, laws)
              const quotes: QuoteObj[] = [new QuoteObj($t('法规'), true)]
              unionData.text.forEach((law: any) => {
                quotes.push(new QuoteObj(law.lawName + ' ' + law.lawNum, false))
              })
              this.titleStr.push(`${quotes.length - 1}个法规`)
              this.quoteAbout.setTextList(quotes)
            }
          }
        }
      } else if (data.title == 'case_search') {
        if (data.outputs && data.outputs.text) {
          const unionData = JSON5.parse(data.outputs.text)
          if (unionData && unionData.text && Array.isArray(unionData.text)) {
            const cases = unionData.text
            this.setUnionData(AboutContentEnum.CASES, cases)
            const quotes: QuoteObj[] = [new QuoteObj($t('案例'), true)]
            cases.forEach((it: any) => [quotes.push(new QuoteObj(it.caseid, false))])
            if (cases.length) {
              this.titleStr.push(`${quotes.length - 1}个案例`)
              this.quoteAbout.setTextList(quotes)
            }
          }
        }
      } else if (data.title == 'web_search') {
        if (data.outputs && data.outputs.json && Array.isArray(data.outputs.json)) {
          if (data.outputs.json.length > 0) {
            const webs = data.outputs.json[0].result
            if (webs.length) {
              this.setUnionData(AboutContentEnum.WEB, webs)
              const quotes: QuoteObj[] = [new QuoteObj($t('网页'), true)]
              webs.forEach((web: any) => {
                quotes.push(new QuoteObj(web.title, false))
              })
              this.titleStr.push(`${quotes.length - 1}个网页`)
              this.quoteAbout.setTextList(quotes)
            }
          }
        }
      } else if (data.title == 'article_search') {
        if (data.outputs && data.outputs.json && Array.isArray(data.outputs.json)) {
          const articles = data.outputs.json[0].text
          if (articles.length) {
            this.setUnionData(AboutContentEnum.ARTICLE, articles)
            const quotes: QuoteObj[] = [new QuoteObj($t('实务文章'), true)]
            articles.forEach((article: any) => {
              quotes.push(new QuoteObj(article.document_name, false))
            })
            this.titleStr.push(`${quotes.length - 1}个实务文章`)
            this.quoteAbout.setTextList(quotes)
          }
        }
      } else if (data.title == 'knowledge_search') {
        // if (data.outputs && data.outputs.json && Array.isArray(data.outputs.json)) {
        // 	const knowledges = data.outputs.json[0].result;
        // 	if (knowledges.length) {
        // 		this.setUnionData(AboutContentEnum.KNOWLEDGE, knowledges);
        // 		const quotes: QuoteObj[] = [new QuoteObj('知识库', true)];
        // 		console.log(quotes, 'knowledge_search');
        // 		knowledges.forEach((article: any) => {
        // 			quotes.push(new QuoteObj(article.document_name, false));
        // 		});
        // 		this.titleStr.push(`${quotes.length - 1}个知识库文章`);
        // 		this.quoteAbout.setTextList(quotes);
        // 	}
        // }
      }

      const endTitleStr = this.titleStr[0] + this.titleStr.slice(1).join('，')
      this.quoteAbout.setTitle(endTitleStr)
      this.quoteAbout.setStatus(ChatProcessEnum.DONE)
    }
  }
  getDifyAnswerContent(contentStr: string) {
    const contentData = JSON5.parse(contentStr)
    if (Array.isArray(contentData)) {
      contentData.forEach(({ data }) => {
        this.buildDifySerachResult(data, true)
      })
    }
    this.setStatus(ChatStatusEnum.DONE)
  }
  isThink = false
  difyRecevier(event: any, content: string, data: any) {
    if (event === 'message') {
      if (content.indexOf('<think') != -1) {
        this.isThink = true
      }
      if (content.indexOf('</think>') != -1) {
        this.isThink = false
        this.deepThinkData.setStatus(ChatProcessEnum.DONE)
      }
      if (this.isThink) {
        const str = (this.deepThinkData.text + content).replace(/<think[^>]*>([\s\S]*?)/i, '').replace(/<\/think>/i, '')
        this.deepThinkData.setText(str)
      }

      if (!this.isThink) {
        this.setStatus(ChatStatusEnum.RUNNING)
        this.chatContent += content.replace(/<\/think>/i, '')
      }
    } else if (event === 'node_started') {
      this.setDifyStatus(data)
    } else if (event === 'node_finished') {
      this.buildDifySerachResult(data)
    }
  }

  static createByUser({
    chatContent,
    chatRole,
    chatId,
    id,
    isDeepSeek,
  }: {
    chatContent: string
    chatRole: ChatRoleEnum
    chatId: string
    id: string
    isDeepSeek: number
  }) {
    const instance = new ChatMessage()
    instance.chatRole = chatRole
    instance.chatId = chatId
    instance.id = id
    instance.isDeepSeek = isDeepSeek
    if (chatRole == ChatRoleEnum.QUESTION) {
      const { label, content, chatAttachInfos } = JSON.parse(chatContent)
      let tempContent = content
      if (label && label.labelName) {
        tempContent = tempContent.replace(label.labelName, '')
      }
      instance.chatContent = tempContent
      if (chatAttachInfos && Array.isArray(chatAttachInfos)) {
        instance.chatAttachInfos = chatAttachInfos.map((attach) => {
          return new IUploadFile(attach)
        })
      }
      instance.label = label
    }
    return instance
  }
  static createFromResponse(responseData: any) {
    //解析数据库对话内容
    const instance = new ChatMessage()
    const { chatContent, chatId, chatRole, id, isLast, showTools, messageId, feedback, messageStatus } = responseData
    instance.chatId = chatId
    instance.messageId = messageId
    instance.isLast = isLast
    instance.chatRole = chatRole
    instance.id = id
    instance.showTools = showTools
    instance.feedback = feedback
    instance.messageStatus = messageStatus
    if (chatRole == ChatRoleEnum.ANSWER) {
      //回答
      if (messageStatus === 1) {
        instance.getDifyAnswerContent(chatContent)
        instance.quoteAbout.setStatus(ChatProcessEnum.DONE)
        instance.deepThinkData.setStatus(ChatProcessEnum.DONE)
        instance.setStatus(ChatStatusEnum.DONE)
        instance.showThoughtProcessBar = true
      } else if (messageStatus === 0) {
        instance.setStatus(ChatStatusEnum.STOP)
      }
    } else {
      //问题
      let chatContentJson: any = {}
      try {
        chatContentJson = JSON.parse(chatContent)
      } catch (e) {
        chatContentJson = { content: chatContent, chatAttachInfos: [] }
      }
      const { content, chatAttachInfos, thinking, webSearch } = chatContentJson
      instance.isDeepSeek = thinking ? 1 : 0
      instance.isWebSearch = webSearch ? 1 : 0
      const tempContent = content
      instance.chatContent = tempContent
      if (chatAttachInfos && Array.isArray(chatAttachInfos)) {
        instance.chatAttachInfos = chatAttachInfos.map((attach) => {
          return new IUploadFile(attach)
        })
      }
    }
    return instance
  }
}

export class ChatCommand {
  chatId: string = ''
  content: string = ''
  thinking: number = 0
  webSearch: number = 0
  chatAttachInfo: IUploadFile[] = []
  searchTypes: string[] = []
  docTypeCode: string = ''
  modeCode: string = ''
  constructor() {}
  setChatId(chatId: string) {
    this.chatId = chatId
    return this
  }
  setContent(content: string) {
    this.content = content
    return this
  }
  setThinking(thinking: number) {
    this.thinking = thinking
    return this
  }
  setWebSearch(webSearch: number) {
    this.webSearch = webSearch
    return this
  }
  //创建数据的副本而不是存储引用
  setChatAttachInfo(chatAttachInfo: IUploadFile[]) {
    this.chatAttachInfo = JSON.parse(JSON.stringify(chatAttachInfo))
    return this
  }
  setSearchTypes(searchTypes: string[]) {
    this.searchTypes = searchTypes
    return this
  }
  setDocTypeCode(docTypeCode: string) {
    this.docTypeCode = docTypeCode
    return this
  }
  setModeCode(modeCode: string) {
    this.modeCode = modeCode
    return this
  }
}
