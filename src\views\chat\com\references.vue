<template>
  <div class="references-container">
    <div class="references-container-header">
      <div style="flex: 1">{{ $t('引用内容') }}</div>
      <div style="cursor: pointer" @click="closeSide"><i class="iconfont" style="font-size: 1.5rem">&#xe651;</i></div>
    </div>
    <div v-if="tabs.length > 1" class="references-tabs">
      <div
        v-for="(tab, index) in tabs"
        :key="index"
        class="references-tab"
        :class="activeTab == index ? 'active' : ''"
        ref="tabItems"
        @click="
          () => {
            selectTab(index, tab)
          }
        "
      >
        {{ tab.title }}({{ tab.count }})
      </div>
      <div class="tab-slider" :style="sliderStyle"></div>
    </div>
    <el-scrollbar :style="{ height: `calc(100% - ${tabs.length > 1 ? '8.125rem' : '2.75rem'})` }">
      <div v-if="getArticles.length" class="side-console-body">
        <div v-for="(item, index) in getArticles" :key="index" class="art-box" style="padding-bottom: 16px">
          <div v-if="tabType == 'web'">
            <div class="article-header">
              <div class="label">
                <span>{{ $t('网页') }}{{ index + 1 }}</span>
              </div>
              <div class="title word-break">{{ item.title }}</div>
            </div>
            <div class="article-body" v-html="item.content"></div>
            <div class="article-link">
              <el-link underline="never" :href="item.url" target="_blank"
                >{{ $t('查看详情') }}<Icons name="arrow-right-plat" width="1.25rem" height="1.25rem" />
              </el-link>
            </div>
          </div>
          <div v-if="tabType == 'article'">
            <div class="article-header">
              <div class="label" style="width: 60px">
                <span>{{ $t('实务文章') }}{{ index + 1 }}</span>
              </div>
              <div class="title word-break">{{ item.document_name }}</div>
            </div>
            <div class="article-body" v-html="item.content"></div>
            <!-- <div class="article-link">
								<el-link underline="never" :href="item.url" target="_blank">{{ $t('查看详情') }}<Icons name="arrow-right-plat" width="1.25rem" height="1.25rem" />
								</el-link>
							</div> -->
          </div>
          <div v-if="tabType == 'knowledge'">
            <div class="article-header">
              <div class="label" style="width: 60px">
                <span>{{ $t('知识库') }}{{ index + 1 }}</span>
              </div>
              <div class="title word-break">{{ item.document_name }}</div>
            </div>
            <div class="article-body" v-html="item.content"></div>
          </div>
          <div v-if="tabType == 'law'">
            <div class="article-header">
              <div class="label">
                <span>{{ $t('法规') }}{{ index + 1 }}</span>
              </div>
              <div class="title word-break">{{ item.lawName }} {{ item.lawNum }}</div>
            </div>
            <div class="article-time">
              <div>{{ item.status }}</div>
              <!-- <div>|</div>
							<div>{{ $t('施行日期') }}</div>
							<div>{{ item.effectDate }}</div> -->
            </div>
            <div class="article-body">
              {{ item.lawContent }}
            </div>
          </div>

          <div v-if="tabType == 'cases'">
            <div class="article-header">
              <div class="label">
                <span>{{ $t('案例') }}{{ index + 1 }}</span>
              </div>
              <div class="title word-break">{{ item.title }}</div>
            </div>
            <div class="side-console-case-tabs">
              <div
                v-if="item.data.court_believe_words"
                class="case-tab"
                :class="item.caseJudgetType == 'court_believe_words' ? 'active' : ''"
                @click="activeCaseJudget(item, 'court_believe_words')"
              >
                <div class="case-text">{{ $t('本院认为') }}</div>
                <div class="case-bar"></div>
              </div>
              <div
                v-if="item.data.court_ascertained_words"
                class="case-tab"
                :class="item.caseJudgetType == 'court_ascertained_words' ? 'active' : ''"
                @click="activeCaseJudget(item, 'court_ascertained_words')"
              >
                <div class="case-text">{{ $t('本院查明') }}</div>
                <div class="case-bar"></div>
              </div>
              <div
                v-if="item.data.judge_result_words"
                class="case-tab"
                :class="item.caseJudgetType == 'judge_result_words' ? 'active' : ''"
                @click="activeCaseJudget(item, 'judge_result_words')"
              >
                <div class="case-text">{{ $t('裁判结果') }}</div>
                <div class="case-bar"></div>
              </div>
            </div>
            <div class="article-body" v-html="renderHtml(item)"></div>
            <div class="article-link">
              <el-link underline="never" @click="viewCase(item.uniqid)"
                >{{ $t('查看详情') }}<Icons name="arrow-right-plat" width="1.25rem" height="1.25rem" />
              </el-link>
            </div>
          </div>
          <div v-if="index != getArticles.length - 1" class="line"></div>
        </div>
      </div>

      <div v-else>
        <div style="display: flex; align-items: center; justify-content: center">
          <el-empty :description="$t('暂无详情')" />
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>
<script lang="ts" setup>
import { computed, onMounted, ref, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { uniqBy } from 'lodash-es'
import type { Cache } from '@/utils/cache'
import { AboutContentEnum } from '@/stores/modules/chat/helper'
import type { TabItem, CaseJudgementType, CaseItem, WebItem } from '@/types/chat'
import { $t } from '@/utils/i18n'

const router = useRouter()
const activeTab = ref(0)
const emits = defineEmits(['close'])
const articleData = ref<any[]>([])
const tabType = ref('')

const selectTab = (index: number, tab: TabItem): void => {
  tabType.value = tab.type
  activeTab.value = index
  const finder = tabs.value.find((it) => it.type == tab.type)
  if (finder) {
    articleData.value = finder.items
  }
}

const getArticles = computed(() => {
  return articleData.value
})

const closeSide = () => {
  emits('close')
}

const tabItems = ref([])
const sliderStyle = computed(() => {
  if (!tabItems.value[activeTab.value]) return {}

  const activeItem: any = tabItems.value[activeTab.value]
  return {
    width: `${activeItem.offsetWidth}px`,
    left: `${activeItem.offsetLeft}px`,
  }
})

const activeCaseJudget = (item: CaseItem, type: keyof CaseJudgementType): void => {
  item.caseJudgetType = type
}
const viewCase = (id: string) => {
  router?.push('/precedent/' + id)
}
const renderHtml = (item: CaseItem) => {
  const text = item.caseJudgetType ? item.data[item.caseJudgetType] : item.data['court_believe_words']
  return text
}

class TabObj {
  title: string = ''
  count: number = 0
  type: string = ''
  items: any[] = []
  constructor(title: string, count: number, type: string) {
    this.title = title
    this.count = count
    this.type = type
  }
}

const tabs = ref<TabObj[]>([])

const getData = (data: Cache) => {
  tabs.value = []
  if (data.has(AboutContentEnum.WEB)) {
    const webs = data.get(AboutContentEnum.WEB)
    const tmp: any[] = []
    if (Array.isArray(webs)) {
      webs.forEach((item) => {
        item.forEach((web: WebItem) => {
          tmp.push(web)
        })
      })
    }
    const tab = new TabObj($t('网页'), tmp.length, 'web')
    tab.items = tmp
    tabs.value.push(tab)
  }
  if (data.has(AboutContentEnum.CASES)) {
    const cases = data.get(AboutContentEnum.CASES)
    const tmp: any[] = []
    if (Array.isArray(cases)) {
      cases.forEach((item) => {
        item.forEach((data: any) => {
          const {
            court,
            title,
            uniqid,
            format_paragraphs: {
              judge_result: { court_ascertained_words, court_believe_words, judge_result_words },
            },
          } = data

          tmp.push({
            type: 'cases',
            court,
            title,
            uniqid,
            data: { court_ascertained_words, court_believe_words, judge_result_words },
            caseJudgetType: 'court_believe_words',
          })
        })
      })
    }
    const tab = new TabObj($t('案例'), tmp.length, 'cases')
    tab.items = tmp
    tabs.value.push(tab)
  }
  if (data.has(AboutContentEnum.LAW)) {
    const laws = data.get(AboutContentEnum.LAW)
    const tmp: any[] = []
    if (Array.isArray(laws)) {
      laws.forEach((item) => {
        item.forEach((law: any) => {
          tmp.push(law)
        })
      })
    }
    const result = uniqBy(tmp, (item) => `${item.lawName}-${item.lawNum}`)
    const tab = new TabObj($t('法规'), result.length, 'law')
    tab.items = result
    tabs.value.push(tab)
  }
  if (data.has(AboutContentEnum.ARTICLE)) {
    const arts = data.get(AboutContentEnum.ARTICLE)
    const tmp: any[] = []
    if (Array.isArray(arts)) {
      arts.forEach((item) => {
        item.forEach((art: any) => {
          tmp.push(art)
        })
      })
    }
    const tab = new TabObj($t('实务文章'), tmp.length, 'article')
    tab.items = tmp
    tabs.value.push(tab)
  }
  if (data.has(AboutContentEnum.KNOWLEDGE)) {
    const arts = data.get(AboutContentEnum.KNOWLEDGE)
    const tmp: any[] = []
    if (Array.isArray(arts)) {
      arts.forEach((item) => {
        item.forEach((art: any) => {
          tmp.push(art)
        })
      })
    }
    const tab = new TabObj($t('知识库'), tmp.length, 'knowledge')
    tab.items = tmp
    tabs.value.push(tab)
  }
}

defineExpose({
  setData(cache: Cache, artId: string) {
    getData(cache)
    if (tabs.value.length) {
      selectTab(0, tabs.value[0])
    }
    if (artId) {
      const idx = artId.split('/')
      if (Array.isArray(idx)) {
        let type = idx[0] == 'case' ? 'cases' : idx[0]
        type = type == 'data' ? 'knowledge' : type
        tabs.value.forEach((tab, index) => {
          if (tab.type == type) {
            selectTab(index, tab)
            setTimeout(() => {
              scrollToDiv(idx[1])
            }, 200)
          }
        })
      }
    }
  },
})

function scrollToDiv(idx: string) {
  let index = parseInt(idx) - 1
  if (index < 0) {
    index = 0
  }
  const scrollDiv = document.querySelector('.references-container')?.querySelector('.simplebar-content-wrapper')
  if (!scrollDiv) return
  let height = 0
  const items = scrollDiv.querySelectorAll('.article-body')
  const arts = scrollDiv.querySelectorAll('.art-box')
  if (arts) {
    for (let i = 0; i < arts.length; i++) {
      if (i == index) {
        arts[i].firstElementChild!.classList.add('article-focus')
        setTimeout(() => {
          arts[i].firstElementChild!.classList.remove('article-focus')
        }, 3000)
      } else {
        arts[i].firstElementChild!.classList.remove('article-focus')
      }
    }
  }
  if (items) {
    for (let i = 0; i < items.length; i++) {
      if (i < index) {
        height += parseFloat(getComputedStyle(items[i]).height || '0')
      }
    }
  }

  scrollDiv.scrollTo({ top: height, behavior: 'smooth' })
}

onMounted(() => {})
onUnmounted(() => {})
</script>
<style lang="scss" scoped>
.references-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: calc(100vh - 7rem);
  padding: 1rem;
  padding-bottom: 0;
  border: 1px solid var(--is-color-d0d1dc);
  border-radius: 0.75rem;
  .references-container-header {
    display: flex;
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.375rem;
    color: var(--minor-font);
  }
  .side-console-body {
    padding: 1rem 0;
  }
  .references-tabs {
    position: relative;
    display: flex;
    height: 2.5rem;
    padding: 0.25rem;
    margin-top: 1rem;
    background-color: #f9f9fb;
    border-radius: 0.5rem;
  }
  .references-tab {
    z-index: 999;
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    font-weight: 400;
    color: rgb(24 29 37 / 50%);
    cursor: pointer;
    transition: color 0.3s;
  }
  .active {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--main-font);
  }
  .tab-slider {
    position: absolute;
    z-index: 2;
    height: 2rem;
    background-color: var(--bg-color);
    border-radius: 0.25rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}
.article-header {
  display: inline-flex;
  .label {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 1.25rem;
    margin-right: 0.5rem;
    font-size: 0.75rem;
    font-weight: 400;
    color: var(--main-font);
    background: var(--primary-btn-bg);
    border-radius: 0.25rem;
  }
  .title {
    max-width: 15rem;
    overflow: hidden; /* 隐藏溢出内容 */
    text-overflow: ellipsis; /* 显示省略号 */
    font-size: 0.875rem;
    font-weight: 600;
    color: rgb(0 0 0 / 88%);
    white-space: nowrap; /* 禁止换行 */
  }
}
.article-body {
  display: -webkit-box;
  max-height: 24.25rem;
  padding: 0 8px;
  padding-top: 0.75rem;
  overflow: hidden;
  -webkit-line-clamp: 17; /* 行数 = 400px ÷ 行高 */
  line-clamp: 17; /* 行数 = 400px ÷ 行高 */
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.375rem;
  color: rgb(0 0 0 / 80%);
  letter-spacing: 1%;
  word-wrap: break-word;
  -webkit-box-orient: vertical;
}
.article-time {
  display: flex;
  align-items: center;
  padding-top: 0.75rem;
  font-size: 0.75rem;
  font-weight: 400;
  color: rgb(0 2 26 / 80%);
  > div {
    padding: 0 2px;
  }
}
.article-link {
  display: flex;
  justify-content: end;
  padding-top: 0.75rem;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.25rem;
  color: rgb(0 0 0 / 88%);
  letter-spacing: 0%;
}
.line {
  width: 100%;
  height: 1px;
  padding-bottom: 0.75rem;
  border-bottom: 1px dashed rgb(225 229 234 / 100%);
}
.article-focus {
  padding: 6px 0;
  background: rgb(244 245 255 / 100%);
  border-radius: 6px;
  transition: background-color 0.5s ease;
}
.side-console-case-tabs {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 400;
  color: rgb(0 2 26 / 90%);
  .case-tab {
    --case-tab-active-color: rgb(0 2 26 / 90%) text-align: center;

    cursor: pointer;
    .case-text {
      padding: 6px 12px;
      color: var(--case-tab-active-color);
    }
    .case-bar {
      width: 50%;
      height: 2px;
      margin: 0 auto;
      background: var(--case-tab-active-color);
      border-radius: 8px;
    }
  }
  .case-tab.active {
    --case-tab-active-color: var(--main-font);
  }
}
:deep(.simplebar-vertical) {
  width: 10px;
}
:deep(.simplebar-scrollbar::before) {
  background-color: #0003;
  border-radius: 10px;
}
</style>
