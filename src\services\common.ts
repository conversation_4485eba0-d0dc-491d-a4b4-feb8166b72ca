import request, { download, get } from '@/services'
import type { IResponse } from '@/services' // 预览

export function downloadPdfUrl<T>(url: string): Promise<IResponse<T>> {
  return get<T>(url, {}, { responseType: 'blob' })
}

// 下载
interface IDownloadStreamParams {
  fileCode: string
  fileName: string
  fileCodes?: string[]
}

export function downloadStream<T>(data: IDownloadStreamParams): Promise<IResponse<T>> {
  return download<T>(`/attachment/download`, data)
}

// 下载合同
export function downloadContract<T>(id: string): Promise<IResponse<T>> {
  return download<T>(`/llm/llm-contract/download/${id}`)
}
