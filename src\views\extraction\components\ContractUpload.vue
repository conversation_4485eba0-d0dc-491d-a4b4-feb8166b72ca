<script setup lang="ts">
import type { UploadProps, UploadInstance } from 'element-plus'
import { postUpload } from '@/services/contract'
import type { ExtractionCantract } from '../types'
import { getBlobFromFormData } from '@/utils'
import { $t } from '@/utils/i18n'

const emits = defineEmits<{
  (e: 'uploadFile', val: ExtractionCantract): void
}>()

const fileTypeOptions = ['doc', 'docx', 'pdf']
const acceptFileType = fileTypeOptions.map((f) => `.${f}`).join(', ')
const refUpload = ref<UploadInstance>()
const maxFile = ref(100)
const loading = ref(false)
const isDocFile = ref(true)

const uploadOriginFile: UploadProps['onChange'] = (file: any) => {
  // 加载初始文件
  const fileContent = file.raw
  let fileSuffix = fileContent.name.substring(fileContent.name.lastIndexOf('.') + 1)
  fileSuffix = fileSuffix.toLowerCase()
  if (!fileTypeOptions.includes(fileSuffix)) {
    ElMessage.error($t('格式错误'))
    return refUpload.value!.clearFiles()
  }
  if (fileContent.size >= maxFile.value * 1024 * 1024) {
    ElMessage.error(`文件大小不能超过${maxFile.value}M`)
    return refUpload.value!.clearFiles()
  }

  isDocFile.value = ['docx', 'doc'].includes(fileSuffix)
  loading.value = true

  const formData = new FormData()
  formData.append('file', fileContent)
  formData.append('suffix', 'html')
  getUpload(formData, isDocFile.value)
}

const getUpload = async (file: FormData, isDocFile: boolean) => {
  const blob = getBlobFromFormData(file)
  try {
    const { data = [] } = await postUpload(file, {})
    if (Array.isArray(data)) {
      const { templateUrl: contractUrl, templateName: contractName } = data[0]
      const contractInfo: ExtractionCantract = { contractName, contractUrl, isDocFile, fileBlob: blob }
      emits('uploadFile', contractInfo)
    }
  } catch (error) {
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <div class="upload-wrap">
    <el-upload
      ref="refUpload"
      v-loading="loading"
      class="upload-origin"
      action="#"
      drag
      :auto-upload="false"
      :show-file-list="false"
      :accept="acceptFileType"
      :on-change="uploadOriginFile"
    >
      <div class="icon-upload"></div>
      <div class="txt-tips">{{ $t('点击或将文件拖拽到这里上传') }}</div>
      <div class="el-upload__text">{{ $t('支持') }}{{ acceptFileType }}{{ $t('，文件≤') }}{{ maxFile }}M</div>
      <div class="el-upload__text">{{ $t('注意：不能直接修改后缀名.doc为.docx,否则会出错') }}</div>
    </el-upload>
  </div>
</template>

<style lang="scss" scoped>
.page-left {
  // 抽取页面上传模块自适应（不会影响上传弹窗的样式）
  @media screen and (width <= 1356px) {
    .upload-wrap {
      width: 0 !important;
      min-width: 504px !important;
    }
  }

  @media screen and (width >= 1440px) {
    .upload-wrap {
      max-width: 1020px;
    }
  }
  .upload-wrap {
    width: calc(100% - 176px);
    padding-top: 48px;
  }
}
.upload-wrap {
  display: flex;
  justify-content: center;
  width: 100%;
  background-color: #fff;
  .upload-origin {
    width: 100%;
    .txt-tips {
      font-size: 16px;
      font-weight: 500;
      color: #595959;
    }
    .el-upload__text {
      margin-top: 12px;
      font-size: 12px;
      color: #929292;
    }
    :deep(.el-upload--text) {
      width: 100%;
      height: 100%;
    }
    :deep(.el-upload-dragger) {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      padding: 90px 0;

      // 抽取图标
      .icon-upload {
        width: 90px;
        height: 90px;
        margin-bottom: 16px;
        background: url('@/assets/images/upload_extract.png') no-repeat center center / 100%;
      }

      // 上传文件之后的图标
    }
    :deep(.el-upload-dragger:hover) {
      background-color: var(--primary-btn-bg);
      border: 1px dashed var(--main-bg);
    }
  }
}
</style>
