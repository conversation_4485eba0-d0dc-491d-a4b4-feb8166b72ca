<script setup lang="ts">
import Sidebar from './components/Sidebar.vue'
</script>

<template>
  <div class="manage-wrapper">
    <Sidebar />
    <div class="manage-main">
      <router-view />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.manage-wrapper {
  display: flex;
  height: 100vh;
  .manage-main {
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: hidden;
  }
}
</style>
