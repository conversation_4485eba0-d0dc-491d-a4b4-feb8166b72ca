<script setup lang="ts">
import { pdfPureViewer } from '@iterms/pdf-viewer'
import { BASE_API } from '@/utils/config'
import { PDF_BASE_URL } from '@/utils/config'
import { querySemanticResult } from '@/services/textComparison'
import { downloadPdfUrl } from '@/services/common'
import ViewHeader from './ViewHeader.vue'
import { useRoute } from 'vue-router'
import type { ISemanticsResponse } from '@/services/textComparison'
import type { IDiff } from './type'

import { $t } from '@/utils/i18n'
const orginBlob = ref<Blob>()
const targetBlob = ref<Blob>()
const orginName = ref<string>('')
const targetName = ref<string>('')

const refOrigin = ref<InstanceType<typeof pdfPureViewer>>()
const refTarget = ref<InstanceType<typeof pdfPureViewer>>()

const hasDiffList = ref<IDiff[]>([])
const noDiffList = ref<IDiff[]>([])
const tabActive = ref('has')
const progress = ref(0)
const list = computed(() => {
  return tabActive.value == 'has' ? hasDiffList.value : noDiffList.value
})
const activeIdx = ref(-1)

const route = useRoute()
const id = route.query.compareId as string

let startTimer: number | null
onBeforeUnmount(() => {
  startTimer && clearInterval(startTimer)
})

async function convertPdfUrl() {
  try {
    const data = await startCompare()

    orginName.value = data.leftFilename
    targetName.value = data.rightFilename
    const list =
      data.compareResult?.diff?.map((item) => ({
        origin: item.leftContent,
        target: item.rightContent,
        content: item.diffContent,
        similarity: item.semanticSimilarity,
      })) ?? []
    hasDiffList.value = list.filter((item) => item.similarity !== '100%')
    noDiffList.value = list.filter((item) => item.similarity === '100%')

    const leftUrl = data.compareResult?.leftPdfFilePath.split(BASE_API)[1]
    const rightUrl = data.compareResult?.rightPdfFilePath.split(BASE_API)[1]
    const { data: leftData } = await downloadPdfUrl<any>(leftUrl as string)
    const { data: rightData } = await downloadPdfUrl<any>(rightUrl as string)
    orginBlob.value = new Blob([leftData], {
      type: 'application/pdf',
    })
    targetBlob.value = new Blob([rightData], {
      type: 'application/pdf',
    })
  } catch (error) {
    console.warn(error)
  } finally {
    progress.value = 100
  }
}
convertPdfUrl()

async function startCompare(): Promise<ISemanticsResponse> {
  let startResolve: (value: ISemanticsResponse | PromiseLike<ISemanticsResponse>) => void
  let startReject: () => void

  const start = async function () {
    const { data } = await querySemanticResult<ISemanticsResponse>({
      id,
    })
    // const { data } = res
    progress.value = data.progressNum
    if (data.progressNum === 100) {
      startTimer && clearInterval(startTimer)
      startResolve(data)
    } else if (data.progressNum === -1) {
      startTimer && clearInterval(startTimer)
      ElMessage.error($t('语义对比失败'))
      startReject()
    }
  }

  startTimer = window.setInterval(start, 1000)

  return new Promise((resolve, reject) => {
    startResolve = resolve
    startReject = reject
    start()
  })
}

function changeTab() {
  activeIdx.value = -1
  refOrigin.value?.findTextNext('')
  refTarget.value?.findTextNext('')
}

function handleSearch(item: IDiff, idx: number) {
  activeIdx.value = idx
  const origin = item.origin.slice(0, 5)
  const target = item.target.slice(0, 5)

  refOrigin.value?.findTextNext(origin)
  refTarget.value?.findTextNext(target)
}
</script>

<template>
  <div v-loading="progress != 100" class="page-wrap" :element-loading-text="`${progress}%`">
    <div class="content">
      <div class="left">
        <ViewHeader :header-obj="{ isOrigin: true, filename: orginName }" />
        <pdfPureViewer v-if="orginBlob" ref="refOrigin" :file="orginBlob" :base-path="PDF_BASE_URL" :key="'aaa'" />
      </div>
      <div class="right">
        <ViewHeader :header-obj="{ isOrigin: false, filename: targetName }" />
        <pdfPureViewer v-if="targetBlob" ref="refTarget" :file="targetBlob" :base-path="PDF_BASE_URL" :key="'bbb'" />
      </div>
    </div>

    <div class="diff-wrap">
      <el-tabs v-model="tabActive" @tab-click="changeTab">
        <el-tab-pane name="has">
          <template v-slot:label>
            <span class="tab-item"
              >{{ $t('有差异')
              }}<el-badge
                v-if="hasDiffList.length > 0"
                :value="hasDiffList.length"
                :max="99"
                type="primary"
                class="tab-item-badge"
              ></el-badge>
            </span>
          </template>
        </el-tab-pane>
        <el-tab-pane name="no">
          <template v-slot:label>
            <span class="tab-item"
              >{{ $t('无差异')
              }}<el-badge
                v-if="noDiffList.length > 0"
                :value="noDiffList.length"
                :max="99"
                type="primary"
                class="tab-item-badge"
              >
              </el-badge>
            </span>
          </template>
        </el-tab-pane>
      </el-tabs>

      <div v-if="list.length" ref="refDiffList" class="diff-list">
        <div
          v-for="(item, id) in list"
          :key="id"
          class="diff-list-item"
          :style="{ '--angel': tabActive === 'has' ? ' #f85951' : '#1fac78' }"
          :class="{ 'item-active': activeIdx == id }"
          @click="handleSearch(item, id)"
        >
          <!-- <div style="margin-bottom: 8px"><span class="item-sp">{{ $t('相似度：') }}</span>{{ item.similarity }}</div> -->
          <div class="diff-item">
            <span class="diff-item-title">{{ $t('原文1：') }}</span>
            <pre v-html="item.origin"></pre>
          </div>
          <div class="diff-item">
            <span class="diff-item-title">{{ $t('原文2：') }}</span>
            <pre v-html="item.target"></pre>
          </div>
          <div class="diff-item">
            <span class="diff-item-title">{{ $t('语义影响：') }}</span>
            <pre v-html="item.content"></pre>
          </div>
        </div>
      </div>
      <el-empty v-else :description="$t('暂无数据')"></el-empty>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.page-wrap {
  display: flex;
  .content {
    display: flex;
    flex: 1;
    .left {
      display: flex;
      flex-direction: column;
      width: 50%;
      height: 100vh;
    }
    .right {
      display: flex;
      flex-direction: column;
      width: 50%;
      height: 100vh;
    }
  }
  .list {
    flex-shrink: 0;
    width: 300px;
    height: 100vh;
    padding: 12px;
    overflow-y: auto;
    .box-card {
      width: 100%;
      min-height: 60px;
      padding: 8px;
      margin-bottom: 8px;
      background-color: var(--bg-color);
      border-radius: 4px;
    }
    .card-active {
      border: 1px solid #276ef9;
    }
  }
}
.diff-wrap {
  position: relative;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 300px;
  height: 100vh;
  padding: 15px 0 15px 15px;
  padding-bottom: 40px;
  background: var(--bg-color);
  border-left: 1px solid #e9e9e9;
  transition: width 0.2s;
  :deep(.el-tabs) {
    width: 270px;
    margin-left: 0;
  }
  :deep(.el-tabs__nav-wrap) {
    &::after {
      width: 97%;
    }
  }
  .tab-item {
    position: relative;
    &-badge {
      position: absolute;
      z-index: 2;
    }
  }
  .diff-list {
    flex: 1;
    width: 100%;
    overflow-y: auto;
    .diff-list-item {
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: center;
      width: calc(100% - 12px);
      min-height: 84px;
      padding: 10px 0 10px 10px;
      margin-top: 15px;
      overflow: hidden;
      color: #303133;
      cursor: pointer;
      border: 1px solid #e9e9e9;
      border-radius: 4px;
      &:hover {
        border: 1px solid #276ef9;
      }
      &::before {
        position: absolute;
        top: -10px;
        left: -10px;
        z-index: 1;
        content: '';
        border-top: 10px solid var(--angel);
        border-right: 10px solid transparent;
        border-bottom: 10px solid transparent;
        border-left: 10px solid transparent;
        transform: rotate(135deg);
      }
    }
    .item-active {
      border: 1px solid #276ef9 !important;
    }
    .diff-item {
      // display: flex;
      margin-bottom: 12px;
      &-title {
        white-space: nowrap;
      }
    }
  }
}
pre {
  display: inline;
  word-break: break-all;
  white-space: pre-wrap;
}
</style>
