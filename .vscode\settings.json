{"editor.wordWrap": "on", "eslint.format.enable": true, "editor.formatOnSave": true, "[json]": {"editor.defaultFormatter": "vscode.json-language-features"}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.defaultFormatter": "dbaeumer.vscode-eslint", "stylelint.validate": ["css", "scss", "postcss", "vue"], "explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"tsconfig.json": "tsconfig.*.json, env.d.ts", "vite.config.*": "jsconfig*, vitest.config.*, cypress.config.*, playwright.config.*", "package.json": "package-lock.json, pnpm*, .yarnrc*, yarn*, .eslint*, eslint*, .oxlint*, oxlint*, .prettier*, prettier*, .editorconfig, .stylelint*, .commitlintrc*, .npmrc, .gitignore", "components.d.ts": "*.d.ts", ".env.production": ".env.production,.env.pro.production, .env.development, .env.pro.development, .env.local.development,"}, "editor.codeActionsOnSave": {"source.fixAll.stylelint": "explicit", "source.fixAll.eslint": "explicit"}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}