import LayoutManage from '@/viewsAdmin/layoutManage/index.vue'
import type { RouteRecordRaw } from 'vue-router'

const router: RouteRecordRaw[] = [
  {
    path: '/manage',
    component: LayoutManage,
    children: [
      // 审查规则库
      // {
      //   path: '',
      //   name: 'RuleList',
      //   component: () => import(/* webpackChunkName: "RuleList" */ '@/viewsAdmin/manage/ruleList'),
      // },
    ],
  },
  {
    path: '/role',
    component: LayoutManage,
    children: [
      // 角色管理
      {
        path: '',
        name: 'RoleManage',
        meta: {
          permission: 'corpManage',
        },
        component: () => import(/* webpackChunkName: "RoleManage" */ '@/viewsAdmin/manage/role/index.vue'),
      },
    ],
  },
  {
    path: '/organization',
    component: LayoutManage,
    children: [
      // 组织管理
      {
        path: '',
        name: 'Organization',
        meta: {
          permission: 'corpManage',
        },
        component: () => import(/* webpackChunkName: "Organization" */ '@/viewsAdmin/manage/organization/index.vue'),
      },
    ],
  },
]
export default router
