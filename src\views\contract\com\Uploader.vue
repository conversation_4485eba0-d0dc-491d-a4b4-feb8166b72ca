<script lang="ts" setup>
import { ref } from 'vue'
import type { UploadProps, UploadInstance } from 'element-plus'
import { createContact, generateContratSummary, postUpload } from '@/services/contract'
import router from '@/router'
import { $t } from '@/utils/i18n'
const uploaderRef = ref<UploadInstance>()
const loading = ref(false)
const maxSize = ref(100)
const isDoc = ref(true)

const fileTypeOptions = ['doc', 'docx', 'pdf']
const acceptFileType = fileTypeOptions.map((f) => `.${f}`).join(', ')
const onChange: UploadProps['onChange'] = async (file: any) => {
  const fileContent = file.raw
  let fileSuffix = fileContent.name.substring(fileContent.name.lastIndexOf('.') + 1)
  fileSuffix = fileSuffix.toLowerCase()
  if (!fileTypeOptions.includes(fileSuffix)) {
    ElMessage.error($t('格式错误'))
    return uploaderRef.value!.clearFiles()
  }
  if (fileContent.size >= maxSize.value * 1024 * 1024) {
    ElMessage.error(`${$t('文件大小不能超过')}${maxSize.value}M`)
    return uploaderRef.value!.clearFiles()
  }
  // 根据文件类型显示不同的图片

  // imgUrl.value = imagesList[fileSuffix];

  isDoc.value = ['docx', 'doc'].includes(fileSuffix)
  loading.value = true

  const formData = new FormData()
  formData.append('file', fileContent)
  formData.append('suffix', 'html')
  startUpload(formData, isDoc.value)
}
const create = async (fileCode: string, fileName: string) => {
  const { code, data, message } = await createContact({ fileCode, fileName })
  if ('000000' === code || '200' === code) {
    const { contractId } = data as { contractId: string }

    router.push('/contract/view/1/' + contractId).then(async () => {
      await generateContratSummary(fileCode)
    })
  } else {
    ElMessage.error(message)
  }
}

const startUpload = async (formData: FormData, isDoc: boolean) => {
  try {
    const { data } = await postUpload(formData, {})

    if (Array.isArray(data)) {
      const { templateUrl: contractUrl, templateName: contractName } = data[0]

      create(contractUrl, contractName)
      // router.push('/contract/view/');
    }
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}
</script>
<template>
  <el-upload
    ref="uploaderRef"
    v-loading="loading"
    action="#"
    :auto-upload="false"
    :show-file-list="false"
    :accept="acceptFileType"
    :on-change="onChange"
  >
    <div>
      <slot></slot>
    </div>
  </el-upload>
</template>
<style scoped lang="scss">
.upload-origin {
  // width: calc(100% - 200px);
  width: 100%;
  height: 340px;
  .txt-tips {
    font-size: 16px;
    font-weight: 500;
    color: #595959;
  }
  .el-upload__text {
    margin-top: 12px;
    font-size: 12px;
    color: #929292;
  }
  :deep(.el-upload--text) {
    width: 100%;
    height: 100%;
  }
  :deep(.el-upload-dragger) {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    padding: 90px 0;
    .icon-upload {
      display: block;
      width: 90px;
      height: 90px;
      margin-bottom: 16px;
    }
    .el-button {
      width: 212px;
      height: 40px;
      margin-bottom: 16px;
      border-radius: 4px;
    }
  }
  :deep(.el-upload-dragger:hover) {
    background-color: var(--primary-btn-bg);
    border: 1px dashed var(--is-color-773bef);
  }
}
:deep(.el-loading-spinner) {
  top: 0;
}
</style>
