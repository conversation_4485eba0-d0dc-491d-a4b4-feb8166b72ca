<template>
  <div class="tool-bar">
    <el-tooltip effect="dark" :content="$t('复制')" placement="bottom">
      <div class="tool-bar-item" @click="copy">
        <i class="iconfont" style="font-size: 1rem">&#xe67e;</i>
      </div>
    </el-tooltip>
    <div class="tool-bar-spec">
      <div class="tool-bar-spec-line"></div>
    </div>
    <el-tooltip effect="dark" :content="(like ? $t('取消') : '') + $t('点赞')" placement="bottom">
      <div class="tool-bar-item" @click="likeContent">
        <i v-if="like" class="iconfont" style="font-size: 1rem; color: var(--is-color-773bef)">&#xe681;</i>
        <i v-else class="iconfont" style="font-size: 1rem">&#xe667;</i>
      </div>
    </el-tooltip>
    <el-tooltip effect="dark" :content="(dislike ? $t('取消') : '') + $t('点踩')" placement="bottom">
      <div class="tool-bar-item" @click="dislikeContent">
        <i v-if="dislike" class="iconfont" style="font-size: 1rem; color: var(--is-color-773bef)">&#xe682;</i>
        <i v-else class="iconfont" style="font-size: 1rem">&#xe665;</i>
      </div>
    </el-tooltip>
    <el-dialog
      v-model="dialogVisible"
      :title="$t('你觉得哪些方面令你不满意？')"
      width="500"
      :show-close="false"
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="feedback-types-bar" style="display: flex; gap: 0.5rem">
        <div
          v-for="item in types"
          :key="item.key"
          class="feedback-types-bar-item"
          :class="currentType.key == item.key ? 'active' : ''"
          @click="selectType(item)"
        >
          {{ item.label }}
        </div>
      </div>
      <div v-if="currentType.key == 4" style="margin-top: 0.5rem">
        <el-input v-model="feedbackContent" type="textarea" :rows="5" :placeholder="$t('请输入反馈内容')" />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog(true)">{{ $t('取消') }}</el-button>
          <el-button type="primary" @click="submitFeedback">
            {{ $t('提交') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { ChatMessage } from '@/stores/modules/chat/helper'
import { copyToClipboard } from '@/utils'
import { ElMessage } from 'element-plus'
import { onUnmounted, ref, watch } from 'vue'
import { RESPONSE_CODE_SUCCESS } from '@/constants'
import ChatApi from '@/services/chat'
import type { feedbackParams } from '@/types/chat'
import { $t } from '@/utils/i18n'

const props = defineProps({
  chatId: {
    type: String,
    required: true,
  },
  chatMessage: {
    type: ChatMessage,
    required: true,
  },
})
const feedbackContent = ref('')
const dialogVisible = ref(false)
const emits = defineEmits(['edit'])
const like = ref(false)
const dislike = ref(false)
const types = ref([
  { key: 1, label: $t('没理解问题') },
  { key: 2, label: $t('编造信息') },
  { key: 3, label: $t('没有帮助') },
  { key: 4, label: $t('其他') },
])
const currentType = ref({ key: 0, label: '' })
const selectType = (_type: { key: number; label: string }) => {
  currentType.value = _type
}
const copy = () => {
  const { copyContent } = props.chatMessage

  copyToClipboard(copyContent.cleanText)
  ElMessage.success($t('复制成功！'))
}
const likeContent = () => {
  like.value = !like.value
  if (like.value) {
    dislike.value = false
  }
  saveFeedback({ rating: like.value ? 'like' : null })
}
const dislikeContent = () => {
  dislike.value = !dislike.value
  if (dislike.value) {
    like.value = false
    dialogVisible.value = true
    feedbackContent.value = ''
  } else {
    like.value = false
    saveFeedback({ rating: null })
  }
}
const submitFeedback = async () => {
  if (currentType.value.key == 0) {
    ElMessage.warning($t('请选择反馈类型'))
    return
  }
  if (currentType.value.key === 4) {
    if (feedbackContent.value.trim() === '') {
      ElMessage.warning($t('请输入反馈内容！'))
      return
    }
  }

  saveFeedback({ rating: 'dislike', type: currentType.value.label, content: feedbackContent.value })
}
const closeDialog = (flag: boolean) => {
  dialogVisible.value = false
  if (flag) {
    saveFeedback({ rating: 'dislike' })
  }
}

const saveFeedback = async (feed: feedbackParams) => {
  const { chatId } = props
  const { code, message } = await ChatApi.feedback({ chatId, messageId: props.chatMessage.messageId, feedback: feed })
  if (code === RESPONSE_CODE_SUCCESS) {
    closeDialog(false)
  } else {
    ElMessage.error(message)
  }
}

const watchFeedback = watch(
  () => props.chatMessage.feedback,
  (fb) => {
    if (fb) {
      if (fb.rating === 'like') {
        like.value = true
        dislike.value = false
      } else if (fb.rating === 'dislike') {
        dislike.value = true
        like.value = false
      } else {
        dislike.value = false
        like.value = false
      }
    }
  },
  {
    immediate: true,
    deep: true,
  },
)
onUnmounted(() => {
  watchFeedback.stop()
})
</script>
<style lang="scss" scoped>
.tool-bar {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  width: 100%;
  height: 36px;
  line-height: 36px;
  &-spec {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 0.5rem;
    height: 2rem;
    &-line {
      width: 2px;
      height: 1rem;
      border-left: 1px solid var(--page-header-line);
    }
  }
  &-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    cursor: pointer;
    &:hover {
      background-color: rgb(238 240 244);
      border-radius: 0.5rem;
    }
    &-line {
      width: 2px;
      height: 1rem;
      border-left: 1px solid var(--page-header-line);
    }
  }
}
.feedback-types-bar {
  .active {
    color: var(--main-font);
    border: 1px solid var(--main-font);
  }
  &-item {
    &:hover {
      color: var(--main-font);
      border: 1px solid var(--main-font);
    }

    display: flex;
    align-items: center;
    justify-content: center;
    width: 5.5rem;
    height: 2.5rem;
    font-size: 0.85rem;
    font-weight: 500;
    color: var(--minor-font);
    cursor: pointer;
    border: 1px solid #d9d9d9;
    border-radius: 0.25rem;
  }
}
</style>
