/**
 * 简单的缓存类，用于在内存中存储键值对
 */
export class Cache {
  store: Map<string, any>

  constructor() {
    this.store = new Map()
  }

  /**
   * 检查缓存中是否存在指定的键
   * @param key 缓存键
   * @returns 是否存在
   */
  has(key: string): boolean {
    return this.store.has(key)
  }

  /**
   * 获取缓存值
   * @param key 缓存键
   * @returns 缓存值
   */
  get(key: string): any {
    return this.store.get(key)
  }

  /**
   * 设置缓存值
   * @param key 缓存键
   * @param value 缓存值
   */
  put(key: string, value: any): void {
    this.store.set(key, value)
  }

  /**
   * 删除缓存项
   * @param key 缓存键
   * @returns 是否删除成功
   */
  remove(key: string): boolean {
    return this.store.delete(key)
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    this.store.clear()
  }

  /**
   * 获取缓存大小
   * @returns 缓存项数量
   */
  size(): number {
    return this.store.size
  }

  /**
   * 获取所有缓存键
   * @returns 所有键的数组
   */
  keys(): string[] {
    return Array.from(this.store.keys())
  }

  /**
   * 获取所有缓存值
   * @returns 所有值的数组
   */
  values(): any[] {
    return Array.from(this.store.values())
  }
}
