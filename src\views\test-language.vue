<template>
  <div class="language-test-page">
    <h1>{{ $t('语言切换测试') }}</h1>

    <div class="test-content">
      <p>{{ $t('当前语言') }}: {{ getCurrentLanguageInfo.label }}</p>
      <p>{{ $t('语言代码') }}: {{ currentLanguage }}</p>

      <div class="language-switcher">
        <h3>{{ $t('选择语言') }}:</h3>
        <div class="language-buttons">
          <button
            v-for="option in languageOptions"
            :key="option.value"
            :class="['lang-btn', { active: currentLanguage === option.value }]"
            @click="switchLanguage(option.value)"
          >
            {{ option.label }} ({{ option.shortLabel }})
          </button>
        </div>
      </div>

      <div class="test-translations">
        <h3>{{ $t('测试翻译') }}:</h3>
        <ul>
          <li>{{ $t('个人中心') }}</li>
          <li>{{ $t('退出登录') }}</li>
          <li>{{ $t('确定') }}</li>
          <li>{{ $t('取消') }}</li>
          <li>{{ $t('语言') }}</li>
          <li>{{ $t('操作成功') }}</li>
          <li>{{ $t('操作失败') }}</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useLanguage } from '@/composables/useLanguage'
import { $t } from '@/utils/i18n'

const { currentLanguage, languageOptions, switchLanguage, getCurrentLanguageInfo } = useLanguage()
</script>

<style scoped>
.language-test-page {
  max-width: 800px;
  padding: 20px;
  margin: 0 auto;
}
.test-content {
  margin-top: 20px;
}
.language-switcher {
  padding: 20px;
  margin: 20px 0;
  border: 1px solid #ddd;
  border-radius: 8px;
}
.language-buttons {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}
.lang-btn {
  padding: 8px 16px;
  cursor: pointer;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  transition: all 0.2s ease;
}
.lang-btn:hover {
  background: #f0f0f0;
}
.lang-btn.active {
  color: white;
  background: #492ed1;
  border-color: #492ed1;
}
.test-translations {
  padding: 20px;
  margin: 20px 0;
  border: 1px solid #ddd;
  border-radius: 8px;
}
.test-translations ul {
  padding-left: 20px;
  list-style-type: disc;
}
.test-translations li {
  margin: 5px 0;
}
</style>
