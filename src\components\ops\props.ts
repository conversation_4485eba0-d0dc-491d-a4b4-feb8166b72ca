import type { PropType } from 'vue'

import type { IConfig } from './config'

function defineListenerProp<F>(fallback?: any) {
  return {
    type: [Function, Array] as PropType<F | F[]>,
    default: fallback,
  }
}

export const propsArgs = {
  id: {
    type: String,
    required: false,
    default: 'ai-2025',
  },
  documentServerUrl: {
    type: String,
    required: true,
  },
  config: {
    type: Object as PropType<IConfig>,
    required: true,
  },
  onLoadComponentError: defineListenerProp<(e: Event | object) => void | Promise<any>>(),
  onAppReady: defineListenerProp<(e: Event | object) => void | Promise<any>>(),
  onDocumentStateChange: defineListenerProp<(e: Event | object) => void | Promise<any>>(),
  onMetaChange: defineListenerProp<(e: Event | object) => void | Promise<any>>(),
  onDocumentReady: defineListenerProp<(e: Event | object) => void | Promise<any>>(),
  onInfo: defineListenerProp<(e: Event | object) => void | Promise<any>>(),
  onWarning: defineListenerProp<(e: Event | object) => void | Promise<any>>(),
  onOutdatedVersion: defineListenerProp<(e: Event | object) => void | Promise<any>>(),
  onError: defineListenerProp<(e: Event | object) => void | Promise<any>>(),
  onRequestSharingSettings: defineListenerProp<(e: Event | object) => void | Promise<any>>(),
  onRequestRename: defineListenerProp<(e: Event | object) => void | Promise<any>>(),
  onMakeActionLink: defineListenerProp<(e: Event | object) => void | Promise<any>>(),
  onRequestInsertImage: defineListenerProp<(e: Event | object) => void | Promise<any>>(),
  onRequestSaveAs: defineListenerProp<(e: Event | object) => void | Promise<any>>(),
  onRequestMailMergeRecipients: defineListenerProp<(e: Event | object) => void | Promise<any>>(),
  onRequestCompareFile: defineListenerProp<(e: Event | object) => void | Promise<any>>(),
  onRequestEditRights: defineListenerProp<(e: Event | object) => void | Promise<any>>(),
  onRequestHistory: defineListenerProp<(e: Event | object) => void | Promise<any>>(),
  onRequestHistoryClose: defineListenerProp<(e: Event | object) => void | Promise<any>>(),
  onRequestHistoryData: defineListenerProp<(e: Event | object) => void | Promise<any>>(),
  onRequestRestore: defineListenerProp<(e: Event | object) => void | Promise<any>>(),
  // 自定义事件
  onRequestOnlineUsers: defineListenerProp<(e: Event | object) => void | Promise<any>>(),
  onRequestSaveText: defineListenerProp<(e: Event | object) => void | Promise<any>>(),
  // onRequestComments: defineListenerProp<(e: Event | Object) => void | Promise<any>>(),
  onRequestCommentsList: defineListenerProp<(e: Event | object) => void | Promise<any>>(),
  onGetAllControls: defineListenerProp<(e: Event | object) => void | Promise<any>>(),
  onRequestShortcut: defineListenerProp<(e: Event | object) => void | Promise<any>>(),
  onRequestOpenContextMenu: defineListenerProp<(e: Event | object) => void | Promise<any>>(),
}
