<template>
  <el-dialog
    v-model="visible"
    :title="$t('合同起草')"
    width="500px"
    :close-on-click-modal="false"
    @close="closeHandler"
  >
    <div>
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="80px" label-position="top" class="form">
        <el-form-item :label="$t('合同名称')" prop="contractName">
          <el-input v-model="formData.contractName" :placeholder="$t('请输入合同名称，例:XX保密协议')" />
        </el-form-item>
        <el-form-item :label="$t('合同背景与目的')" prop="contractPurpose">
          <el-input
            v-model="formData.contractPurpose"
            :placeholder="$t('请输入起草合同的背景与目的')"
            type="textarea"
            :rows="4"
          />
        </el-form-item>
        <el-form-item :label="$t('合同立场')" prop="rulePosition">
          <el-input v-model="formData.rulePosition" :placeholder="$t('请输入合同立场')" />
        </el-form-item>
        <div>
          <div class="reference-file">
            <div>{{ $t('参考合同') }}</div>
            <div v-if="formData.referFileName === ''">
              <el-upload
                ref="uploadRef"
                v-loading="loading"
                action="#"
                :auto-upload="false"
                :show-file-list="false"
                :accept="acceptFileType"
                :on-change="uploadOriginFile"
              >
                <div class="upload-box" style="color: var(--primary-color)">
                  <i class="iconfont icon-is-plus" style="font-size: 0.75rem"></i>
                  <span class="upload-span">{{ $t('上传合同') }}</span>
                </div>
              </el-upload>
            </div>
            <div v-else>
              <div style="display: flex; align-items: center">
                <span class="refer-file-name">{{ formData.referFileName }}</span>
                <i class="iconfont icon-is-close" style="cursor: pointer" @click="removeRefer"></i>
              </div>
            </div>
          </div>
        </div>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">{{ $t('取消') }}</el-button>
        <el-button v-if="saveLoading" type="primary" :loading="saveLoading" style="width: 3.75rem"></el-button>
        <el-button v-else type="primary" @click="saveDraft"> {{ $t('下一步') }} </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { reactive, ref } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import type { UploadProps, UploadInstance } from 'element-plus'
import { draftGenerate, generateContratSummary, postUpload } from '@/services/contract'
import router from '@/router'
import { $t } from '@/utils/i18n'
const emits = defineEmits(['callback'])
const visible = ref(false)
const formRef = ref<FormInstance>()
const uploadRef = ref<UploadInstance>()
const fileTypeOptions = ['doc', 'docx']
const acceptFileType = fileTypeOptions.map((f) => `.${f}`).join(', ')
const fileMaxSize = ref(100)
const loading = ref(false)
const saveLoading = ref(false)
interface FormDataInter {
  contractName: string //标题
  contractPurpose: string //目的
  rulePosition: string //立场
  referFileId: string //参考
  referFileName: string
}

const formData = reactive<FormDataInter>({
  contractName: '',
  contractPurpose: '',
  rulePosition: '',
  referFileId: '',
  referFileName: '',
})

const rules = reactive<FormRules>({
  contractName: [
    { required: true, message: $t('请输入合同名称'), trigger: 'blur' },
    { min: 2, message: $t('长度不能小于2个字符'), trigger: 'blur' },
    { max: 20, message: $t('长度不能大于20个字符'), trigger: 'blur' },
  ],
  contractPurpose: [
    { required: true, message: $t('请输入合同背景与目的'), trigger: 'blur' },
    { min: 2, message: $t('长度不能小于2个字符'), trigger: 'blur' },
    { max: 500, message: $t('长度不能大于500个字符'), trigger: 'blur' },
  ],
  rulePosition: [
    { required: true, message: $t('请输入合同立场'), trigger: 'blur' },
    { min: 2, message: $t('长度不能小于2个字符'), trigger: 'blur' },
    { max: 7, message: $t('长度不能大于7个字符'), trigger: 'blur' },
  ],
})

const open = () => {
  visible.value = true
  formRef.value?.resetFields()
}
const closeHandler = () => {}
const close = () => {
  visible.value = false
  formRef.value?.resetFields()
}

const removeRefer = () => {
  formData.referFileId = ''
  formData.referFileName = ''
}

const saveDraft = () => {
  formRef.value?.validate(async (valid) => {
    if (valid) {
      save()
    } else {
      ElMessage.warning($t('请填写必填项'))
    }
  })
  // .catch((e) => {
  //   saveLoading.value = false
  //   console.log(e)
  // })
}
const save = async () => {
  try {
    saveLoading.value = true
    const { code, data, message } = await draftGenerate(formData)
    console.log(code, data, message)
    if ('000000' === code || '200' === code) {
      const { fileCode, contractId } = data as { fileCode: string; contractId: string }

      close()
      router.push(`/contract/view/1/` + contractId).then(async () => {
        await generateContratSummary(fileCode)
      })
    } else {
      saveLoading.value = false
      console.log(saveLoading.value)
      ElMessage.error(message)
    }
  } catch (e) {
    saveLoading.value = false
    ElMessage.error(e + '')
  }
}

const uploadOriginFile: UploadProps['onChange'] = (file) => {
  const fileContent = file.raw
  if (!fileContent) {
    ElMessage.error($t('请选择文件'))
    return
  }
  let fileSuffix = fileContent.name.substring(fileContent.name.lastIndexOf('.') + 1)
  fileSuffix = fileSuffix.toLowerCase()
  if (!fileTypeOptions.includes(fileSuffix)) {
    ElMessage.error($t('格式错误'))
    return uploadRef.value!.clearFiles()
  }
  if (fileContent.size >= fileMaxSize.value * 1024 * 1024) {
    ElMessage.error(`文件大小不能超过${fileMaxSize.value}M`)
    return uploadRef.value!.clearFiles()
  }
  // 根据文件类型显示不同的图片

  // imgUrl.value = imagesList[fileSuffix];

  loading.value = true

  const formData = new FormData()
  formData.append('file', fileContent)
  formData.append('suffix', 'html')
  uploadFile(formData)
}

const uploadFile = async (file: FormData) => {
  try {
    const { data } = await postUpload(file, {})
    if (Array.isArray(data)) {
      const { templateUrl: contractUrl, templateName: contractName } = data[0]

      formData.referFileName = contractName
      formData.referFileId = contractUrl
    }
  } catch (error) {
    ElMessage.error($t('上传失败'))
  } finally {
    loading.value = false
  }
}

defineExpose({
  open,
})
</script>
<style scoped lang="scss" scope>
:deep(.el-form-item__label) {
  font-size: 0.875rem;
  font-weight: 400;
  color: var(--minor-font);
}
.dialog-footer {
  text-align: right;
}
.upload-box {
  display: flex;
  align-items: center;
}
.upload-span {
  margin-left: 0.5rem;
  font-size: 0.75rem;
  font-weight: 400;
  color: var(--is-color-773bef);
  cursor: pointer;
}
.reference-file {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
  font-weight: 400;
  color: var(--minor-font);
}
.refer-file-name {
  width: 300px;
  margin-right: 0.5rem;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: right;
  white-space: nowrap;
}
</style>
