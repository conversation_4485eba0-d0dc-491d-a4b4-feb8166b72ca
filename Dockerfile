FROM hub.fabigbig.com/innovation/node:20.18-bullseye-slim as build-stage

WORKDIR /app

RUN npm install -g pnpm --registry https://registry.npmmirror.com/

COPY package.json pnpm-lock.yaml ./

RUN pnpm install --registry https://registry.npmmirror.com/

COPY . .

# RUN npm run build:pnpm
RUN npm run build:test

FROM hub.fabigbig.com/innovation/nginx:basic

ENV API_HOST=localhost

ENV APP=saas

ENV BASE_URL=\/pro

ENV BASE_PUBLIC_URL=\/pro

ENV BASE_API=\/pro-api

COPY --from=build-stage /app/dist/  /www/html/

COPY --from=build-stage /app/nginx/docker-entrypoint.sh  /www/

RUN chmod a+x /www/docker-entrypoint.sh

COPY --from=build-stage /app/nginx/default.conf.template  /etc/nginx/templates/

# COPY --from=build-stage /app/nginx/nginx.conf  /etc/nginx/

COPY --from=build-stage /app/nginx/mime.types  /etc/nginx/

EXPOSE 80

ENTRYPOINT ["/www/docker-entrypoint.sh"]

CMD ["nginx", "-g", "daemon off;"]
