import Layout from '@/layout/index.vue'
import type { RouteRecordRaw } from 'vue-router'

import { $t } from '@/utils/i18n'
const router: RouteRecordRaw[] = [
  {
    path: '/chat',
    component: Layout,
    children: [
      {
        path: '',
        name: 'chat',
        meta: {
          fullscreen: false,
          single: false,
          title: $t('智能问答'),
          permission: 'QA',
        },
        component: () => import(/* webpackChunkName: "contract" */ '@/views/chat/index.vue'),
      },
      {
        path: ':chatId',
        name: 'chatDetail',
        meta: {
          fullscreen: false,
          single: false,
          title: $t('智能问答详情页'),
        },
        component: () => import(/* webpackChunkName: "contract" */ '@/views/chat/conversation.vue'),
      },
      {
        path: '/history-chat',
        name: 'chatHistory',
        meta: {
          fullscreen: false,
          single: false,
          title: $t('智能问答详情页'),
        },
        component: () => import(/* webpackChunkName: "contract" */ '@/views/chat/com/historyChat.vue'),
      },
    ],
  },
]
export default router
