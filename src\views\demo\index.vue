<template>
  <div style="display: flex; width: 100vw; height: 100vh; overflow: hidden">
    <div style="width: 70vw; height: 100vh">
      <WpsOffice ref="officeRef" :file-id="fileId" :office-type="fileType" token="" class="office-frame"></WpsOffice>
    </div>
    <div style="width: 30vw; height: 100vh">
      <el-input v-model="keystring" placeholder="" />
      <el-input v-model="keystring1" placeholder="" />
      <el-button @click="find()">{{ $t('查找') }}</el-button>
      <el-button @click="replace()">{{ $t('替换') }}</el-button>
      <div></div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import WpsOffice from '@/components/wps/index.vue'
import mitt from '@/utils/eventsBus'

import { useI18n } from 'vue-i18n'

import { $t } from '@/utils/i18n'
const { t } = useI18n()
const keystring = ref(t($t('数据')))
const keystring1 = ref(t($t('问题')))
const fileId = ref('')
const fileType = ref('')
const officeRef = ref()
const find = async () => {
  const { pos } = await officeRef.value.find(keystring.value)
  console.log(pos)
  officeRef.value.location(pos)
}
const replace = async () => {
  await officeRef.value.replaceText(keystring.value, keystring1.value)
}
mitt.on('selectionChangeForDoc', (data) => {
  console.log('emm')
  console.log(data)
})
</script>
<style lang="scss" scoped>
// Added content to fix empty source error
</style>
