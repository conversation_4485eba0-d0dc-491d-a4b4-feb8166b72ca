<script setup lang="ts">
const route = useRoute()
const name = route.meta.title
const emits = defineEmits(['goto'])
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  showBack: {
    type: Boolean,
    default: true,
  },
})

const title = computed(() => props.title || name)
</script>

<template>
  <div class="header">
    <div class="header-content">
      <div v-if="showBack" class="header-arrow">
        <i class="iconfont icon-is-back iconfont" @click="emits('goto')"></i>
      </div>
      <div v-if="showBack" class="header-line"></div>
      <div class="header-title">{{ title }}</div>
    </div>
    <slot v-if="$slots.default"></slot>
  </div>
</template>

<style lang="scss" scoped>
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 2.625rem;
  padding: 0.625rem 1rem;
  font-size: 1rem;
  font-weight: bold;
  border-bottom: 1px solid var(--page-header-line);
  &-content {
    display: flex;
    align-items: center;
  }
  &-arrow {
    width: 1.25rem;
    height: 1.25rem;
    color: var(--font-color);
    cursor: pointer;
  }
  &-line {
    width: 1px;
    height: 1.2rem;
    margin: 0 0.5rem;
    border-left: 1px solid var(--page-header-line);
  }
  &-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--font-color);
  }
}
</style>
