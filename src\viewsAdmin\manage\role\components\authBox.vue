<script setup lang="ts">
import { getRoleAuthorizeByRoleCode, listMenuTree, saveRole } from '@/services/manage/index'
import type { MenuItem, MenuData, ISaveRoleParams } from '@/services/manage/index'

import { $t } from '@/utils/i18n'
interface RoleForm {
  roleCode?: string
  dataAccess?: number
  roleName?: string
  roleRemark?: string
  roleId?: string
  isEdit?: boolean
}

const props = withDefaults(defineProps<{ roleObj: RoleForm }>(), {
  roleObj: () => ({
    roleCode: '',
    dataAccess: 1,
    roleName: '',
    roleRemark: '',
    roleId: '',
    isEdit: false,
  }),
})

// { roleId: '', roleName: '', roleRemark: '', dataAccess: 1 }
const formData = ref<RoleForm>({
  roleCode: '',
  dataAccess: 1,
  roleName: '',
  roleRemark: '',
  roleId: '',
  isEdit: false,
})
const departmentOptions = [
  { code: 1, label: $t('仅自己') },
  { code: 2, label: $t('主部门'), desc: $t('仅含本级') },
  { code: 3, label: $t('主部门'), desc: $t('含下属部门') },
  { code: 4, label: $t('主部门及归属部门'), desc: $t('仅含本级') },
  { code: 5, label: $t('主部门'), desc: $t('含下属部门'), other: { label: $t('及归属部门'), desc: $t('含下属部门') } },
  { code: 6, label: $t('全部') },
]
const treeOption = {
  label: 'menuName',
}
const treePower = ref<MenuItem[]>([])
const treeAdmin = ref<MenuItem[]>([])
const refTreePower = ref()
const refTreeAdmin = ref()
const loading = ref(false)

const getAuthCode = async (roleCode: string) => {
  try {
    loading.value = true
    const { data } = await getRoleAuthorizeByRoleCode<Array<string>>(roleCode)
    getTreeData(data)
  } catch (error) {
    loading.value = false
  }
}

const getTreeData = async (defaultKeys?: Array<string>) => {
  try {
    loading.value = true
    const { data } = await listMenuTree<MenuData>()
    treePower.value = data.powerList
    treeAdmin.value = data.adminList
    refTreePower.value.setCheckedKeys(defaultKeys)
    refTreeAdmin.value.setCheckedKeys(defaultKeys)
  } catch (error) {
  } finally {
    loading.value = false
  }
}

if (props.roleObj.isEdit) {
  formData.value = { ...props.roleObj }
  getAuthCode(props.roleObj.roleCode as string)
} else {
  getTreeData([])
}

const emit = defineEmits(['closeAuth', 'refreshAuth'])

function handleBack() {
  ElMessageBox.confirm($t('您还没有保存修改的数据，确定要返回吗？'), $t('确认提示'), {
    confirmButtonText: $t('确定'),
    cancelButtonText: $t('取消'),
  })
    .then(() => {
      emit('closeAuth')
    })
    .catch(() => {})
}

async function handleSave() {
  const listPower = refTreePower.value.getCheckedKeys()
  const listAdmin = refTreeAdmin.value.getCheckedKeys()
  const params: ISaveRoleParams = {
    roleId: formData.value?.roleId as string,
    roleName: formData.value?.roleName,
    dataAccess: formData.value?.dataAccess,
    roleRemark: formData.value?.roleRemark,
    menuCodeList: [...listPower, ...listAdmin],
  }
  try {
    loading.value = true
    await saveRole(params)
    ElMessage.success($t('保存成功'))
    emit('refreshAuth')
  } catch (error) {
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <div v-loading="loading" class="box-wrap">
    <div class="header">
      <span class="header-back" @click="handleBack"><i class="el-icon-arrow-left"></i>{{ $t('返回') }}</span>
      <span class="header-title">{{ $t('权限设置') }}</span>
      <el-button type="primary" class="header-save" :disabled="!formData.roleName" @click="handleSave">{{
        $t('保存')
      }}</el-button>
    </div>
    <div class="section-inner">
      <div class="section">
        <span class="section-title">{{ $t('角色信息') }}</span>
        <el-form :inline="true" :model="formData" label-width="120px">
          <el-form-item :label="$t('设置角色名称')" required>
            <el-input v-model.trim="formData.roleName" :placeholder="$t('请输入角色名称')"></el-input>
          </el-form-item>
          <el-form-item :label="$t('角色备注')">
            <el-input v-model.trim="formData.roleRemark" :placeholder="$t('请输入角色备注')"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div class="section">
        <span class="section-title">{{ $t('数据权限') }}</span>
        <el-form :inline="true" :model="formData" label-width="120px" class="section-body">
          <el-form-item required>
            <el-radio-group v-model="formData.dataAccess">
              <el-radio v-for="(item, idx) in departmentOptions" :key="idx" :value="item.code">
                {{ item.label }}
                <span v-if="item.desc" class="section-desc">（{{ item.desc }}）</span>
                <template v-if="item.other">
                  <span>{{ item.other.label }}</span>
                  <span class="section-desc">（{{ item.other.desc }}）</span>
                </template>
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <div class="section">
        <span class="section-title">
          <span style="white-space: nowrap">{{ $t('功能权限') }}</span>
          <span class="section-desc">{{
            $t(
              '（部分菜单下的“数据编辑”权限点对应各类数据编辑权限，如不勾选则角色仅具备编辑自己创建数据的权限，勾选后则角色对所有自己可见的该类数据均可编辑）',
            )
          }}</span></span
        >
        <div class="section-body">
          <div class="section-body__tit">{{ $t('AI 应用平台') }}</div>
          <div class="section-body__intro">{{ $t('功能') }}</div>
          <el-tree
            ref="refTreePower"
            :data="treePower"
            :props="treeOption"
            show-checkbox
            node-key="currentId"
            :expand-on-click-node="true"
            icon-class="el-icon-arrow-right"
          >
            <template v-slot="{ node, data }">
              <span v-if="node.level === 1" class="tree-level tree-level-1">
                <span>{{ node.label }}{{ $t('（一级导航）') }}</span>
              </span>
              <span v-else-if="data && data.leafNode" :class="{ 'tree-last': data && data.leafNode }">
                <span>{{ node.label }}</span>
              </span>
              <span v-else class="tree-level tree-level-2">
                <span>{{ node.label }}</span>
              </span>
            </template>
          </el-tree>
        </div>
        <div class="section-body section-len">
          <div class="section-body__tit">{{ $t('管理后台') }}</div>
          <div class="section-body__intro">{{ $t('功能') }}</div>
          <el-tree
            ref="refTreeAdmin"
            :data="treeAdmin"
            :props="treeOption"
            show-checkbox
            node-key="currentId"
            :expand-on-click-node="true"
            icon-class="el-icon-arrow-right"
          >
            <template v-slot="{ node, data }">
              <span v-if="node.level === 1" class="tree-level tree-level-1">
                <span>{{ node.label }}{{ $t('（一级导航）') }}</span>
              </span>
              <span v-else-if="data.leafNode" :class="{ 'tree-last': data.leafNode }">
                <span>{{ node.label }}</span>
              </span>
              <span v-else class="tree-level tree-level-2">
                <span>{{ node.label }}</span>
              </span>
            </template>
          </el-tree>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.box-wrap {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: calc(100vh - 54px);
  .header {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    display: flex;
    align-items: center;
    width: 100%;
    padding: 16px;
    background: #fff;
    &-back {
      display: flex;
      align-items: center;
      padding-right: 12px;
      line-height: 18px;
      color: #595959;
      cursor: pointer;
      &::after {
        display: inline-block;
        width: 1px;
        height: 12px;
        margin-left: 12px;
        content: '';
        background-color: #e9e9e9;
      }
      .el-icon-arrow-left {
        color: #bdbdbd;
      }
    }
    &-title {
      flex: 1;
      font-size: 16px;
      font-weight: bold;
    }

    // &-save {
    // }
  }
  .section-inner {
    padding: 64px 16px;
    overflow-y: auto;
  }
  .section {
    &-title {
      position: relative;
      display: flex;

      // padding-top: 8px;
      padding-bottom: 8px;
      padding-left: 8px;

      // align-items: center;
      // padding: 7px 8px;
      font-weight: bold;
      &::before {
        position: absolute;
        top: 1px;
        left: -4px;
        display: inline-block;
        width: 2px;
        height: 14px;
        content: '';
        background-color: #276ef9;
      }
    }
    &-desc {
      align-self: flex-start;
      font-size: 14px;
      font-weight: normal;
      color: #929292;
    }
    :deep(.el-input__inner) {
      width: 250px;
    }
    :deep(.el-radio) {
      padding: 8px;
    }
    :deep(.el-form-item) {
      margin-bottom: 10px;
    }
    :deep(.el-radio-group) {
      margin-bottom: -4px;
    }
    :deep(.el-form-item__label) {
      font-weight: normal;
      color: #262626;
    }
    :deep(.el-radio__label) {
      font-weight: normal;
      color: #262626;
    }
    &-body {
      padding-left: 12px;
      &__tit {
        padding: 8px 0 12px;
        font-weight: bold;
      }
      &__intro {
        height: 40px;
        padding-left: 16px;
        line-height: 40px;
        background-color: #f7f8f9;
      }
    }
    &-len {
      margin-top: 24px;
    }
  }
  .tree-level-1 {
    font-weight: bold;
  }
}
:deep(.el-tree-node__content:has(.tree-last)) {
  float: left;
}
:deep(.el-tree > .el-tree-node) {
  border-bottom: 1px solid #e9e9e9;
}
:deep(.el-tree > .el-tree-node > .el-tree-node__content) {
  height: 26px;
  padding-top: 24px;
  padding-bottom: 18px;
}
:deep(.el-tree > .el-tree-node > .el-tree-node__children > .el-tree-node) {
  padding-bottom: 10px;
}
:deep(.el-tree-node__content) {
  background-color: #fff;
}
:deep(.el-tree-node:focus > .el-tree-node__content) {
  background-color: #fff;
}
</style>
