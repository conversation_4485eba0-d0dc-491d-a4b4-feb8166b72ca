<script lang="ts" setup>
import ReviewBox from './com/ReviewBox.vue'
import ResultBox from './com/ResultBox.vue'
import WpsOffice from '@/components/wps/index.vue'
import Header from '@/layout/components/Header.vue'
import { useUserStore, useContractStore } from '@/stores'
import router from '@/router'

import { EBusEvents, EContractStatus, type IContractRecord } from './com/Model'
import mitt from '@/utils/eventsBus'
import { getReviewInfoByContractId } from '@/services/contract' // reviewRecordDownload
import { queryWpsAppId } from '@/services/onlineOffice'
import { RESPONSE_CODE_SUCCESS } from '@/constants'

// import { useDownloadFile } from '@/composables/useDownloadFile'
// import { exportUrlFile } from '@/utils'
import { downloadContract } from '@/services/common'
import { exportFile } from 'knox-tool'

const route = useRoute()
const currentCom = ref(markRaw(ResultBox))
const useStore = useUserStore()
const contractStore = useContractStore()
const fileId = ref('')
const fileType = ref('')
const appId = ref('')
const token = ref('')

const goto = () => {
  router.replace('/contract')
}

const loadContractInfoById = async (id: string) => {
  const [{ data: wpsKey }, { data: contract, code, message }] = await Promise.all([
    queryWpsAppId(),
    getReviewInfoByContractId<IContractRecord>(id),
  ])

  appId.value = (wpsKey as { wpsAppId: string }).wpsAppId
  if (code !== RESPONSE_CODE_SUCCESS && code !== '200') {
    ElMessage.error(message)
    setTimeout(() => {
      router.replace('/contract')
    }, 3000)
    return
  }

  if (!contract) {
    return
  }

  contractStore.contractInfo
    .setContractName(contract.contractName)
    .setOriginalFileCode(contract.originalFileCode)
    .setConstractId(id)
    .setIsReviewed(contract.recordId ? true : false)
    .setRecordId(contract.recordId)
  if (contract.originalFileCode) {
    const suffix = contract.originalFileCode.match(/\.\w+$/)
    if (Array.isArray(suffix)) {
      contractStore.contractInfo.setIsDoc(suffix[0] !== '.pdf')
    }
  }
  fileId.value = contractStore.contractInfo.constractId
  fileType.value = !contractStore.contractInfo.isDocFile ? 'pdf' : 'wps/doc/docx'

  mitt.emit(EBusEvents.CONSTRACT_INFO, contractStore.contractInfo)
}

const loadPage = async () => {
  const status = route.params.status as string
  if (status == EContractStatus.RESULT + '') {
    currentCom.value = markRaw(ResultBox)
  } else {
    currentCom.value = markRaw(ReviewBox)
  }
}

onMounted(() => {
  token.value = useStore.token
})

watch(
  () => route.params!.status,
  async (_) => {
    await loadPage()
    const id = route.params.id as string
    if (id) {
      loadContractInfoById(id)
    }
  },
  {
    immediate: true,
    deep: true,
  },
)
// mitt.on(EBusEvents.CONSTRACT_INFO, (info: any) => {
//   const contractInfo = info as ContractInfo
//   fileId.value = contractInfo.constractId
//   fileType.value = !contractInfo.isDocFile ? 'pdf' : 'wps/doc/docx'
//   token.value = useStore.token
// })

// 重审合同
// const hanldeBackReview = () => {
//   // recordId.value = root.$route.query.id // 用于复审时，获取审查详情
//   // isReviewed.value = false
//   // noFirstReview.value = true
//   // refResult.value.clearReview()
//   // getAIInputEnable() // 重置 initOption 的状态，AI 问答框
//   router.push('/contract/view/1/' + contractStore.contractInfo.constractId)
// }

// console.log('contractStore', contractStore)
// console.log('contractStore', contractStore.contractInfo)

// const { downloadFile } = useDownloadFile()
// const downloadLoading = ref(false)
// const handleDownload = async (command: string) => {
//   downloadLoading.value = true
//   try {
//     if (command === 'download') {
//       const res = await downloadContract<Blob>(contractStore.contractInfo.constractId)
//       exportFile(res.data, contractStore.contractInfo.contractName)
//     } else if (command === 'downloadReview') {
//       console.log('下载审查意见书')
//       // if (reviewing.value) return // 正在审查时不允许下载 ////////
//       const fileName = contractStore.contractInfo.contractName
//       const lastDotIndex = fileName.lastIndexOf('.')
//       const exportFileName = lastDotIndex === -1 ? fileName : fileName.slice(0, lastDotIndex)
//       downloadFile({
//         fileName: exportFileName + '(审查意见书)' + '.docx',
//         fileApi: [reviewRecordDownload, { recordId: contractStore.contractInfo.recordId }],
//       })
//     }
//   } catch (error) {
//     console.warn(error)
//   } finally {
//     downloadLoading.value = false
//   }
// }

const officeRef = ref<InstanceType<typeof WpsOffice>>()
provide('officeRef', officeRef)

// 下载合同(临时)
const download = async () => {
  const flag = await officeRef.value?.save()
  if (flag) {
    const res = await downloadContract<Blob>(contractStore.contractInfo.constractId)
    exportFile(res.data, contractStore.contractInfo.contractName)
  }
}

const handleChange = (val: any) => {
  console.log('WPS内容变化', val)
  // 触发内容变化事件
}
</script>
<template>
  <div class="detailWrapper">
    <div class="detailLeft">
      <Header :title="contractStore.contractInfo.contractName" :show-arrow="true" @goto="goto">
        <template #default>
          <el-button @click="download">{{ $t('下载合同') }}</el-button>
          <!-- <div v-if="contractStore.contractInfo.isDocFile" class="left-btn">
            <el-tooltip effect="light" :content="$t('合同修改后才可进行复审')" placement="bottom" visible-arrow> -->
          <!--  :disabled="!docChanged || reviewing" -->
          <!-- <el-button v-show="route.params.status == '2'" @click="hanldeBackReview" class="ghost">{{ $t('重审合同') }}</el-button>
            </el-tooltip>
            <el-dropdown
              v-show="route.params.status == '2'"
              placement="top-start"
              class="download"
              @command="handleDownload"
            >
              <span class="text-btn btn-last" :loading="downloadLoading">{{ $t('下载') }}</span>
              <template #dropdown>
                <el-dropdown-menu class="more-menu">
                  <el-dropdown-item command="download">{{ $t('下载合同') }}</el-dropdown-item> -->
          <!-- :class="{ 'forbid-download': reviewing }" -->
          <!-- <el-dropdown-item command="downloadReview">{{ $t('下载审查意见书') }}</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div> -->
        </template>
      </Header>
      <div style="width: 100%; height: calc(100vh - 2.625rem)">
        <WpsOffice
          v-if="fileId"
          ref="officeRef"
          :app-id="appId"
          :file-id="fileId"
          :office-type="fileType"
          :token="token"
          :isRevison="true"
          class="office-frame"
          @change="handleChange"
        ></WpsOffice>
      </div>
    </div>
    <div class="detailRight">
      <component :is="currentCom"></component>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.review-wrap {
  flex-direction: row !important;
}
.detailWrapper {
  display: flex;
  flex-direction: row;
  width: 100vw;
  height: 100vh;
  .detailLeft {
    flex: 1;
    width: 67vw;
    height: 100vh;
    text-align: right;
    .left-btn {
      display: flex;
      gap: 16px;
      align-items: center;
      .download {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 80px;
        height: 32px;
        color: var(--main-font);
        cursor: pointer;
        border: 1px solid var(--main-font);
        &:focus {
          color: var(--main-font);
          background-color: transparent;
          border-color: var(--main-font);
        }
        &:hover {
          color: #6d58da;
          background-color: var(--primary-btn-bg);
          border-color: #6d58da;
        }
        &:active {
          color: #3a25a7;
          background-color: var(--primary-btn-bg);
          border-color: #3a25a7;
        }
      }

      // 审查未完成时，禁用下载审查意见书按钮
      // .forbid-download {
      //   color: #606266 !important;
      //   cursor: not-allowed;
      //   background-color: #fff !important;
      // }
    }
  }
  .detailRight {
    justify-content: center;
    width: 33vw;
    min-width: 412px;
    max-width: 580px;
    border-left: 1px solid rgb(234 235 240);
  }
}
</style>
