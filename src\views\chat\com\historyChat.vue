<template>
  <div class="recent-chats-container" v-loading="loading">
    <div class="recent-chats-header">
      <p class="recent-chats-title">{{ $t('历史问答') }}</p>
    </div>
    <div class="recent-chats-body">
      <el-scrollbar
        ref="scrollableRef"
        class="recent-chats-simplebar"
        style="height: calc(100vh - 5rem)"
        @scroll="handleScroll"
      >
        <div class="recent-chats-list">
          <recentChatsItem
            v-for="(item, index) in historyChatsList"
            :key="index"
            :chat-item="item"
            @edit-name="finishEditName"
            @delete-chat="finishDeleteChat"
          ></recentChatsItem>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ChatMessage } from '@/stores/modules/chat/helper'
import { RESPONSE_CODE_SUCCESS } from '@/constants'
import ChatApi from '@/services/chat'
import recentChatsItem from './histroyChatItem.vue'
import type { IChatsListResponse, IChatItem } from '@/types/chat'
import { $t } from '@/utils/i18n'
const pageNo = ref(1)
const pageSize = ref(15)
const scrollableRef = ref()
const hasMore = ref(true)
const loading = ref(false)
const totalItems = ref(0)
const historyChatsList = ref<IChatItem[]>([])

const initParams = () => {
  pageNo.value = 1
  pageSize.value = 15
  hasMore.value = true
  loading.value = false
  totalItems.value = 0
  historyChatsList.value = []
}
const finishEditName = () => {
  initParams()
  loadData()
}

const finishDeleteChat = () => {
  initParams()
  loadData()
}

const handleScroll = () => {
  if (!scrollableRef.value) return

  // 获取 el-scrollbar 的内部滚动容器
  const scrollElement = scrollableRef.value.wrapRef
  if (!scrollElement) return

  const { scrollTop, scrollHeight, clientHeight } = scrollElement
  const distanceToBottom = scrollHeight - scrollTop - clientHeight

  // 当距离底部150px时加载下一页
  if (distanceToBottom < 150 && !loading.value && hasMore.value) {
    loadData()
  }
}

const loadData = async () => {
  if (loading.value || !hasMore.value) return
  loading.value = true
  try {
    const res = await ChatApi.getChatsList<IChatsListResponse>({
      currentPageNo: pageNo.value,
      pageSize: pageSize.value,
    })
    if (res.code === RESPONSE_CODE_SUCCESS) {
      historyChatsList.value.push(...(res.data?.list || []))

      const promises: Promise<any>[] = []

      for (let i = (pageNo.value - 1) * pageSize.value; i < historyChatsList.value.length; i++) {
        // 创建一个异步函数并立即执行，将其 Promise 添加到数组中
        const promise = (async () => {
          const { data } = await ChatApi.chatMessages(historyChatsList.value[i]['id'])
          if (!Array.isArray(data) || data.length === 0) return []

          const chatData: ChatMessage[] = data.map((item) => {
            const msg = ChatMessage.createFromResponse(item)
            if (msg.chatContent) msg.showTools = true
            return msg
          })
          const chatsMessages: ChatMessage[] = chatData.map((item, index) => {
            if (item.chatRole === 'assistant') {
              const target = chatData[index - 1]
              item.isDeepSeek = target.isDeepSeek
            }
            return item
          })

          historyChatsList.value[i].lastAnswerContent =
            chatsMessages[chatsMessages.length - 1]?.chatContent || $t('暂无内容')
          return ''
        })()

        promises.push(promise)
      }

      // 等待所有请求完成
      await Promise.all(promises)
      loading.value = false

      // 更新分页信息
      totalItems.value = res.data.total || 0
      hasMore.value = historyChatsList.value.length < totalItems.value

      if (hasMore.value) {
        pageNo.value++
      }
    }
  } catch (error) {
    console.error('Failed to load data:', error)
  } finally {
    loading.value = false
  }
}
onMounted(() => {
  loadData()
})
</script>

<style scoped>
:deep(.el-scrollbar__bar.is-vertical) {
  width: 0;
}
:deep(.simplebar-scrollbar) {
  width: 0 !important;
}
.recent-chats-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100vh;
  margin: 0 auto;
  color: #262626;
  .recent-chats-header {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 3.5rem;
    .recent-chats-title {
      font-size: 1.125rem;
      font-weight: 500;
      line-height: 24px;
      text-align: center;
    }
  }
  .recent-chats-body {
    display: flex;
    flex: 1;
    justify-content: center;
    .recent-chats-list {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: calc(100vw - var(--iterms-sidebar-width));
      height: fit-content;
      .recent-chats-item-container:first-of-type {
        margin-top: 0;
      }
    }
  }
}
</style>
