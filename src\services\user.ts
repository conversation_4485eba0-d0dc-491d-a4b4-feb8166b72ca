import { post, get } from '@/services'
import type { IResponse } from '@/services'

// interface ILoginParams {
//   username: string
//   password: string
// }
// 数据字典
// export function requesetLogin<T>(data: ILoginParams): Promise<IResponse<T>> {
//   return request({
//     url: `/auth/login`,
//     method: 'post',
//     data,
//   })
// }

interface ILoginCodeParams {
  userName: string
  scene: string
  challenge: string
  validate: string
  seccode: string
}

export function getQrCode<T>(): Promise<IResponse<T>> {
  return post<T>(`/login/get-qr-code`)
}

export function getQrCodeLoginResult<T>(qrCodeId: string): Promise<IResponse<T>> {
  return post<T>(`/login/get-qr-code_result`, {
    qrCodeId,
  })
}

export function getCurrentUserInfo<T>(): Promise<IResponse<T>> {
  return get<T>(`/account/user/getCurrUser`)
}

export function geetestCall<T>(): Promise<IResponse<T>> {
  return post<T>(`/login/geetest/init`)
}

export function sendLoginCode<T>(data: ILoginCodeParams): Promise<IResponse<T>> {
  return post<T>(`/login/verify-code/send`, data)
}

interface IChangeManagerCodeParams {
  scene: string
  challenge: string
  validate: string
  seccode: string
}
export function sendChangeManagerCode<T>(data: IChangeManagerCodeParams): Promise<IResponse<T>> {
  return post<T>(`/member/verify-code/send`, data)
}

export function doLoginByCode<T>(userName: string, code: string, scene: string): Promise<IResponse<T>> {
  return post<T>(`/login/verify-code/check`, {
    userName,
    verifyCode: code,
    scene,
  })
}

export function doLoginByPwd<T>(userName: string, password: string): Promise<IResponse<T>> {
  return post<T>(`/login/loginByPwd`, {
    userName,
    password,
  })
}

export function checkChangeManagerCode<T>(code: string): Promise<IResponse<T>> {
  return post<T>(`/member/verify-code/check`, {
    verifyCode: code,
  })
}

export function editNickName<T>(nickName: string): Promise<IResponse<T>> {
  return post<T>(`/account/modify`, {
    nickName,
  })
}

export function loginOut<T>(): Promise<IResponse<T>> {
  return get<T>(`/account/logout`)
}

export function deleteUser<T>(): Promise<IResponse<T>> {
  return post<T>(`/account/deleteUser`)
}
export function getCorpList<T>(): Promise<IResponse<T>> {
  return post<T>(`/account/corpList`)
}

export function switchCorp<T>(memberId: string): Promise<IResponse<T>> {
  return post<T>(`/account/switch`, {
    memberId,
  })
}

// 获取当前用户的所有功能
export interface IUserPermission {
  menuCodeList: string[]
}
export function getUserPermission<T>(): Promise<IResponse<T>> {
  return get<T>(`/user/get-function-list-by-currentUser`)
}

export default {
  getQrCode,
  getQrCodeLoginResult,
  getCurrentUserInfo,
  geetestCall,
  sendLoginCode,
  sendChangeManagerCode,
  doLoginByCode,
  editNickName,
  loginOut,
  deleteUser,
  checkChangeManagerCode,
  getCorpList,
  switchCorp,
  doLoginByPwd,
  getUserPermission,
}
