<template>
  <div class="chat-container">
    <div class="chat-body">
      <div class="chat-body-content">
        <div class="chat-body-content-title">{{ $t('欢迎使用 iTerms') }}</div>
        <div class="chat-body-content-desc">{{ $t('我可以帮您审查合同，或者直接问我法律问题~~~') }}</div>
        <div class="divider"></div>
        <div class="question-container">
          <div class="question-container-header">{{ $t('你可以这样问') }}</div>
          <div v-for="item in questionList" class="question-container-item" :key="item.id" @click="addQuestion(item)">
            {{ item.title }}
          </div>
        </div>
      </div>
    </div>
    <div class="chat-footer">
      <div style="position: relative">
        <TextInputer
          :key="loadEditor"
          ref="contenteditRef"
          :size="'attach-input-flow'"
          :innerheight="72"
          :hook="checkLoginStatus"
          @send="send"
        />
      </div>
      <p class="agent-tip">{{ $t('以上内容由人工智能模型生成, 仅作参考') }}</p>
    </div>
  </div>
</template>
<script lang="ts" setup>
import TextInputer from '@/components/chat/textInputer.vue'
import { useChatStore, useUserStore } from '@/stores'
import { RESPONSE_CODE_SUCCESS } from '@/constants'
import { ChatCommand } from '@/stores/modules/chat/helper'
import ChatApi from '@/services/chat'
import { useRouter } from 'vue-router'
import { $t } from '@/utils/i18n'

const router = useRouter()
const chatStore = useChatStore()
const userStore = useUserStore()
const loadEditor = ref(new Date().getTime())
const contenteditRef = ref()
const questionList = computed(() => [
  {
    id: 1,
    title: $t('被打成轻伤，如何向对方索赔？'),
  },
  {
    id: 2,
    title: $t('孕期被辞退，怎么维权?'),
  },
  {
    id: 3,
    title: $t('医疗纠纷中，举证责任是由谁来承担？'),
  },
])

const addQuestion = (item: any) => {
  contenteditRef.value.setContent(item.title)
}
const checkLoginStatus = () => {
  return new Promise((resolve) => {
    if (userStore.token) {
      resolve(true)
    } else {
      resolve(false)
    }
  })
}

const send = async (chatCommand: ChatCommand) => {
  chatStore.chatActive
    .setSearchTypes(chatCommand.searchTypes)
    .setQuestion(chatCommand.content)
    .setChatAttachInfo(chatCommand.chatAttachInfo)
    .setThinking(chatCommand.thinking)
    .setWebSearch(chatCommand.webSearch)
  //没有输入只有附件，title取附件名称
  let title = chatCommand.content
  if (title == '' && chatStore.chatActive.chatAttachInfo.length) {
    title = chatStore.chatActive.chatAttachInfo[0]?.fileName
  }
  const { code, data } = await ChatApi.createDifyChat<{ id: string }>(title.slice(0, 20))
  if (code === RESPONSE_CODE_SUCCESS) {
    chatStore.chatActive.setChatId(data.id)
    chatStore.insertChatInHeader(data)
    router?.push('/chat/' + data.id)
    contenteditRef.value.reset()
  }
}
</script>
<style lang="scss" scoped>
.chat-container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  min-width: min(100%, 50rem); /* 当视图窗口小于1200px时，使用100%宽度 */
  max-width: 50rem;
  height: 100%;
  margin: 0 auto;
  .chat-header {
    display: flex;
    align-items: center;
    padding-top: 6.25rem;
    .chat-header-right {
      flex: 1;
      margin-left: 1.25rem;
      .chat-header-right-title {
        font-size: 1.5rem;
        color: var(--minor-font);
      }
      .chat-header-right-desc {
        margin-top: 0.5rem;
        font-size: 0.875rem;
        color: var(--is-color-7d7b89);
      }
    }
  }
  .chat-body {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    justify-content: center;
    width: 100%;
    height: calc(100vh - 13.5rem);
    .chat-body-content {
      padding-top: 3.5rem;
      .chat-body-content-title {
        font-family: 'DingTalk JinBuTi', 'PingFang SC', sans-serif;
        font-size: 2.25rem;
        font-weight: 500;
        color: var(--minor-font);
      }
      .chat-body-content-desc {
        margin-top: 0.5rem;
        font-size: 1rem;
        color: var(--is-color-7d7b89);
      }
      .divider {
        margin: 1rem 0;
        border-bottom: 1px dashed var(--page-header-line);
      }
      .question-container {
        .question-container-header {
          font-size: 0.75rem;
          line-height: 1.25rem;
          color: var(--is-color-7d7b89);
        }
        .question-container-item {
          width: fit-content;
          padding: 0.5rem 0.75rem;
          margin-top: 0.5rem;
          font-size: 0.875rem;
          color: var(--minor-font);
          cursor: pointer;
          background-color: var(--input-bg);
          border-radius: 0.5rem;
        }
        .question-container-item:first-of-type {
          margin-top: 0;
        }
      }
    }
    .feature {
      display: flex;
      flex-direction: column;
      width: 280px;
      height: 320px;
      padding: 1rem;
      background: var(--bg-color);
      background: linear-gradient(135deg, rgb(229 244 255 / 75%) 0%, rgb(239 231 255 / 75%) 100%);
      border-radius: 16px;
      .feature-title {
        font-size: 1rem;
        font-weight: 600;
        color: var(--minor-font);
      }
      .feature-desc {
        font-size: 0.75rem;
        color: var(--is-color-7d7b89);
      }
      .feature-action-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-top: 2rem;
        .feature-action-item {
          display: flex;
          gap: 0.75rem;
          align-items: center;
          padding: 0.4375rem 0 0.4375rem 0.75rem;
          cursor: pointer;
          background-color: #ffffffa5;
          border: 1px solid var(--bg-color);
          border-radius: 0.5rem;
          backdrop-filter: blur(135.9141px);
        }
        .efficiency-tool-container-list-item {
          .efficiency-tool-container-list-item-title {
            font-size: 0.875rem;
            font-weight: 500;
            line-height: 1.375rem;
            color: var(--minor-font);
          }
          .efficiency-tool-container-list-item-desc {
            font-size: 0.75rem;
            line-height: 1.375rem;
            color: var(--is-color-7d7b89);
          }
        }
        .expert-qa-container-list-item {
          font-size: 0.875rem;
          font-weight: 500;
          .expert-qa-container-list-item-title {
            line-height: 1.5625rem;
          }
        }
        .knowledge-base-container-list-item {
          padding: 0.75rem;
          .knowledge-base-container-list-item-title {
            font-size: 0.875rem;
            font-weight: 500;
            line-height: 1.375rem;
          }
          .knowledge-base-container-list-item-desc {
            margin-top: 0.75rem;
            font-size: 0.75rem;
            line-height: 1.25rem;
            color: var(--is-color-7d7b89);
          }
        }
      }
    }
  }
  .chat-footer {
    position: absolute;
    bottom: 0.75rem;
    display: flex;
    flex-direction: column;
    width: 100%;
    .agent-tip {
      margin-top: 0.75rem;
      font-size: 0.75rem;
      color: var(--sidebar-line);
      text-align: center;
    }
  }
}
</style>
