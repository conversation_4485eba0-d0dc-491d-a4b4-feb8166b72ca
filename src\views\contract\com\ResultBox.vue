<script lang="ts" setup>
import Summary from './Summary.vue'
import Risks from './Risks.vue'
import AiHelper from './AiHelper.vue'
import mitt from '@/utils/eventsBus'
import { useContractResultService } from './useContractResultService'
import { useContractStore } from '@/stores'
const service = useContractResultService()
const tabActive = ref('left')
const contractStore = useContractStore()
const aiHelperRef = ref()
const hanldeChangeTab = (val: string) => {
  tabActive.value = val
  // if (val === 'center') {
  // service.contractRiskReview()
  // }
}
mitt.on('selectionChangeForDoc', async (text: any) => {
  // tabActive.value = 'right'
  setTimeout(() => {
    aiHelperRef.value.setReferText(text)
  }, 0)
})
const wrapRef = ref()
onMounted(() => {
  if (wrapRef.value) {
    const resizeObserver = new ResizeObserver(() => {
      if (wrapRef.value?.offsetWidth) {
        const w = wrapRef.value.offsetWidth

        service.isMiniWidth.value = w <= 412
        mitt.emit('isMiniWidth', service.isMiniWidth.value)
      }
    })
    resizeObserver.observe(wrapRef.value)
  }
})
onUnmounted(() => {
  mitt.off('selectionChangeForDoc')
})
</script>
<template>
  <div>
    <div ref="wrapRef" class="tab-wrap">
      <span class="tab-item" @click="hanldeChangeTab('left')">
        <span :class="{ 'tab-active': tabActive === 'left' }">{{ $t('合同摘要') }}</span>
      </span>
      <span class="tab-item" @click="hanldeChangeTab('center')">
        <span :class="{ 'tab-active': tabActive === 'center' }">{{ $t('风险情况') }}</span>
      </span>
      <span class="tab-item" @click="hanldeChangeTab('right')">
        <span :class="{ 'tab-active': tabActive === 'right' }">{{ $t('智能问答') }}</span>
      </span>
    </div>
    <div class="container">
      <Summary v-show="tabActive === 'left'" :contract-info="contractStore.contractInfo" />
      <Risks v-show="tabActive === 'center'" style="height: calc(100vh - 4rem)" :service="service" />
      <AiHelper v-show="tabActive === 'right'" ref="aiHelperRef" />
    </div>
  </div>
</template>
<style lang="scss" scoped>
.tab-wrap {
  display: flex;
  align-items: center;
  width: 100%;
  height: 2.625rem;
  background-color: var(--bg-color);
  border-bottom: 1px solid #e9e9e9;
  .tab-item {
    position: relative;
    width: 50%;
    text-align: center;
    &:hover {
      color: var(--is-color-773bef);
    }
    span {
      padding-bottom: 9px;
      cursor: pointer;
      user-select: none;
    }
    .tab-active {
      font-weight: 500;
      color: var(--is-color-773bef);
      pointer-events: none;
      border-bottom: 2px solid var(--is-color-773bef);
    }
  }
}
.container {
  overflow: auto;
}
</style>
