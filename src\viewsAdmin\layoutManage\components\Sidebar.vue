<script setup lang="ts">
import { $t } from '@/utils/i18n'

interface MenuItem {
  label: string
  icon?: string
  iconActive?: string
  path: string
  children?: MenuItem[]
}

const menus = ref<MenuItem[]>([
  // {
  //   label: '组织权限管理',
  //   icon: 'icon-is-zuzhi',
  //   iconActive: 'icon-is-role',
  //   path: '',
  //   children: [
  //     {
  //       label: '角色管理',
  //       path: '/role',
  //     },
  //     {
  //       label: '组织管理',
  //       path: '/organization',
  //     },
  //   ],
  // },
  {
    label: $t('角色管理'),
    icon: 'icon-is-zuzhi',
    iconActive: 'icon-is-role',
    path: '/role',
  },
  {
    label: $t('组织管理'),
    icon: 'icon-is-zuzhi',
    iconActive: 'icon-is-role',
    path: '/organization',
  },
])

const route = useRoute()
const router = useRouter()
const defaultActive = ref(route.path)
const currentMenu = computed(() => route.path)

const handleBack = () => {
  router.push('/contract')
}

const isFold = ref(false) // 侧边栏折叠状态
const handleChangeFold = (val: boolean) => {
  isFold.value = val
}
</script>

<template>
  <div>
    <!-- 隐藏收起过渡组件 -->
    <Transition name="fade">
      <div v-if="!isFold" class="sidebar">
        <!-- 侧边栏头部 -->
        <div class="sidebar-header">
          <!-- 返回按钮 -->
          <i class="iconfont icon-is-back" @click="handleBack"></i>
          <!-- 菜单名称 -->
          <span class="menus-name">{{ $t('组织管理') }}</span>
          <!-- 折叠按钮 -->
          <i class="iconfont icon-is-fold" @click="handleChangeFold(true)"></i>
        </div>
        <!-- 菜单列表 -->
        <el-scrollbar wrap-class="scrollbar-wrapper">
          <el-menu :default-active="defaultActive" class="el-menu-vertical-demo" router>
            <template v-for="menu of menus" :key="menu.path">
              <!-- 有子菜单 -->
              <el-sub-menu v-if="menu.children && menu.children.length" :index="menu.path">
                <template #title>
                  <!-- 动态图标 -->
                  <i :class="currentMenu == menu.path ? menu.iconActive : menu.icon" class="iconfont"></i>
                  <span class="menu-title">{{ menu.label }}</span>
                </template>
                <!-- 子菜单内容 -->
                <el-menu-item-group>
                  <el-menu-item v-for="childMenu of menu.children" :key="childMenu.path" :index="childMenu.path">
                    <span class="menu-title-sub">{{ childMenu.label }}</span>
                  </el-menu-item>
                </el-menu-item-group>
              </el-sub-menu>
              <!-- 没有子菜单 -->
              <el-menu-item v-else :key="menu.path" :index="menu.path" class="menu-wrap">
                <template #title>
                  <i :class="currentMenu == menu.path ? menu.iconActive : menu.icon" class="iconfont"></i>
                  <span class="menu-title">{{ menu.label }}</span>
                </template>
              </el-menu-item>
            </template>
          </el-menu>
        </el-scrollbar>
      </div>
    </Transition>
    <!-- 折叠状态的展开按钮 -->
    <div v-show="isFold" class="sidebar-menus" @click="handleChangeFold(false)">
      <div class="sidebar-menus-icon">
        <i class="iconfont icon-is-unfold"></i>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.sidebar {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 220px;
  height: 100vh;
  padding: 16px 12px;
  white-space: nowrap;
  background-color: #f9f9fb;

  // 头部
  &-header {
    display: flex;
    align-items: center;
    width: 100%;
    height: 24px;
    margin-bottom: 16px;
    font-size: 16px;
    .icon-is-back {
      font-size: 18px;

      // font-weight: bold;
    }
    .menus-name {
      position: relative;
      flex: 1;
      margin-left: 12px;
      font-weight: 600;
      &::before {
        position: absolute;
        top: 2px;
        left: -8px;
        width: 2px;
        height: 16px;
        content: '';
        background-color: var(--page-header-line);
      }
    }
  }

  // 菜单列表
  .scrollbar-wrapper {
    flex: 1;
    width: 100%;
    .menu-title {
      margin-left: 8px;
      &-sub {
        margin-left: 4px;
      }
      .menu-wrap {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        height: 40px;
        padding-left: 10px;
        margin-bottom: 4px;
        cursor: pointer;
        border-radius: 4px;
        &:hover {
          background-color: var(--menu-bg-active);
        }
      }

      // 没生效的代码
      // .menu-active {
      //   color: var(--main-font);
      //   cursor: default;
      //   background-color: var(--menu-bg-active);
      // }
    }
  }
}

// 图标样式
.iconfont {
  cursor: pointer;
}
.icon-is-unfold,
.icon-is-fold {
  font-size: 20px;
}

// 折叠按钮
.sidebar-menus {
  position: fixed;
  top: 8px;
  left: 0;
  width: 16px;
  height: 40px;
  transition: width 0.2s linear;
  &-icon {
    position: absolute;
    right: 0;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: var(--bg-color);
    box-shadow: 0 4px 12px 0 rgb(34 29 57 / 10%);
  }
  &:hover {
    z-index: 5;
    width: 50px;
    color: var(--main-font);
  }
}

// 折叠动画效果
.fade-enter-active,
.fade-leave-active {
  transition: all 0.2s linear;
}
.fade-enter-from,
.fade-leave-to {
  width: 0;
  opacity: 0;
}

// 菜单列表组件
:deep(.el-scrollbar) {
  width: 100%;
  height: 100%;
}
:deep(.el-menu) {
  background-color: #f9f9fb;
  border-right: none;
}

// :deep(.el-menu-item-group) {
//   .el-menu-item {
//     padding-left: 28px !important;
//   }
// }
:deep(.el-sub-menu__title) {
  height: 40px;
  padding: 0 0 0 8px !important;
  margin-bottom: 8px;
  line-height: 40px;
  background-color: #f9f9fb;

  // color: var(--main-font);
  &:hover {
    background: var(--menu-bg-active);
  }
}
:deep(.el-menu-item-group__title) {
  display: none;
}
:deep(.el-sub-menu__icon-arrow) {
  right: 6px;
  font-size: 14px;
}
:deep(.el-menu-item) {
  height: 40px;
  padding: 0 0 0 28px !important;
  margin-bottom: 8px;
  line-height: 40px;
  background-color: #f9f9fb;
  border-radius: 4px;

  // color: var(--main-font);

  // .icon-agent-eHome {
  //   font-size: 17px;
  // }
  &.is-active {
    color: var(--main-font);
    background-color: var(--menu-bg-active);

    // .icon-agent-eHome::before {
    //   font-size: 16px;
    //   content: '\e821';
    // }
  }
  &:hover {
    background: var(--menu-bg-active);
  }
}
:deep(.menu-wrap.el-menu-item) {
  padding-left: 10px !important;
}
</style>
