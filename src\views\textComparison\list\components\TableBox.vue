<script setup lang="ts">
import {
  queryCompareRecord,
  deleteCompareRecord,
  deleteSemanticsRecord,
  querySemanticsCompareRecord,
} from '@/services/textComparison'
import type { ICompareResponseList } from '@/services/textComparison'
import { statusEnum } from '../types/enums'
import type { IStatusOption, ISearchArgs } from '../types'

import { $t } from '@/utils/i18n'
interface IProps {
  searchArgs: ISearchArgs
  statusOption: IStatusOption[]
  isSemantics: boolean
}
const props = withDefaults(defineProps<IProps>(), {
  isSemantics: false,
})

const loading = ref(false)
const page = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref<ICompareResponseList['list']>([])
const pollInterval = 30000 // 30s 轮询一次

// 比对状态： 1-排队中, 2-比对中, 3-比对成功, 4-比对失败
const getStatusValue = (status: statusEnum | '') => {
  if (status === statusEnum.QUEUE) return 1
  if (status === statusEnum.IN_PROGRESS) return 2
  if (status === statusEnum.SUCCESS) return 3
  if (status === statusEnum.FAILED) return 4
  return undefined
}

const getTableData = async () => {
  loading.value = true
  // 构建请求参数对象
  const params = {
    page: page.value,
    pageSize: pageSize.value,
    data: {
      contractName: props.searchArgs.contractName || undefined,
      progressType: getStatusValue(props.searchArgs.status),
      startTime: props.searchArgs.dateRange?.[0],
      endTime: props.searchArgs.dateRange?.[1],
    },
  }

  try {
    let res
    if (props.isSemantics) {
      res = await querySemanticsCompareRecord<ICompareResponseList>(params)
    } else {
      res = await queryCompareRecord<ICompareResponseList>(params)
    }
    const { data } = res
    tableData.value = data.list
    total.value = +data.total
  } catch (error) {
    console.error(error)
  } finally {
    loading.value = false
  }
}

const timer = setInterval(() => {
  getTableData()
}, pollInterval)

onMounted(() => {
  getTableData()
})

onUnmounted(() => {
  timer && clearInterval(timer)
})

const reset = () => {
  page.value = 1
  getTableData()
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  page.value = 1
  getTableData()
}

const handleCurrentChange = (val: number) => {
  page.value = val
  getTableData()
}

const getProgressPercentage = (progressNum: number) => {
  // 比对失败 / 未开始 时显示 0%
  if (progressNum === statusEnum.FAILED || progressNum === statusEnum.QUEUE) return statusEnum.QUEUE
  return progressNum
}

const getProgressColor = (progressNum: number) => {
  if (progressNum === statusEnum.FAILED) return '#E6555E' // 比对失败
  if (progressNum === statusEnum.SUCCESS) return '#1B9275' // 比对成功
  return undefined
}

// 比对状态 -  比对中：1-99；比对成功：100；比对失败：-1；排队中：0；
const setTagStatus = (val: number, type: 'color' | 'label') => {
  const tag = props.statusOption.find((item) => {
    if (val > statusEnum.QUEUE && val < statusEnum.SUCCESS) return item.value == 1
    return item.value == val
  })
  return tag ? tag[type] : ''
}

const getComparisonInfo = (id: string) => {
  const path = props.isSemantics ? 'semantics' : 'comparison'
  window.open(`/${path}?compareId=${id}`)
}

const delComparisonInfo = (id: string) => {
  ElMessageBox.confirm($t('确认删除比对记录吗?'), $t('提示'), {
    confirmButtonText: $t('确认'),
    cancelButtonText: $t('取消'),
    confirmButtonClass: 'el-button--danger',
    customClass: 'cus-message-box warn',
    showClose: false,
  })
    .then(async () => {
      if (props.isSemantics) {
        await deleteSemanticsRecord(id)
      } else {
        await deleteCompareRecord(id)
      }
      getTableData()
      ElMessage({
        type: 'success',
        message: $t('删除成功!'),
      })
    })
    .catch(() => {
      console.log('取消删除比对记录')
    })
}

defineExpose({
  reset,
})
</script>

<template>
  <div class="table-wrap">
    <el-table
      ref="table"
      v-loading="loading"
      height="100%"
      :header-cell-style="{
        'background-color': '#fff',
        color: '#7D7B89',
        'font-size': '14px',
        'line-height': '22px',
      }"
      :row-style="{ height: '46px' }"
      :cell-style="{ padding: '4px 0 0 0', 'font-size': '14px', color: '#221D39' }"
      :data="tableData"
    >
      <el-table-column :label="$t('原稿文档')" :show-overflow-tooltip="true" min-width="200">
        <template v-slot="scope">
          <span>{{ scope.row.leftFilename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('比对文档')" :show-overflow-tooltip="true" min-width="200">
        <template v-slot="scope">
          <span>{{ scope.row.rightFilename || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('比对状态')" width="200">
        <template v-slot="scope">
          <span class="status-dot" :style="{ 'background-color': setTagStatus(scope.row.progressNum, 'color') }"></span>
          <span>{{ setTagStatus(scope.row.progressNum, 'label') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('比对进度')" min-width="200">
        <template v-slot="scope">
          <div class="progress-wrap" :class="{ 'failed-progress': scope.row.progressNum == -1 }">
            <el-progress
              :percentage="getProgressPercentage(scope.row.progressNum)"
              :stroke-width="6"
              :color="getProgressColor(scope.row.progressNum)"
            ></el-progress>
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('完成时间')" width="200">
        <template v-slot="scope">
          <span>{{ scope.row.updateTime ? scope.row.updateTime : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('操作')" width="200" fixed="right">
        <template v-slot="scope">
          <el-button-group class="el-button-right-line">
            <el-button
              v-if="scope.row.progressNum !== 0"
              type="primary"
              link
              @click="getComparisonInfo(scope.row.id)"
              >{{ $t('查看') }}</el-button
            >
            <el-button type="primary" link @click="delComparisonInfo(scope.row.id)">{{ $t('删除') }}</el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-show="total > 0"
      :current-page="page"
      :page-sizes="[10, 20, 30, 40]"
      :page-size="pageSize"
      layout="total, prev, pager, next, sizes, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
    </el-pagination>
  </div>
</template>

<style lang="scss" scoped>
.table-wrap {
  position: relative;
  flex: 1;
  padding-bottom: 42px;
  .progress-wrap {
    width: 90%;
    :deep(.el-progress) {
      margin-left: 6px;
    }
  }

  /* 检查失败的进度条特殊样式 */
  .failed-progress {
    :deep(.el-progress-bar) {
      :deep(.el-progress-bar__outer) {
        background-color: var(--is-color-e6555e);
      }
      .el-progress-bar__inner {
        width: 100% !important;
        background-color: var(--is-color-e6555e);
      }
    }
  }

  /* 比对状态样式 */
  .status-dot {
    display: inline-block;
    width: 5px;
    height: 5px;
    margin-right: 5px;
    margin-bottom: 2px;
    border-radius: 50%;
  }

  /* 分页组件 */
  :deep(.el-pagination) {
    position: absolute;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: flex-end;
    padding-top: 12px;
  }

  /* 表格右侧固定列底部边框 START */
  :deep(.el-table__fixed::before),
  :deep(.el-table__fixed-right::before) {
    background-color: transparent;
  }

  /* 表格右侧固定列底部边框 END */
}
</style>
