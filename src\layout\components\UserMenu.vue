<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useUserStore } from '@/stores'
import { MEMBER_ID_KEY, RESPONSE_CODE_SUCCESS } from '@/constants'
import UserApi from '@/services/user'
import mitt from '@/utils/eventsBus'
import { useLanguage } from '@/composables/useLanguage'
const { currentLanguage, languageOptions, switchLanguage, getCurrentLanguageInfo } = useLanguage()

import { $t } from '@/utils/i18n'
interface Props {
  visible: boolean
  permissionList: string[]
}

interface Emits {
  (e: 'close'): void
  (e: 'version-switch', memberId?: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const router = useRouter()
const userStore = useUserStore()
const personalList = computed(() => {
  return [
    {
      name: userStore.userInfo?.nickName || '',
      id: userStore.userInfo?.userId || '',
    },
  ]
})
const corpList = computed(() => userStore.userInfo?.corpList || [])
console.log('corpList', corpList)

// 版本切换弹窗状态
const versionDialogVisible = ref(false)

// 选中的个人ID和企业ID
const selectedPersonalId = ref('')
const selectedCorpId = ref('')

// 处理菜单项点击
function handleMenuClick() {
  emit('close')
}

// 处理版本切换
function handleVersionSwitch() {
  // 先关闭菜单
  emit('close')
  // 然后打开对话框
  setTimeout(() => {
    versionDialogVisible.value = true
  }, 10)
  router.push('/role')
}

// 处理版本选择
async function handleSelectVersion(type: 'personal' | 'corp', id: string) {
  try {
    emit('close')

    if (type === 'personal') {
      selectedPersonalId.value = id
      selectedCorpId.value = ''
      // 清除本地存储的企业ID
      localStorage.removeItem(MEMBER_ID_KEY)
      // 切换到个人版本
      await UserApi.switchCorp('')

      //////// 个人版菜单直接写死
      userStore.setMenuList(['contractContent', 'ContractComparison', 'ContractExtract', 'QA'])
      console.log('个人版菜单直接写死', userStore.getMenuList())
    } else {
      selectedCorpId.value = id
      selectedPersonalId.value = ''
      // 保存企业ID到本地存储
      localStorage.setItem(MEMBER_ID_KEY, id)
      // 切换到企业版本
      await UserApi.switchCorp(id)

      // 获取当前用户功能列表
      await userStore.getMenuCodeList()
    }

    // 更新用户信息
    await userStore.fetchCurrentUserInfo()

    // 提示切换成功
    ElMessage.success($t('切换成功'))
    // 触发版本切换事件
    emit('version-switch', type === 'corp' ? id : undefined)
    // 关闭菜单
  } catch (error) {
    ElMessage.error($t('切换失败，请稍后再试'))
    console.error('版本切换失败:', error)
  }
}

// 处理语言切换
function handleLanguageSwitch(lang: any) {
  switchLanguage(lang)
  emit('close')
}

// 处理退出登录
function handleLogout() {
  emit('close')
  ElMessageBox.confirm($t('退出后，需要重新登录才能使用。是否登出？'), $t('确认登出'), {
    confirmButtonText: $t('确定'),
    cancelButtonText: $t('取消'),
    type: 'warning',
  })
    .then(async () => {
      try {
        const { code } = await UserApi.loginOut()
        if (code === RESPONSE_CODE_SUCCESS) {
          userStore.initUserInfo()
          ElMessage.success($t('退出登录成功'))
          mitt.emit('refresh-contract-list')
        } else {
          ElMessage.error($t('退出登录失败，请稍后再试'))
        }
      } catch (error: any) {
        ElMessage.error($t('退出登录失败，请稍后再试'))
      }
    })
    .catch(() => {
      // Handle the cancellation action here
    })
}

const initMemberId = () => {
  const cachedMemberId = localStorage.getItem(MEMBER_ID_KEY)
  if (!cachedMemberId) {
    selectedPersonalId.value = userStore.userInfo?.userId || ''
    selectedCorpId.value = ''
  } else {
    selectedCorpId.value = cachedMemberId
    selectedPersonalId.value = ''
  }
}

// 监听弹窗显示状态，每次显示时调用 initMemberId
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      initMemberId()
    }
  },
  { immediate: true },
)
</script>
<template>
  <div v-if="visible" class="user-menu" @click.stop>
    <!-- 菜单项部分 -->
    <div class="menu-items-wrapper">
      <router-link to="/userCenter" class="item-menu" @click="handleMenuClick">
        <Icons name="person" width="1.35rem" height="1.35rem" />
        <span class="title">{{ $t('个人中心') }}</span>
      </router-link>
      <div class="item-menu" @click="handleVersionSwitch" v-show="permissionList.includes('corpManage')">
        <Icons name="persons" width="1.35rem" height="1.35rem" />
        <span class="title">{{ $t('企业管理') }}</span>
      </div>
      <div class="item-menu language-switcher">
        <span class="language-label">{{ $t('语言') }}:</span>
        <div class="language-options">
          <span
            v-for="option in languageOptions"
            :key="option.value"
            :class="['language-option', { active: currentLanguage === option.value }]"
            @click.stop="handleLanguageSwitch(option.value)"
          >
            {{ option.shortLabel }}
          </span>
        </div>
      </div>
      <div class="item-menu" @click="handleLogout">
        <Icons name="out" width="1.35rem" height="1.35rem" />
        <span class="title">{{ $t('退出登录') }}</span>
      </div>
    </div>

    <!-- 分割线 -->
    <div class="divider"></div>

    <!-- 版本切换部分 -->
    <div class="version-section">
      <!-- 个人版 -->
      <div
        v-for="item in personalList"
        :key="item.id"
        :class="['version-item', { selected: selectedPersonalId === item.id }]"
        @click="handleSelectVersion('personal', item.id)"
      >
        <Icons name="user" width="1.35rem" height="1.35rem" />
        <span class="version-name">{{ item.name }}</span>
        <Icons v-if="selectedPersonalId === item.id" name="selected" width="1.2rem" height="1.2rem" />
      </div>

      <!-- 企业版 -->
      <div
        v-for="item in corpList"
        :key="item.id"
        :class="['version-item', { selected: selectedCorpId === item.id }]"
        @click="handleSelectVersion('corp', item.id)"
      >
        <Icons name="org" width="1.35rem" height="1.35rem" />
        <span class="version-name">{{ item.corp.corpName || '-' }}</span>
        <Icons v-if="selectedCorpId === item.id" name="selected" width="1.2rem" height="1.2rem" />
      </div>
    </div>
  </div>
</template>

<!-- 版本切换弹窗 -->
<style lang="scss" scoped>
.user-menu {
  position: absolute;
  bottom: 100%;
  left: 8px;
  z-index: 1000;
  width: 200px;
  margin-bottom: 0.5rem;
  overflow: hidden;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px #2626261a;
  .menu-items-wrapper {
    padding: 8px;
    .item-menu {
      display: flex;
      align-items: center;
      height: 2.5rem;
      padding: 0 12px;
      text-decoration: none;
      cursor: pointer;
      border-radius: 4px;
      transition: background-color 0.2s ease;
      &:hover {
        background-color: #f5f5f5;
      }
      span {
        margin-left: 0.5rem;
        font-size: 0.875rem;
        color: #333;
      }
    }
  }
  .divider {
    height: 1px;
    margin: 0 8px;
    background-color: #e5e5e5;
  }
  .version-section {
    padding: 8px;
    .version-item {
      position: relative;
      display: flex;
      align-items: center;
      padding: 8px 12px;
      cursor: pointer;
      border-radius: 4px;
      transition: background-color 0.2s ease;
      &:hover {
        background-color: #f5f5f5;
      }
      &.selected {
        background-color: #f0f7ff;
        .selected-indicator {
          position: absolute;
          right: 12px;
          width: 16px;
          height: 16px;
          background-color: #1890ff;
          border-radius: 50%;
        }
      }
      .avatar {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        margin-right: 8px;
        font-size: 12px;
        font-weight: 500;
        border-radius: 50%;
        &.personal-avatar {
          color: #1890ff;
          background-color: #e6f7ff;
        }
        &.corp-avatar {
          color: #52c41a;
          background-color: #f6ffed;
        }
      }
      .version-name {
        flex: 1;
        margin: 0 0.3rem 0 0.5rem;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 0.875rem;
        color: #333;
        white-space: nowrap;
      }
      &.language-switcher {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 12px;
        .language-label {
          width: auto;
          font-size: 0.875rem;
          color: var(--font-color);
        }
        .language-options {
          display: flex;
          gap: 8px;
          .language-option {
            width: auto;
            padding: 2px 6px;
            font-size: 0.75rem;
            color: var(--font-color);
            cursor: pointer;
            border-radius: 3px;
            transition: all 0.2s ease;
            &:hover {
              background-color: rgb(73 46 209 / 15%);
            }
            &.active {
              font-weight: 500;
              color: #492ed1;
              background-color: rgb(73 46 209 / 20%);
            }
          }
        }
      }
    }
  }
}
</style>
