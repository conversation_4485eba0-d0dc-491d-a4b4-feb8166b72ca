import WpsOffice from '@/components/wps/index.vue'
import { pdfViewer } from '@iterms/pdf-viewer'

export interface ExtractionCantract {
  contractUrl: string
  contractName: string
  isDocFile: boolean
  fileBlob?: Blob
}

// 获取两个组件的实例类型
export type PdfViewerInstance = InstanceType<typeof pdfViewer>
export type WpsOfficeInstance = InstanceType<typeof WpsOffice>
export type DocumentViewerRef = PdfViewerInstance | WpsOfficeInstance

// 创建唯一的注入Key（确保类型安全）
export const contractRefKey: InjectionKey<Ref<DocumentViewerRef>> = Symbol('contractRef')
