<script setup lang="ts">
import { copyToClipboard } from '@/utils'
import FoldText from './FoldText.vue'
import { ContractRisk, ContractRiskItem, ContractRiskLocation } from './Model'
// import mitt from '@/utils/eventsBus'
import { $t } from '@/utils/i18n'

const officeRef = inject('officeRef') as any
const props = defineProps({
  node: {
    type: ContractRiskItem,
    required: true,
  },
  item: {
    type: ContractRisk,
    required: true,
  },
  nodeId: {
    type: Number,
    required: true,
  },
})
const summaryActive = ref(props.node.summaryOptions[0]?.code)
function getContent() {
  const obj = props.node.summaryOptions.find((item) => item?.code === summaryActive.value)
  return obj?.content || ''
}
const miniWidth = ref(true)

const formatContent = (content: string) => {
  // return content.replace(/<add>|<\/add>|<del>|<\/del>/g, (c) => {
  //   if (c === '<add>') {
  //     return '<span class="--review-add-text">'
  //   } else if (c === '<del>') {
  //     return '<span class="--review-del-text">'
  //   } else {
  //     return '</span>'
  //   }
  // })
  content = content.replace(/<del>.*?<\/del>/g, '')
  content = content.replace(/<add>|<\/add>/g, '')
  return content
}
const handleLocation = async (location: ContractRiskLocation) => {
  // mitt.emit('LocationByText', location.originalText)
  let text = location.originalText
  let status: number
  let flag: boolean
  flag = await officeRef.value.locationByText(text)
  if (flag) return

  text = text.replace(/[，；。！]$/g, '')
  flag = await officeRef.value.locationByText(text)
  if (flag) return
  status = 1

  const regList = ['\n']
  let findList: string[] = []

  for (let i = 0; i < regList.length; i++) {
    const reg = new RegExp(regList[i], 'g')
    if (reg.test(text)) {
      findList = text.split(regList[i])
      break
    }
  }

  // findList.unshift(text)
  for (const item of findList) {
    flag = await officeRef.value.locationByText(item)
    if (flag) {
      status = 2
      break
    }
  }
  if (status === 1) {
    ElMessage.warning('未能定位到原文')
  } else {
    ElMessage.warning('定位到部分原文')
  }
}
const handleCopySug = (location: ContractRiskLocation) => {
  let str = location.suggestionText.replace(/<add>|<\/add>/g, '')
  str = str.replace(/<del>[\s\S]*?<\/del>/g, '')
  copyToClipboard(str)
  ElMessage.success($t('复制成功'))
}
const changeSummaryActive = (code: string) => {
  summaryActive.value = code
}

const hasSuggest = (locations: ContractRiskLocation[]) => {
  const l = locations.find((item) => item.isTabActive)
  return !!l?.suggestionText
}
</script>
<template>
  <el-collapse-transition>
    <div v-show="node.isActive" class="risknode-wrap">
      <div
        v-for="(location, lIndex) of node.locations"
        v-show="node.locations.length > 1 ? location.isTabActive : true"
        :key="lIndex"
        class="risk-node-innerCard"
        :class="{ bottom: node.summaryOptions.length }"
      >
        <div class="risk-node-yuanwen" @click="handleLocation(location)">
          <FoldText :unique-id="lIndex" :text="location ? location.originalText : $t('原文无内容')" />
        </div>
        <div v-if="location.suggestionText" class="risk-node-suggest">
          <pre
            class="pre"
            v-html="
              `<span class='pre-txt' style='padding: 3px 6px; color: #492ed1; font-size: 14px; background-color: #492ed11a; border-radius: 4px; margin-right: 4px;'>修改</span>` +
              formatContent(location.suggestionText)
            "
          ></pre>
        </div>
        <div class="risk-node-handle">
          <div class="risk-node-handle-left">
            <span class="risk-node-handle-btn" @click="handleLocation(location)">
              <i class="iconfont icon-is-position dark" />
              {{ !miniWidth ? $t('原文定位') : $t('原文') }}</span
            >
            <template v-if="node.ruleLevel !== 0 && hasSuggest(node.locations)">
              <span class="risk-node-handle-line"></span>
              <span class="risk-node-handle-btn" @click="handleCopySug(location)">
                <i class="iconfont icon-is-copy dark" />
                {{ !miniWidth ? $t('复制建议') : $t('复制') }}</span
              >
            </template>
            <span class="risk-node-handle-line"></span>
            <slot name="feedback" :location="location"></slot>
          </div>
          <slot :location="location"></slot>
        </div>
      </div>
      <!-- 审查依据模块 -->
      <div v-if="node.summaryOptions.length" class="risk-node-summary">
        <!-- <div class="risk-node-summary__header">
          <span class="risk-node-summary__txt" @click="toggleBasis(node)">
            <i class="iconfont icon-is-lujing" :class="{ 'icon-active': node.isBasisActive }"></i>
            审查依据
          </span>
        </div> -->
        <!-- 内容 -->
        <el-collapse-transition>
          <div>
            <div class="risk-node-summary__tab">
              <span
                v-for="(item, summaryIdx) in node.summaryOptions"
                :key="summaryIdx"
                class="risk-node-summary__name"
                :class="{
                  'risk-node-summary__active': item.code === summaryActive,
                  'risk-node-summary__active_bottom': item.code === summaryActive && node.summaryOptions.length > 1,
                }"
                @click="changeSummaryActive(item.code)"
                >{{ item.label }}</span
              >
            </div>
            <div class="risk-node-content__body">
              <pre class="pre">{{ getContent() }}</pre>
            </div>
          </div>
        </el-collapse-transition>
      </div>
    </div>
  </el-collapse-transition>
</template>

<style lang="scss" scoped>
.dark {
  color: var(--is-color-773bef);
}
.risknode-wrap {
  position: relative;
  z-index: 1;
  padding: 0 1rem 1rem;
  font-size: 14px;
  .risk-node-content__body {
    flex: 1;
    width: 100%;
    padding: 0.5rem 0 0;
    word-break: break-all;
    border-radius: 0.25rem;
  }
  .pre {
    padding-right: 0.5rem;
    margin: 0;
    font-size: 0.875rem;
    line-height: 1.375rem;
    color: var(--minor-font);
    white-space: pre-wrap;
  }
  .risk-node-innerCard {
    padding: 0.25rem 0 0.5rem;
  }
  .bottom {
    border-bottom: 1px solid var(--page-header-line);
  }
  .risk-node-yuanwen {
    padding: 0 0 0.75rem;
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
    line-height: 1.375rem;
    color: var(--is-color-7d7b89);
    white-space: pre-wrap;
    cursor: pointer;
  }
  .risk-node-intro {
    display: flex;
    width: 100%;
    padding: 0.5rem 0.75rem;
    margin-bottom: 0.75rem;
    overflow: hidden;
    line-height: 1.25rem;
    background-color: #f5f7fa;
    border-radius: 0.25rem;
  }
  .risk-node-suggest {
    padding: 0.75rem;
    margin-bottom: 1rem;
    background: linear-gradient(90.59deg, #f5f7fa 0.97%, #eef4fd 100.01%);
    border-radius: 0.25rem;
  }
  .risk-node-handle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    color: var(--popper-font);
    &-left {
      display: flex;
      align-items: center;
    }
    &-line {
      width: 1px;
      height: 0.75rem;
      margin: 0 1rem;
      background-color: var(--is-color-d0d1dc);
    }
    &-btn {
      display: flex;
      align-items: center;
      line-height: 1.25rem;
      cursor: pointer;
      i {
        margin-right: 0.25rem;
      }
      &:hover {
        color: var(--is-color-773bef);
      }
    }
  }
  .risk-node-summary {
    margin-top: 0.75rem;
    &__txt {
      display: flex;
      align-items: center;
      color: #221d40;
      cursor: pointer;
      i {
        transform: rotate(-90deg);
      }
    }
    &__header {
      display: flex;
      justify-content: space-between;
      width: 100%;
      .icon-is-lujing {
        display: inline-block;
        margin: 0 0.5rem 0 0.25rem;
        font-size: 6px;
        color: #bdbdbd;
      }
      .icon-active {
        transform: rotateX(0deg);
      }
    }
    &__tab {
      display: flex;
      gap: 0 1.5rem;
      align-items: center;
      padding: 0;
      margin-top: 0.75rem;
      cursor: pointer;
    }
    &__name {
      display: inline-block;
      padding-bottom: 0.25rem;
      color: var(--is-color-7d7b89);
      &:hover {
        color: var(--is-color-773bef);
      }
    }
    &__active {
      font-weight: 500;
      line-height: 1.125rem;
      color: var(--popper-font);
    }
    &__active_bottom {
      border-bottom: 2px solid var(--popper-font);
    }
  }
}
.risk-location-wrap {
  display: flex;
  flex-direction: column;
  width: 100%;
  .risk-location {
    display: flex;
    align-items: center;
    width: 100%;
    padding-top: 0.5rem;
    cursor: pointer;
    &__header {
      display: flex;
      align-items: center;
      width: 4.1875rem;
      padding-right: 0.25rem;
      margin-left: -0.5rem;
      white-space: nowrap;
    }
    .risk-node-content__location {
      width: calc(100% - 4.1875rem);
      .row-location {
        display: flex;
        width: 100%;
        white-space: nowrap;
      }
    }
    &:hover {
      color: #276ef9;
    }
  }
  .risk-location-active {
    color: #276ef9;
  }
  .select-local {
    color: #276ef9;
  }
}
.opera-box {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 0.75rem;
  font-size: 0.875rem;
  line-height: 1.125rem;
  color: #929292;
  .opera {
    display: inline-block;
    margin: 0;
    margin-left: 1.125rem;
    cursor: pointer;
    .text {
      margin-left: 4px;
    }
  }
}
.unrefer-icon {
  transform: rotateX(180deg);
}
.legal-basis-box {
  .legal-basic-header {
    display: flex;
    flex: 1;
    align-items: center;
    cursor: pointer;
    .icon-arrow-active {
      transform: rotateX(-180deg);
    }
  }
  .legal-basic-content {
    padding: 0.75rem 0;
  }
}
.risk-node-content__header__isEdit {
  font-weight: bold;
  color: #262626;
}
.risk-node-content__body__isEdit {
  margin-bottom: 0.5rem;
  color: #262626 !important;
}
</style>

<style>
.--review-add-text {
  color: var(--is-color-e6555e);
}
.--review-del-text {
  color: rgb(73 46 209 / 60%);
  text-decoration: line-through;
}
</style>
