/**
 * 国际化翻译排除配置文件
 * 用于配置哪些文件或目录不需要进行翻译处理
 */

export const excludeConfig = {
  // 文件名模式匹配（支持通配符）
  filePatterns: [
    // 第三方SDK文件
    '**/web-office-sdk-solution-*.js',
    '**/geetestSdk.js',

    // 其他第三方库文件
    '**/vendor/**',
    '**/lib/**',
    '**/libs/**',

    // 构建和依赖目录
    '**/node_modules/**',
    '**/dist/**',
    '**/build/**',

    // 类型定义文件
    '**/*.d.ts',

    // 测试文件（可选）
    // '**/*.test.js',
    // '**/*.spec.js',

    // 配置文件（可选）
    // '**/config/**',
    // '**/*.config.js',
  ],

  // 精确文件名匹配
  exactFileNames: [
    'geetestSdk.js',
    'web-office-sdk-solution-v2.0.7.es.js',
    'i18n-converter.js',
    'RiskNode.vue',
    'useContractReviewService.ts',
    // 可以添加更多需要排除的具体文件名
  ],

  // 目录名匹配
  directoryNames: [
    'node_modules',
    'dist',
    'build',
    'vendor',
    'lib',
    'libs',
    'assets', // 静态资源目录通常不需要翻译
    // 可以添加更多需要排除的目录名
  ],

  // 文件扩展名排除（这些文件类型通常不包含需要翻译的文本）
  excludeExtensions: [
    '.min.js', // 压缩的JS文件
    '.bundle.js', // 打包文件
    '.map', // source map文件
    '.json', // JSON文件（除了语言文件）
    '.css', // CSS文件
    '.scss', // SCSS文件
    '.less', // LESS文件
    '.png', // 图片文件
    '.jpg',
    '.jpeg',
    '.gif',
    '.svg',
    '.ico',
    '.woff', // 字体文件
    '.woff2',
    '.ttf',
    '.eot',
  ],

  // 自定义排除函数
  customExclude: (filePath) => {
    // 可以在这里添加自定义的排除逻辑
    // 返回 true 表示排除该文件，false 表示不排除

    // 示例：排除所有以 'temp' 开头的文件
    // if (path.basename(filePath).startsWith('temp')) {
    //   return true
    // }

    // 示例：排除包含特定关键词的文件
    // if (filePath.includes('legacy') || filePath.includes('deprecated')) {
    //   return true
    // }

    return false
  },
}

/**
 * 检查文件是否应该被排除
 * @param {string} filePath 文件路径
 * @returns {boolean} 是否应该排除
 */
export function shouldExcludeFile(filePath) {
  const fileName = path.basename(filePath)
  const dirName = path.dirname(filePath)
  const fileExt = path.extname(filePath)

  // 检查文件扩展名
  if (excludeConfig.excludeExtensions.includes(fileExt)) {
    return true
  }

  // 检查精确文件名
  if (excludeConfig.exactFileNames.includes(fileName)) {
    return true
  }

  // 检查目录名
  for (const dir of excludeConfig.directoryNames) {
    if (dirName.includes(dir) || filePath.includes(`/${dir}/`) || filePath.includes(`\\${dir}\\`)) {
      return true
    }
  }

  // 检查文件模式（这里简化处理，实际项目中可以使用 minimatch 库）
  for (const pattern of excludeConfig.filePatterns) {
    const simplePattern = pattern.replace(/\*\*/g, '').replace(/\*/g, '')
    if (filePath.includes(simplePattern)) {
      return true
    }
  }

  // 执行自定义排除函数
  if (excludeConfig.customExclude(filePath)) {
    return true
  }

  return false
}

// 导入 path 模块
import path from 'path'

export default excludeConfig
