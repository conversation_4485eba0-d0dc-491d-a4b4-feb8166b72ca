import { $t } from '@/utils/i18n'

export interface IContractTableData {
  contractId: string
  contractName: string
  contractStatus: string
  createTime: string
}

export interface IContractSearchParams {
  contractName: string
  dateRange: string[]
  status: string
}

/**
 * 审查立场
 */
export class ReviewRulePosition {
  ruleCode: string = ''
  rulePosition: string = ''
  constructor(ruleCode: string, rulePosition: string) {
    this.ruleCode = ruleCode
    this.rulePosition = rulePosition
  }
}
/**
 * 审查清单
 */
export class ReviewRuleList {
  typeName: string = ''
  itermList: ReviewRulePosition[] = []
  constructor() { }
}

export class ContractInfo {
  contractName: string = ''
  isDocFile: boolean = false
  constractId: string = ''
  isReviewed: boolean = false
  recordId: string = ''
  originalFileCode: string = ''
  ruleCode: string = ''

  constructor(data?: any) {
    if (data) {
      this.contractName = data.contractName
      this.originalFileCode = data.originalFileCode

      this.isDocFile = true
      this.isReviewed = true
    }
  }
  setOriginalFileCode(originalFileCode: string) {
    this.originalFileCode = originalFileCode
    return this
  }
  setRuleCode(ruleCode: string) {
    this.ruleCode = ruleCode
    return this
  }

  setConstractId(constractId: string) {
    this.constractId = constractId
    return this
  }
  setRecordId(recordId: string) {
    this.recordId = recordId
    return this
  }
  setIsReviewed(isReviewed: boolean) {
    this.isReviewed = isReviewed
    return this
  }
  setIsDoc(isDoc: boolean) {
    this.isDocFile = isDoc
    return this
  }
  setContractName(name: string) {
    this.contractName = name
    return this
  }
}

/**
 * 审查规则
 */
export class ReviewRuleType {
  id: string = ''
  ruleTypeName: string = ''
  child: ReviewRuleTypeItem[] = []
}
export class ReviewRuleTypeItem {
  id: string = ''
  parentId: string = ''
  ruleTypeName: string = ' '
  orderNum: number = 0
  isSelected: boolean = false
  allRuleTypeName: string = ''
  constructor(id?: string, ruleTypeName?: string) {
    this.id = id || ''
    this.ruleTypeName = ruleTypeName || ''
  }
}

/**
 * AI 审查立场
 */
export class AiReviewPosition {
  contractType: string = ''
  participants: ContractParticipant[] = []
  constructor({ contractType, participants }: AiReviewPosition) {
    this.contractType = contractType || ''
    if (Array.isArray(participants)) {
      participants.forEach((item) => {
        this.participants.push(new ContractParticipant(item.participantName, item.reviewPosition))
      })
    }
  }
}

/**
 * AI 审查立场 合同参与者
 */
export class ContractParticipant {
  participantName: string = ''
  reviewPosition: string = ''
  purposes: string[] = [] //审查目的
  errs: string[] = []
  constructor(participantName: string, reviewPosition: string) {
    this.participantName = participantName
    this.reviewPosition = reviewPosition
  }
}
export interface IContractRecord {
  contractContent?: string
  contractName: string
  originalFileCode: string
  recordId: string
  reviewRecordProgress: string
}
/**
 * 合同状态
 */
export enum EContractStatus {
  NEW = 0,
  REVIEW = 1,
  RESULT = 2,
}

export enum EBusEvents {
  CONSTRACT_INFO = 'EVTS_CONSTRACT_INFO',
}

export class ContractRisk {
  isActive: boolean = false
  ruleTypeName: string = ''
  orderNum: number = 0
  progress: number = 0
  llmReviewRecordId: string = ''
  ruleItemList: ContractRiskItem[] = []
  addRuleItem(item: ContractRiskItem) {
    this.ruleItemList.push(item)
  }
  copy(): ContractRisk {
    const copy = new ContractRisk()
    Object.assign(copy, this)
    return copy
  }
}
interface ISummaryOpts {
  label: string
  code: string
  content: string
  sort: number
}

export class ContractRiskItem {
  id: string = ''
  isActive: boolean = true
  ruleName: string = ''
  ruleWarn: string = ''
  ruleRisk: string = ''
  hasRisk: boolean = false
  ruleLevel: number = 0
  font: { color: string; bgColor: string; name: string } = { color: '', bgColor: '', name: '' }
  lawAccording: string = ''
  originalTextList: string[] = []
  relationText: string[] = []
  ruleSuggestionList: string[] = []
  locations: ContractRiskLocation[] = []
  ruleReference: string = ''
  summaryOptions: ISummaryOpts[] = []
  constructor(riskItems?: ContractRiskItem) {
    Object.assign(this, riskItems)
    // console.log(this.hasRisk, this.ruleLevel)
    this.ruleLevel = this.hasRisk ? this.ruleLevel : 4
    if (Array.isArray(this.originalTextList)) {
      if (this.originalTextList.length === 0) {
        const location = new ContractRiskLocation()
        location.id = '1'
        location.originalText = ''
        location.canSearch = false
        this.locations = [location]
      } else {
        this.locations = this.originalTextList.map((text, index) => {
          const location = new ContractRiskLocation()
          location.id = index.toString()
          location.originalText = text === $t('无') ? '' : text

          location.relationText = this.relationText && this.relationText.length ? this.relationText[index] : ''
          location.suggestionText = this.hasRisk
            ? this.ruleSuggestionList && this.ruleSuggestionList.length
              ? this.ruleSuggestionList[index]
              : ''
            : ''
          location.canSearch = !!location.originalText
          return location
        })
      }
      if (this.locations.length) {
        this.locations[0].isTabActive = true
        this.locations[0].isActive = true
      }
    }
    if (this.ruleReference) {
      this.summaryOptions.push({ label: $t('参考条款'), code: 'ruleReference', content: this.ruleReference, sort: 2 })
    }
    if (this.ruleRisk) {
      this.summaryOptions.push({ label: $t('风险说明'), code: 'ruleRisk', content: this.ruleRisk, sort: 1 })
    }
    if (this.lawAccording) {
      this.summaryOptions.push({ label: $t('法律依据'), code: 'lawAccording', content: this.lawAccording, sort: 3 })
    }
  }
  setFont(font?: any) {
    this.font = font
  }
}

export class ContractRiskLocation {
  id: string = ''
  isActive: boolean = false
  originalText: string = ''
  relationText: string = ''
  suggestionText: string = ''
  isTabActive: boolean = false
  revisonId: number = 0
  canSearch: boolean = false
}
export enum ChatStatusEnum {
  SENDABLE = 'send',
  DISABLE = 'disable',
  STOPABLE = 'stop',
}

export enum SmartChatTypeEnum {
  QUESTION = 'q',
  ANSWER = 'a',
}
export class SmartChat {
  id = ''
  type: SmartChatTypeEnum = SmartChatTypeEnum.ANSWER
  content = ''
  roundId = ''
  isDefault = false
  isLoading = true
  constructor(content?: string, type?: SmartChatTypeEnum) {
    this.id = '' + new Date().getTime() + Math.floor(Math.random() * 100)
    if (type) {
      this.type = type
    }
    if (content) {
      this.content = content
    }
  }
}
export interface QueryParamInter {
  question: string
  chatSessionId: string
  lastTimeChatRoundId?: string
  typeCode?: string
  referenceContent?: string
}

export class RuleTypeList {
  id: string = ''
  orderNum: number = 0
  parentId: string = ''
  allRuleTypeName?: string
  ruleTypeName: string = ''
  ruleItem: RuleTypeItem = new RuleTypeItem()
  child: RuleTypeList[] = []
  isError: boolean = false
  isEdit: boolean = false
  isFold: boolean = false
  isLoading: boolean = false
}
export class RuleTypeItem {
  infoExtractPrompts: string = ''
  lawAccording?: string
  reviewBasis?: string
  riskJudgePrompts: string = ''
  ruleLevel: number = 0
  ruleName: string = ''
  ruleReference?: string
  ruleRisk?: string
  ruleSuggestion?: string
  ruleTypeId?: string
  ruleWarn?: string
  isEdit: boolean = false
}
export interface IContractRule {
  contractType: string
  reviewPosition: string
  contractName: string
  fileUrl: string
  fileCode: string
}
