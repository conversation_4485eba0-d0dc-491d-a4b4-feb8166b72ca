<template>
  <div v-if="!content"></div>
  <div v-else v-html="processedContent" @click="bindEvt"></div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import MarkdownIt from 'markdown-it'

const markDown = new MarkdownIt({
  html: false,
  breaks: true,
  linkify: true,
  typographer: false,
})
markDown.renderer.rules.link_open = (tokens: any, idx: any, options: any, _evn: any, self: any) => {
  return self.renderToken(tokens, idx, options)
}

interface Props {
  content: string
  onHandlerClick?: (id: string) => void
  onCallback?: (data: { editText: string; cleanText: string }) => void
}

const props = defineProps<Props>()

const bindEvt = (event: MouseEvent) => {
  if (!event.target) return
  const target = event.target as HTMLElement
  if (target.tagName === 'SPAN' && target.classList.contains('quote-marks')) {
    event.preventDefault()
    props.onHandlerClick && props.onHandlerClick(target.id)
  } else if (target.tagName == 'A') {
    const anchorTarget = target as HTMLAnchorElement
    if (!target.classList.contains('quote-href')) {
      event.preventDefault()
      window.open(anchorTarget.href)
    } else {
      event.preventDefault()
      props.onHandlerClick && props.onHandlerClick(target.id)
    }
  }
}

const processedContent = computed(() => {
  if (!props.content) {
    return ''
  }

  const text = markDown.render(props.content)
  const thinkRegex = /&lt;think(?:\s+.*?)?&gt;[\s\S]*?&lt;\/think&gt;/g
  const outerRegex = /\^([^]*?)\^/g
  const tempText = text.replace(thinkRegex, '')

  const result = tempText.replace(outerRegex, (_match: string, contentInsideCaret: string): string => {
    const innerResult = contentInsideCaret.replace(/\[([^\]]+)\]/g, (_: string, inside: string): string => {
      const strs: string[] = inside.split('/')
      if (strs.length == 3) {
        return `${strs[2]}<span id="${inside}" class="quote-marks"></span>`
      }
      return `<span id="${inside}" class="quote-marks"></span>`
    })
    return `${innerResult}`
  })

  console.log('result', result)
  const cleanText = result.replace(/<[^>]*>/g, '').replace(/\^(\[[^\]]+\])+\^/g, '')
  props.onCallback && props.onCallback({ editText: result, cleanText })

  return result
})
</script>
