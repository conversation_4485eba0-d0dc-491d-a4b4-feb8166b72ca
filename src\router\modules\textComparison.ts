import Layout from '@/layout/index.vue'
import type { RouteRecordRaw } from 'vue-router'
import { $t } from '@/utils/i18n'

const router: RouteRecordRaw[] = [
  {
    path: '/createComparison',
    component: Layout,
    children: [
      {
        path: '',
        name: 'CreateComparison',
        meta: {
          permission: 'ContractComparison',
        },
        component: () => import(/* webpackChunkName: "CreateComparison" */ '@/views/textComparison/create/index.vue'),
      },
    ],
  },
  {
    path: '/comparisonList',
    component: Layout,
    children: [
      {
        path: '',
        name: 'ComparisonList',
        component: () => import(/* webpackChunkName: "ComparisonList" */ '@/views/textComparison/list/index.vue'),
        meta: {
          fullscreen: true,
          single: true,
          title: $t('比对列表'),
          permission: 'ContractComparison',
        },
      },
    ],
  },
  {
    path: '/comparison',
    children: [
      {
        path: '',
        name: 'Comparison',
        component: () => import(/* webpackChunkName: "Comparison" */ '@/views/textComparison/comparison/index.vue'),
      },
    ],
  },
  {
    path: '/semantics',
    children: [
      {
        path: '',
        name: 'Semantics',
        component: () => import(/* webpackChunkName: "Semantics" */ '@/views/textComparison/semantics/index.vue'),
      },
    ],
  },
]
export default router
