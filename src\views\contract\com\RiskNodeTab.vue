<script lang="ts" setup>
import { ContractRisk, ContractRiskItem, ContractRiskLocation } from './Model'

const props = defineProps({
  node: {
    type: ContractRiskItem,
    required: true,
  },
  locations: {
    type: Array<ContractRiskLocation>,
    required: true,
  },
})

const emits = defineEmits(['update:locations', 'search-position'])

const locations = computed({
  get: () => props.locations,
  set: (val: Array<ContractRiskLocation>) => {
    emits('update:locations', val)
  },
})

const toggleNodeTab = (location: ContractRiskLocation) => {
  locations.value.forEach((item) => {
    item.isTabActive = false
  })
  location.isActive = true
  location.isTabActive = true
  emits('search-position', location.originalText)
}
</script>
<template>
  <el-collapse-transition>
    <div v-show="node.isActive" class="risk-node-tab">
      <div
        v-for="(location, i) of locations"
        :key="location.id"
        class="risk-node-tab-item"
        :class="{
          active: location.isTabActive,
          dothidden: location.isActive,
        }"
        @click="toggleNodeTab(location)"
      >
        <span>{{ node.ruleLevel == 4 ? $t('定位') : $t('风险') }}</span> {{ i + 1 }}
      </div>
    </div>
  </el-collapse-transition>
</template>

<style scoped lang="scss">
.risk-node {
  &-tab {
    display: flex;
    flex-wrap: wrap;
    gap: 13px 7px;
    align-items: center;
    justify-content: flex-start;
    padding: 6px 16px;
    margin-bottom: 6px;

    // 默认状态
    &-item {
      position: relative;
      width: 78px;
      height: 28px;
      font-size: 14px;
      font-weight: 400;
      line-height: 28px;
      color: #221d40;
      text-align: center;
      cursor: pointer;
      border: 1px solid var(--page-header-line);
      border-radius: 2px;
      &:hover {
        color: var(--is-color-773bef);
        border-color: var(--is-color-773bef);
      }

      // 初始默认状态 - 右上角红点
      &::before {
        position: absolute;
        top: -3px;
        right: -3px;
        width: 6px;
        height: 6px;

        // transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
        content: '';
        background-color: var(--is-color-e6555e);
        border-radius: 50%;
      }

      // 无风险状态 三角形
      &::after {
        position: absolute;
        top: -1px;
        right: -1px;
        width: 9px;
        height: 9px;
        content: '';
        background-color: var(--is-color-1b9275);
        border-top-right-radius: 2px;
        clip-path: polygon(100% 0, 100% 100%, 0 0);
        transition:
          opacity 0.3s ease-in-out,
          visibility 0.3s ease-in-out;
      }
    }
    .dothidden::before {
      visibility: hidden;
      opacity: 0;
    }
    .modified::after {
      visibility: hidden;
      opacity: 0;
    }

    // 选中状态
    .active {
      color: var(--is-color-773bef);
      background-color: var(--primary-btn-bg);
    }
  }
}
</style>
