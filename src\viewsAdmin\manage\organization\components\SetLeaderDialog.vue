<template>
  <el-dialog
    :title="dialogTitle"
    v-model="visible"
    :close-on-click-modal="false"
    width="480px"
    :show-close="true"
    @close="closeDialog"
  >
    <el-form
      ref="ruleForm"
      :model="form"
      :label-position="'right'"
      label-width="60px"
      class="demo-ruleForm custom-dialog-form"
      @submit.native.prevent
    >
      <el-form-item :label="$t('部门')">
        <el-input :value="curOrgInfo.orgName" readonly></el-input>
      </el-form-item>
      <el-form-item :label="$t('负责人')">
        <el-select v-model="form.userCode" style="width: 100%" filterable>
          <el-option
            v-for="item in userSelects"
            :key="item.userCode"
            :label="item.realName"
            :value="item.userCode"
          ></el-option>
        </el-select>
        <div v-if="disableFlag" class="error">{{ $t('该组织下未添加人员信息') }}</div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click.stop="closeDialog">{{ $t('取消') }}</el-button>
        <el-button type="primary" :disabled="btnDisabled" :loading="loading" @click.stop="submit">{{ $t('确定') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import { setLeaderReq, getUserByOrgId } from '@/services/manage/organization'

import { $t } from '@/utils/i18n'
interface UserSelect {
  userCode: string
  realName: string
}

interface OrgInfo {
  orgId: string
  orgName: string
  leaderUserCode?: string
}

interface FormData {
  userCode: string
}

const emit = defineEmits<{
  succ: []
}>()

const dialogTitle = ref($t('设置部门负责人'))
const visible = ref(false)
const userSelects = ref<UserSelect[]>([])
const curOrgInfo = ref<OrgInfo>({} as OrgInfo)
const form = reactive<FormData>({
  userCode: '',
})
const loading = ref(false)
const disableFlag = ref(false)
const ruleForm = ref<FormInstance>()

const btnDisabled = computed(() => {
  return !form.userCode
})

const closeDialog = () => {
  userSelects.value = []
  form.userCode = ''
  disableFlag.value = false
  visible.value = false
}

const openDialog = (orgInfo: OrgInfo) => {
  curOrgInfo.value = orgInfo
  // if (orgInfo.leaderUserCode) form.userCode = orgInfo.leaderUserCode
  getUserList()
  visible.value = true
}

const getUserList = async () => {
  const params = {
    data: {
      orgId: Number(curOrgInfo.value.orgId),
      subFlag: true,
    },
    page: 1,
    pageSize: 999,
  }
  const { data = {} } = await getUserByOrgId(params)

  const result = (data as any)?.result || []
  if (result.length) {
    userSelects.value = result.map((val: any) => {
      return {
        userCode: val.userCode,
        realName: val.realName,
      }
    })
  } else {
    userSelects.value = []
  }

  if (!userSelects.value.length) disableFlag.value = true
}

const submit = () => {
  if (loading.value || !form.userCode) return false

  loading.value = true
  confirmUpdate()
}

const confirmUpdate = async () => {
  const params = {
    orgId: Number(curOrgInfo.value.orgId),
    userCode: form.userCode,
  }

  const { code } = await setLeaderReq(params).finally(() => {
    loading.value = false
  })

  if (String(code) === '200') {
    emit('succ')
    ElMessage.success($t('操作成功'))
    closeDialog()
  }
}

defineExpose({
  openDialog,
})
</script>
<style lang="scss" scoped>
.custom-dialog-form {
  :deep(.el-form-item__label) {
    font-size: 12px;
    font-weight: normal;
    color: #2a2b2c;
  }
}
.error {
  font-size: 12px;
  color: red;
}
</style>
