<script lang="ts" setup>
import OverTooltip from '@/components/TextOverTip.vue'
import TaskLoadingBox from './TaskLoading.vue'
import OptionBox from './RuleOption.vue'
import { useContractStore } from '@/stores'
import { getAllRuleList, saveReviewRule } from '@/services/contract'
import { type IContractRule, RuleTypeList } from './Model'

import { $t } from '@/utils/i18n'
const emit = defineEmits(['success'])
const contractStore = useContractStore()
const visible = ref(false)
const isTaskloading = ref(true)
const reviewLoading = ref(false)
const objData = ref<IContractRule>({
  contractName: '',
  reviewPosition: '',
  contractType: '',
  fileUrl: '',
  fileCode: '',
})
const close = () => {
  visible.value = false
}
const dataTree = ref<RuleTypeList[]>([])

const queryAllRuleList = async (id: string) => {
  const { data } = await getAllRuleList(id)
  const { ruleTypeList } = data as { ruleTypeList: RuleTypeList[] }
  if (Array.isArray(ruleTypeList)) {
    dataTree.value = ruleTypeList.map((item) => {
      item.isError = false
      item.isEdit = false
      return item
    })
    if (dataTree.value.length > 0) {
      currentRuleType.value = dataTree.value[0]
    }
  }
  taskLoadingRef.value?.stop()
  isTaskloading.value = false
}
const currentRuleType = ref<RuleTypeList>()

const handleCreate = () => {
  const rule = new RuleTypeList()
  rule.isEdit = true
  dataTree.value.push(rule)
}

const activeIndex = ref(0)
const handleSelect = (rule: RuleTypeList, idx: number) => {
  activeIndex.value = idx
  currentRuleType.value = rule
}
const handleEdit = (rule: RuleTypeList) => {
  const other = dataTree.value.filter((item) => item.ruleTypeName && item.ruleTypeName !== rule.ruleTypeName)

  if (other.length > 0) {
    other.forEach((item) => {
      item.isEdit = false
    })
  }
  rule.isEdit = true
}
const handleRemove = (idx: number) => {
  ElMessageBox.confirm($t('确认删除审查类型?'), $t('提示'), {
    confirmButtonText: $t('确认'),
    cancelButtonText: $t('取消'),
    customClass: 'cus-message-box',
    showClose: false,
  }).then(() => {
    dataTree.value.splice(idx, 1)

    currentRuleType.value = dataTree.value[0]
  })
}
const handleEnter = (item: any) => {
  if (!item.label) {
    item.isError = true
    return
  }
  item.isEdit = false
}
const handleFocus = (item: any) => {
  item.isError = false
}
const handleBlur = (rule: RuleTypeList) => {
  const other = dataTree.value.filter((item) => item.ruleTypeName && item.ruleTypeName !== rule.ruleTypeName)
  if (other.length > 0) {
    other.forEach((item) => {
      item.isError = false
    })
  }
  if (rule.ruleTypeName.trim() === '') {
    dataTree.value.pop()
    return
  }
  if (currentRuleType.value?.ruleTypeName === rule.ruleTypeName) {
    rule.isEdit = false
    return
  }
  const list = dataTree.value.filter((item) => item.ruleTypeName && item.ruleTypeName === rule.ruleTypeName)
  if (list.length > 1) {
    rule.isError = true
    return ElMessage.error($t('审查类型不能重复'))
  } else {
    rule.isEdit = false
  }
}
interface ICommitRuleParams {
  ruleListId: string
  ruleTypeList: RuleTypeList[]
}
const checkValid = () => {
  let count = 0
  dataTree.value.forEach((item) => {
    if (!item.ruleTypeName) {
      count++
    }
    item.child.forEach((child) => {
      if (
        child.ruleTypeName.trim() === '' ||
        child.ruleItem.infoExtractPrompts.trim() === '' ||
        child.ruleItem.riskJudgePrompts.trim() === '' ||
        child.isError
      ) {
        count++
      }
    })
  })
  return count === 0
}
const handleSubmit = async () => {
  if (!checkValid()) {
    ElMessageBox.confirm(
      '审查清单中存在重名或信息不完整的审查点，请确认是否继续发起审查，发起后系统将会过滤掉重名或信息不完成的审查点?',
      $t('提示'),
      {
        confirmButtonText: $t('确认'),
        cancelButtonText: $t('取消'),
        customClass: 'cus-message-box',
        showClose: false,
      },
    ).then(async () => {
      save(true)
    })
    return
  } else {
    save(false)
  }
}
const save = async (filter: boolean = false) => {
  let tempData: any[] = []
  if (filter) {
    tempData = dataTree.value.filter((item) => {
      item.child = item.child.filter((child) => {
        return (
          child.ruleItem.ruleName.trim() !== '' &&
          child.ruleItem.riskJudgePrompts.trim() !== '' &&
          child.ruleItem.riskJudgePrompts.trim() !== '' &&
          !child.isError
        )
      })
      return item.child.length > 0
    })
  } else {
    tempData = dataTree.value
  }
  tempData = tempData.map((item) => {
    return {
      typeName: item.ruleTypeName,
      ruleItermList: item.child.map((node: RuleTypeList) => {
        return {
          ruleWarn: node.ruleTypeName,
          ruleName: node.ruleItem.ruleName,
          infoExtractPrompts: node.ruleItem.infoExtractPrompts,
          riskJudgePrompts: node.ruleItem.riskJudgePrompts,
        }
      }),
    }
  })
  const params: ICommitRuleParams = {
    ruleListId: ruleListId.value,
    ruleTypeList: tempData,
  }
  const { code, data, message } = await saveReviewRule(params)
  if (code === '200' || code === '000000') {
    const { ruleCode } = data as { ruleCode: string }
    contractStore.contractInfo.setRuleCode(ruleCode)
    emit('success', ruleCode)
  } else {
    ElMessage.error(message)
  }
}

const update = (item: RuleTypeList[]) => {
  if (currentRuleType.value) {
    currentRuleType.value.child = item
  }
}
const taskLoadingRef = ref()
const ruleListId = ref('')
const show = (data: IContractRule, id: string) => {
  objData.value = data
  visible.value = true
  setTimeout(async () => {
    isTaskloading.value = true
    taskLoadingRef.value.start()
  }, 100)
}
defineExpose({
  show,
  query(id: string, ruleTypeList: RuleTypeList) {
    ruleListId.value = id
    // queryAllRuleList(id)
    if (Array.isArray(ruleTypeList)) {
      dataTree.value = ruleTypeList.map((item) => {
        item.isError = false
        item.isEdit = false
        return item
      })
      if (dataTree.value.length > 0) {
        currentRuleType.value = dataTree.value[0]
      }
    }
    taskLoadingRef.value?.stop()
    isTaskloading.value = false
  },
})

onMounted(() => {
  // queryAllRuleList(ruleListId.value)
})
</script>
<template>
  <el-dialog
    v-model="visible"
    header-class="task"
    body-class="task"
    top="5%"
    width="80vw"
    style="min-width: 62.5rem"
    :append-to-body="true"
    :modal-append-to-body="false"
    :close-on-click-modal="false"
    @close="close()"
  >
    <template #header>
      <div class="dialog-title-wrap">
        <div v-if="isTaskloading">
          <div class="dialog-title">{{ $t('审查清单生成中') }}</div>
          <div class="dialog-sub-title">
            {{ $t('预计1-2分钟，请耐心等待')
            }}<span><i class="loading-dot">.</i><i class="loading-dot">.</i><i class="loading-dot">.</i></span>
          </div>
        </div>
        <div v-else>
          <span class="dialog-title" style="display: flex; flex-direction: column"
            >{{ objData.contractName && objData.contractName.split('.').slice(0, -1).join('.') }}审查清单</span
          >
          <span class="dialog-sub-title">{{ $t('审查立场：') }}{{ objData.reviewPosition }}</span>
        </div>
      </div>
    </template>
    <TaskLoadingBox ref="taskLoadingRef" class="mask-loading" :hide-header="true" />
    <div v-if="reviewLoading" v-loading="true" class="loading-wrap"></div>
    <div class="content">
      <div class="left-tree">
        <div class="left-top">
          <span class="left-top-text">{{ $t('审查类型') }}</span>
          <div class="btn-icon left-top-btn" @click="handleCreate">
            <i class="iconfont icon-is-plus" style="font-size: 12px"></i>
            <span>{{ $t('添加') }}</span>
          </div>
        </div>
        <div class="tree-wrap">
          <div v-for="(item, idx) of dataTree" :key="'item' + idx" class="tree-node">
            <div
              v-if="!item.isEdit"
              class="tree-node-top"
              :class="{ 'is-active': idx === activeIndex }"
              @click="handleSelect(item, idx)"
            >
              <span>{{ idx + 1 }}.</span>
              <div class="tree-node-label" @dblclick="handleEdit(item)">
                <OverTooltip style="width: 100%" :content="item.ruleTypeName" />
              </div>
              <div class="tree-node-icon">
                <i class="iconfont icon-is-fankui" style="margin-right: 0.25rem" @click="handleEdit(item)"></i>
                <i class="iconfont icon-is-shanchu" @click="handleRemove(idx)"></i>
              </div>
            </div>
            <div v-else class="tree-node-input">
              <el-input
                ref="refInput"
                v-model="item.ruleTypeName"
                :class="{ 'input-error': item.isError }"
                :placeholder="$t('请输入审查类别')"
                @keyup.enter.capture="handleEnter(item)"
                @focus="handleFocus(item)"
                @blur="handleBlur(item)"
              ></el-input>
            </div>
          </div>
        </div>
      </div>
      <OptionBox
        v-if="currentRuleType"
        :option-data="currentRuleType.child"
        :contract-rule="objData"
        @update="update"
      />
    </div>
    <template v-slot:footer>
      <div class="footer-wrap">
        <div>
          <el-button @click="close()">{{ $t('取消') }}</el-button>
          <el-button type="primary" @click="handleSubmit">{{ $t('发起审查') }}</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>
<style lang="scss" scoped>
.rulehandle-wrap {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  margin-bottom: -32px;
}
.dialog-title-wrap {
  display: flex;
  flex-direction: column;
  .dialog-title {
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
  }
  .dialog-sub-title {
    font-size: 12px;
    line-height: 20px;
  }
}
.mask-loading {
  position: absolute;
  top: 69px;
  left: 0;
  z-index: 1;
  width: 100%;
  height: calc(100% - 69px);
  padding: 16px !important;
  background-color: #fff;
}
.loading-wrap {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  background-color: #fff;
  opacity: 0.7;
}
.content {
  display: flex;
  height: 100%;
}
.left-tree {
  display: flex;
  flex-direction: column;
  width: 260px;
  height: 100%;
  border-right: 1px solid #e9e9e9;
  .left-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 56px;
    padding: 12px 6px 12px 16px;
    &-text {
      font-size: 16px;
      font-weight: bold;
      color: #221d39;
    }
    &-btn {
      font-size: 12px;
      color: #492ed1;
      .el-icon-plus {
        margin-right: 4px;
      }
    }
  }
  .tree-wrap {
    flex: 1;
    height: 0;
    overflow-y: auto;
  }
}
.btn-icon {
  display: flex;
  justify-content: center;
  font-size: 16px;
  color: #bdbdbd;
  cursor: pointer;
}
.cursor {
  cursor: pointer;
}
.tree-node {
  position: relative;
  flex: 1;
  width: 100%;
  &-top {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 40px;
    padding: 12px 4px 12px 16px;
    &:hover {
      background-color: #f1f5ff;
    }
    &:not(.is-active) {
      cursor: pointer;
    }
    :deep(.el-dropdown) {
      position: absolute;
      right: 4px;
    }
    .tree-node-label {
      display: flex;
      flex: 1;
      align-items: center;
      width: 0;
      height: 100%;
      padding: 0 0 0 12px;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    &:hover .tree-node-icon {
      visibility: initial;
    }
    .tree-node-icon {
      visibility: hidden;
      font-size: 12px;
      cursor: pointer;
      .icon-is-fankui {
        margin-right: 8px;
      }
      i {
        &:hover {
          color: #492ed1;
        }
      }
    }
  }
  .tree-node-input {
    padding: 0 12px;
    margin-bottom: 8px;
    .input-error {
      :deep(.el-input__inner) {
        border-color: #f56c6c;
      }
    }
  }
  .is-active {
    background-color: #f1f5ff;
  }
}
.loading-dot {
  animation: loading 0.9s infinite ease-in-out;
}
.loading-dot:nth-child(1) {
  animation-delay: 0.3s;
}
.loading-dot:nth-child(2) {
  animation-delay: 0.6s;
}
.loading-dot:nth-child(3) {
  animation-delay: 0.9s;
}

@keyframes loading {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
</style>
