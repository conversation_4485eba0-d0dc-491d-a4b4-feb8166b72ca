<template>
  <div class="message-container">
    <div class="message-container-body">
      <div
        v-if="chatMessage.isDeepSeek === 1 && ![ChatStatusEnum.STOP, ChatStatusEnum.ERROR].includes(status)"
        class="thought-box"
      >
        <div class="thought-header">
          <div class="thought-header-left">
            <i class="iconfont" style="margin-right: 0.5rem; color: #000">&#xe66f;</i>
            <div class="status-text">{{ CHAT_STATUS_MAP[status] }}</div>
          </div>
          <div
            class="thought-header-right"
            @click="
              () => {
                setThoughtVisiable()
              }
            "
          >
            <i v-if="chatMessage.showThoughtProcessBar" class="iconfont" style="font-size: 1rem; color: rgb(34 29 57)"
              >&#xe66e;</i
            >
            <i v-else class="iconfont" style="font-size: 1rem; color: rgb(34 29 57)">&#xe675;</i>
          </div>
        </div>
        <div v-if="chatMessage.showThoughtProcessBar">
          <div v-if="deepThinkData.text" style="margin-top: 16px">
            <div class="thought-infos">
              {{ deepThinkData.text }}
            </div>
          </div>
        </div>
      </div>
      <div
        v-if="
          [
            ChatStatusEnum.PREPARE,
            ChatStatusEnum.PROGRESS,
            ChatStatusEnum.FILE_CONTENT_EXTRACTOR,
            ChatStatusEnum.LAW_SEARCHING,
            ChatStatusEnum.CASE_SEARCHING,
            ChatStatusEnum.WEB_SEARCHING,
            ChatStatusEnum.ARTICLE_SEARCHING,
          ].includes(status)
        "
        class="loading-icon-container"
        style="display: flex; align-items: center"
      >
        <div style="display: flex; align-items: center">
          <i class="iconfont loading-icon" style="font-size: 1.5rem; color: #000">&#xe641;</i>
        </div>
      </div>
      <div v-if="content && chatMessage.messageStatus !== 0" class="content">
        <OutputContent :content="content" @callback="getContent" @handler-click="handlerClick" />
      </div>
      <div v-if="[ChatStatusEnum.STOP, ChatStatusEnum.ERROR].includes(status)" style="display: flex; margin-top: 1rem">
        <div v-if="status === ChatStatusEnum.STOP">{{ $t('(已终止生成)') }}</div>
        <div v-if="status === ChatStatusEnum.ERROR">{{ $t('系统繁忙，暂时无法为您解答') }}</div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed } from 'vue'
import { OutputContent } from './content.ts'
import { ChatStatusEnum, CHAT_STATUS_MAP, ChatMessage } from '@/stores/modules/chat/helper'
const emits = defineEmits(['focus-detail', 'get-content', 'thought-visiable'])

const props = defineProps({
  chatMessage: {
    type: ChatMessage,
    required: true,
  },
})

const setThoughtVisiable = () => {
  emits('thought-visiable', !props.chatMessage.showThoughtProcessBar)
}

const status = computed(() => {
  return props.chatMessage.status
})
const content = computed(() => {
  return props.chatMessage.chatContent
})

const deepThinkData = computed(() => {
  return props.chatMessage.deepThinkData
})

const getContent = (content: any) => {
  emits('get-content', content)
}
const handlerClick = (number: string) => {
  emits('focus-detail', number)
}
</script>
<style lang="scss" scoped>
.message-container-body {
  .thought-box {
    display: block;
    height: auto;
    .thought-header {
      display: flex;
      align-items: center;
      width: fit-content;
      padding: 0.75rem;
      background: rgb(247 248 251 / 100%);
      border-radius: 0.5rem;
      border-top-left-radius: 0;
      .thought-header-left {
        display: flex;
        flex: 1 1 0;
        align-items: center;
      }
      .thought-header-right {
        display: flex;
        align-items: center;
        margin-left: 0.5rem;
        cursor: pointer;
      }
      .status-text {
        font-size: 0.875rem;
        line-height: 1.375rem;
        color: var(--font-color);
      }
    }
    .thought-infos {
      padding-left: 0.5rem;
      font-size: 0.875rem;
      font-weight: 400;
      line-height: 1.375rem;
      color: var(--sidebar-line);
      letter-spacing: 0.0625rem;
      border-left: 3px solid var(--page-header-line);
    }
    .margin-4 {
      align-items: center;
      margin-right: 0.5rem;
    }
  }
  .loading-icon-container {
    margin-top: 0.5rem;
    svg {
      animation: rotate 1s linear infinite;
    }
    .path {
      stroke-linecap: round;
      animation: dash 1.5s ease-in-out infinite;
    }
    .loading-icon {
      animation: rotate 1s linear infinite;
    }
  }

  @keyframes rotate {
    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes dash {
    0% {
      stroke-dasharray: 1, 150;
      stroke-dashoffset: 0;
    }
    50% {
      stroke-dasharray: 90, 150;
      stroke-dashoffset: -35;
    }
    100% {
      stroke-dasharray: 90, 150;
      stroke-dashoffset: -124;
    }
  }
  .content {
    color: var(--iterms-text-title-color);
  }
}
</style>
