import { post, get } from '@/services'
import type { IResponse } from '@/services'

export enum modeEnum {
  EDIT = 'edit',
  VIEW = 'view',
}
interface IParams {
  fileCode: string
  fileName: string
  mode: modeEnum
  notSaveStatus: string
}

export type IOnlineOfficeConfig = Record<string, any>
// 数据字典
export function queryOnlineOfficeConfig<T>(data: IParams): Promise<IResponse<T>> {
  return post<T>(`/file-online/select-config`, data)
}

export function queryWpsAppId<T>(): Promise<IResponse<T>> {
  return get<T>(`/config/get`)
}
