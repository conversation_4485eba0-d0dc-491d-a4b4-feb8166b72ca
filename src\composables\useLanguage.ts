import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'

import { $t } from '@/utils/i18n'
// 支持的语言类型
export type SupportedLocale = 'zh_CN' | 'en_US' | 'zh_HK'

// 语言选项配置
export interface LanguageOption {
  value: SupportedLocale
  label: string
  shortLabel: string
}

// 语言选项列表
export const languageOptions: LanguageOption[] = [
  {
    value: 'zh_CN',
    label: $t('简体中文'),
    shortLabel: $t('简')
  },
  {
    value: 'en_US', 
    label: 'English',
    shortLabel: 'EN'
  },
  {
    value: 'zh_HK',
    label: $t('繁體中文'),
    shortLabel: $t('繁')
  }
]

// 本地存储的key
const LANGUAGE_STORAGE_KEY = 'app-language'

// 当前语言状态
const currentLanguage = ref<SupportedLocale>('zh_CN')

/**
 * 语言切换composable
 */
export function useLanguage() {
  const { locale } = useI18n()

  // 初始化语言设置
  const initLanguage = () => {
    // 从本地存储获取语言设置
    const savedLanguage = localStorage.getItem(LANGUAGE_STORAGE_KEY) as SupportedLocale
    if (savedLanguage && languageOptions.some(option => option.value === savedLanguage)) {
      currentLanguage.value = savedLanguage
      locale.value = savedLanguage
    } else {
      // 默认使用简体中文
      currentLanguage.value = 'zh_CN'
      locale.value = 'zh_CN'
    }
  }

  // 切换语言
  const switchLanguage = (lang: SupportedLocale) => {
    currentLanguage.value = lang
    locale.value = lang
    // 保存到本地存储
    localStorage.setItem(LANGUAGE_STORAGE_KEY, lang)
    
    // 可以在这里添加其他需要在语言切换时执行的逻辑
    console.log(`Language switched to: ${lang}`)
  }

  // 获取当前语言信息
  const getCurrentLanguageInfo = computed(() => {
    return languageOptions.find(option => option.value === currentLanguage.value) || languageOptions[0]
  })

  // 获取下一个语言（用于循环切换）
  const getNextLanguage = () => {
    const currentIndex = languageOptions.findIndex(option => option.value === currentLanguage.value)
    const nextIndex = (currentIndex + 1) % languageOptions.length
    return languageOptions[nextIndex].value
  }

  // 循环切换语言
  const toggleLanguage = () => {
    const nextLang = getNextLanguage()
    switchLanguage(nextLang)
  }

  return {
    currentLanguage: computed(() => currentLanguage.value),
    languageOptions,
    getCurrentLanguageInfo,
    switchLanguage,
    toggleLanguage,
    initLanguage
  }
}
