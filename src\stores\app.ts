import { defineStore } from 'pinia'
import router from '@/router'
import type { Canceler } from 'axios'

import { $t } from '@/utils/i18n'
interface IUserInfo {
  id: number
  name: string
}

interface IAxiosCancelToken {
  cancel: Canceler
  url: string
}

export const useAppStore = defineStore(
  'appStore',
  () => {
    const token = ref('')
    const userInfo = ref<IUserInfo | null>(null)

    function setToken(tokenVal: string) {
      token.value = tokenVal
    }
    function setUserInfo(userInfoVal: IUserInfo) {
      userInfo.value = userInfoVal
    }

    function logout() {
      token.value = ''
      userInfo.value = null
      router.push('/login')
    }

    const axiosCancelTokens = ref<IAxiosCancelToken[]>([])
    function addAxiosCancelToken(cancelToken: IAxiosCancelToken) {
      axiosCancelTokens.value.push(cancelToken)
    }
    function removeAxiosCancelToken(url: string) {
      const tokens = axiosCancelTokens.value.filter((token) => token.url === url)
      tokens.forEach((token) => {
        token.cancel($t('主动取消请求'))
      })
    }
    function clearAxiosCancelTokens() {
      axiosCancelTokens.value.forEach((token) => {
        token.cancel($t('主动取消请求'))
      })
      axiosCancelTokens.value = []
    }

    const isLogin = computed(() => !!token.value)

    return {
      isLogin,
      token,
      userInfo,
      setToken,
      setUserInfo,
      logout,
      axiosCancelTokens,
      addAxiosCancelToken,
      removeAxiosCancelToken,
      clearAxiosCancelTokens,
    }
  },
  // {
  //   persist: {
  //     key: 'appStore',
  //     storage: sessionStorage,
  //     pick: ['userInfo', 'token'], // Persist only userInfo and token
  //   },
  // },
)
