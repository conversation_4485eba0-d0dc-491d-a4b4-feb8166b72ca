import { EBusEvents } from './Model'
import MarkdownIt from 'markdown-it'

import { fetch } from '@/services/sse'
import { useContractStore } from '@/stores'
import { formatMD } from '@/utils'
import mitt from '@/utils/eventsBus'
import { throttle } from 'lodash-es'
import { generateContratSummary } from '@/services/contract'
import { $t } from '@/utils/i18n'
export function useSummaryService() {
  const contractStore = useContractStore()
  const summary = ref('')
  let summaryTextList: string[] = []
  let isStartSSE = true

  const isLoading = ref(true)
  const markdown = new MarkdownIt({
    html: true,
    breaks: true,
    linkify: true,
    typographer: true,
  })
  let controller: AbortController

  const throttleFun = throttle(async () => {
    if (answerRef.value) {
      setTimeout(() => {
        answerRef.value.scrollTo(0, answerRef.value.wrapRef.scrollHeight)
      }, 10)
    }
  }, 100)
  function generateSummary() {
    if (contractStore.contractInfo.constractId == '') {
      return
    }
    isStartSSE = true
    controller = new AbortController()
    chatScrollFlag.value = true

    fetch(
      '/llm/contract-summary/query-contract-summary-sse',
      // `/llm/contract-summary/generate-sse`,
      {
        fileCode: contractStore.contractInfo.originalFileCode,
        contractName: contractStore.contractInfo.contractName,
      },
      {
        async onopen(response) {
          if (response.ok) {
            // if (!recordId) {
            isLoading.value = true
            speedMessage()
            // }
          } else if (response.status >= 400 && response.status < 500 && response.status !== 429) {
            if (response.status == 401) {
              ElMessage.error($t('您的会话已过期，请重新登录'))
              //need relaod
            } else {
              ElMessage.error($t('服务错误'))
            }
            handleErrorSSE()
          } else {
            ElMessage.error($t('服务错误'))
            handleErrorSSE()
          }
        },
        onmessage(msg) {
          if (msg.event === '') {
            const result = JSON.parse(msg.data)
            if (result.errorMsg) {
              throw new Error(result.errorMsg)
            } else {
              if (!result.answer) {
                return
              }
              if (result.status == 0) {
                summaryTextList.push(...result.answer)
              } else if (result.status == 1) {
                cancelAnimationFrame(requestId)
                summary.value += result.answer
                // throttleFun()
              } else {
                summary.value += $t('合同摘要生成失败，请稍后再试')
              }

              // if (result.answer) {
              //   summary.value += result.answer
              //   throttleFun()
              // }
            }
          } else if (msg.event === 'close') {
            controller.abort()
            isLoading.value = false
            isStartSSE = false
          }
        },
        onerror(err) {
          console.log(err)
          isStartSSE = false
          handleErrorSSE()
        },
        onclose() {
          isLoading.value = false
          isStartSSE = false
          controller.abort()
          return new Error()
        },
      },
      controller,
    )
  }

  const createContractSummary = async () => {
    const { code, data } = await generateContratSummary(contractStore.contractInfo.originalFileCode)
    if (code == '000000') {
      generateSummary()
    }
  }
  mitt.on(EBusEvents.CONSTRACT_INFO, (_info: any) => {
    //
    createContractSummary()
    // generateSummary()
  })

  onBeforeUnmount(() => {
    mitt.off(EBusEvents.CONSTRACT_INFO)
  })

  function handleStop() {
    controller?.abort()
  }

  function handleErrorSSE() {
    throw new Error()
  }

  const chatRef = ref()
  const answerRef = ref()
  let requestId: number
  function speedMessage() {
    let idx = 0
    function animationWidth() {
      const val = summaryTextList[idx]
      if (val !== undefined) {
        summary.value += val
        idx++
        throttleFun()
        requestId = requestAnimationFrame(animationWidth)
      } else if (isStartSSE) {
        requestId = requestAnimationFrame(animationWidth)
      } else {
        summaryTextList = []
        isLoading.value = false
      }
    }
    requestId = requestAnimationFrame(animationWidth)
  }

  function exportCons(item: any) {
    const value = formatMD(item)
    return value + (isLoading.value ? "<span class='ai-cursor'></span>" : '')
  }

  function clear() {
    summary.value = ''
    summaryTextList = []
    isStartSSE = false
    isLoading.value = true
    handleStop()
  }

  function getSummary() {
    return summary.value
  }
  const chatScrollFlag = ref(false)
  function handleChatScroll(e: { wheelDelta: number }) {
    if (e.wheelDelta > 8) {
      chatScrollFlag.value = false
    } else {
      const top = answerRef.value.scrollTop
      const sh = answerRef.value.scrollHeight
      const h = answerRef.value.clientHeight
      if (sh - h - top < 16) {
        chatScrollFlag.value = true
      }
    }
  }

  const getSummaryOutput = computed(() => {
    const str = exportCons(summary.value)
    return markdown.render(str)
  })
  return {
    getSummaryOutput,
    summary,
    chatRef,
    answerRef,
    generateSummary,
    exportCons,
    clear,
    isLoading,
    getSummary,
    handleChatScroll,
  }
}
