<template>
  <el-dialog
    v-model="dialogVisible"
    align-center
    :title="$t('切换版本')"
    width="400px"
    body-class="dialog-body-class"
    :close-on-click-modal="false"
    :append-to-body="true"
    @close="closeDialog"
  >
    <!-- <div class="divider"></div> -->
    <div style="padding: 0 12px">
      <div class="section-title">{{ $t('个人版本') }}</div>
      <el-radio-group v-model="selectedPersonalId" @change="onPersonalChange">
        <el-radio v-for="item in personalList" :key="item.id" :label="item.id">{{ item.name }}</el-radio>
      </el-radio-group>
      <div class="section-title" style="margin-top: 20px">{{ $t('企业版本') }}</div>
      <div class="team-list-scroll">
        <el-radio-group v-model="selectedCorpId" @change="onCorpChange">
          <el-radio v-for="item in corpList" :key="item.id" :label="item.id">
            {{ item.corp.corpName || '-' }}
          </el-radio>
        </el-radio-group>
      </div>
    </div>
    <!-- <div class="divider" style="margin-top: 20px; margin-bottom: 0"></div> -->
    <template #footer>
      <div>
        <el-button class="cancel-button" @click="closeDialog">{{ $t('取消') }}</el-button>
        <el-button type="primary" class="confirm-button" @click="onConfirm" :loading="confirmLoading">
          {{ $t('确定') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { ref, watch, computed, nextTick } from 'vue'
import { useUserStore, useChatStore } from '@/stores'
import { RESPONSE_CODE_SUCCESS, MEMBER_ID_KEY } from '@/constants'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { switchCorp } from '@/services/user'
import type { IChatsListResponse, IChatItem } from '@/types/chat'
import { ChatItem } from '@/stores/modules/chat/helper'
import mitt from '@/utils/eventsBus'

import ChatApi from '@/services/chat'
import { $t } from '@/utils/i18n'
const userStore = useUserStore()
const chatStore = useChatStore()
const router = useRouter()
const corpList = computed(() => userStore.userInfo?.corpList || [])
const personalList = computed(() => {
  return [
    {
      name: userStore.userInfo?.nickName || '',
      id: userStore.userInfo?.userId || '',
    },
  ]
})

const props = defineProps<{
  visible: boolean
}>()

const emits = defineEmits(['update:visible', 'confirm'])

// 计算属性：控制对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emits('update:visible', value),
})
const selectedCorpId = ref('')
const selectedPersonalId = ref('')
const confirmLoading = ref(false)

const onPersonalChange = (value: any) => {
  if (value) {
    selectedCorpId.value = ''
  }
}

const onCorpChange = (value: any) => {
  if (value) {
    selectedPersonalId.value = ''
  }
}

const initMemberId = () => {
  const cachedMemberId = localStorage.getItem(MEMBER_ID_KEY)
  if (!cachedMemberId) {
    selectedPersonalId.value = userStore.userInfo?.userId || ''
    selectedCorpId.value = ''
  } else {
    selectedCorpId.value = cachedMemberId
    selectedPersonalId.value = ''
  }
}

watch(
  () => props.visible,
  (val) => {
    if (val) {
      initMemberId()
    }
  },
)

const closeDialog = () => {
  emits('update:visible', false)
}

const onConfirm = async () => {
  if (confirmLoading.value) return

  confirmLoading.value = true
  try {
    const memberId = selectedCorpId.value
    const res = await switchCorp(memberId)
    if (res.code === RESPONSE_CODE_SUCCESS) {
      localStorage.setItem(MEMBER_ID_KEY, memberId)
      emits('confirm', memberId)
      closeDialog()

      // 先获取用户信息，再调用聊天列表，最后跳转到合同页面
      try {
        await userStore.fetchCurrentUserInfo()

        // 调用 ChatApi.getChatsList
        try {
          const {
            code,
            data: { list },
          } = await ChatApi.getChatsList<IChatsListResponse>({ currentPageNo: 1, pageSize: 15 })
          if (RESPONSE_CODE_SUCCESS == code && list) {
            const tempList: ChatItem[] = []
            list.forEach((it: IChatItem) => {
              tempList.push(ChatItem.create(it))
            })
            chatStore.setHistoryChatList(tempList)
          }
        } catch (chatError) {
          console.warn('获取聊天列表失败:', chatError)
        }

        gotoContract()
      } catch (userInfoError) {
        console.warn('获取用户信息失败，但切换成功:', userInfoError)

        gotoContract()
      }
    } else {
      ElMessage.error(res.message || $t('切换失败'))
    }
  } catch (error) {
    // 检查是否是请求被取消的错误
    if (error && error.toString().includes('cancel')) {
      console.warn('切换请求被取消:', error)
      ElMessage.warning($t('操作被取消，请重试'))
    } else {
      ElMessage.error(error || $t('切换失败，请稍后重试'))
    }
  } finally {
    confirmLoading.value = false
  }
}

const gotoContract = async () => {
  ElMessage.success($t('切换成功'))
  await nextTick()
  router.push('/contract').then(() => {
    mitt.emit('refresh-contract-list')
  })
}
</script>
<style scoped lang="scss">
.divider {
  margin: 0 0 16px;
  border-top: 1px solid #e9e9e9;
}
.section-title {
  margin-bottom: 8px;
  font-size: 1rem;
  font-weight: 500;
  color: var(--font-color);
  text-align: left;
}
.team-list-scroll {
  max-height: 120px;
  padding-right: 4px;
  margin-bottom: 0;
  overflow-y: auto;
}
.el-radio-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-start;
  margin-left: 0;
}
.el-radio {
  margin-bottom: 0;
  font-size: 0.875rem;
  color: var(--font-color);
  text-align: left;
}
.cancel-button {
  margin-right: 8px;
}
</style>
